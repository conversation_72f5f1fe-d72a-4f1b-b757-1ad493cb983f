CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `TSA_Tenant_Auto_Enrichment_For_Already_Grouped_Tenants`()
BEGIN
	drop temporary table if exists tempTS;
	create temporary table tempTS(
		select count(*) as confirmations,BranchID from Empirical_DataStage.Tenants_Stage where BranchID IN (
			SELECT DISTINCT CompanyID FROM Empirical_DataStage.QLDCTsWithCompanyForGroupedTenantsFromTSA)
		group by BranchID
	);
    
    
	drop temporary table if exists tempCompanyCT;
	create temporary table tempCompanyCT(
		select * from Empirical_DataStage.QLDCTsWithCompanyForGroupedTenantsFromTSA
			where CompanyID in (select BranchID from tempTS where confirmations>=2) #and ConfirmedTenantID=1249858
	);
    
    select min(ConfirmedTenantID) into @minConfirmedtenantID from tempCompanyCT;
    
    while @minConfirmedTenantID is not null do
		call Empirical_Prod.TSA_Tenant_Auto_Enrichment(@minConfirmedTenantID,10,22);
        -- select @minConfirmedTenantID;
        select min(ConfirmedTenantID) into @minConfirmedtenantID from tempCompanyCT where ConfirmedTenantID>@minConfirmedtenantID;
        
	end while;

END