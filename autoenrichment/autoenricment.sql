select * from Empirical_Tenants.ConfirmedTenants where ConfirmedTenantID=1281501;


select * from Empirical_Prod.Company where ApplicationID=10;

# COM
drop temporary table if exists comProperties;
create temporary table comProperties(
select ParentID from Empirical_Prod.Address where ZipCode in (3000, 3002, 3003, 3004, 3006, 3008, 3031, 3051, 3052, 3053, 3054)
 and ParentTableID=1 and IsActive=1 and Sequence=1
 );
 
drop temporary table if exists comPropertiesTSRecords;
create temporary table comPropertiesTSRecords(
select * from Empirical_DataStage.Tenants_Stage where PropertyID in 
(select * from comProperties) and PropertyID is not null
);

drop temporary table if exists comPropertiesCTs;
create temporary table comPropertiesCTs(
select  PropertyID, ConfirmedTenantID from Empirical_Prod.SuiteTenant 
where PropertyID in (select * from comProperties) and IsActive=1
);
drop temporary table if exists comPropertiesCTsRecords;
create temporary table comPropertiesCTsRecords(
select ctp.PropertyID, ct.CompanyID,ct.ConfirmedTenantID,ct.TenantName from Empirical_Tenants.ConfirmedTenants as ct
inner join comPropertiesCTs as ctp on ctp.ConfirmedTenantID =ct.ConfirmedTenantID
#where ct.ProviderID=1
);
drop temporary table if exists comPropertiesCTsRecordsWithCompany;
create temporary table comPropertiesCTsRecordsWithCompany(
select ct.*,c.IsHidden,c.ModifiedDate as CModifiedDate,c.ModifiedBy as CModifiedBy from comPropertiesCTsRecords ct 
inner join Empirical_Prod.Company c on ct.CompanyID=c.CompanyID where c.IsHidden=0 or c.IsHidden is null
);




# QLD
drop temporary table if exists QLDProperties;
create temporary table QLDProperties(
select ParentID from Empirical_Prod.Address where StateID=58
 and ParentTableID=1 and IsActive=1 and Sequence=1
 );
 
drop temporary table if exists QLDPropertiesTSRecords;
create temporary table QLDPropertiesTSRecords(
select * from Empirical_DataStage.Tenants_Stage where PropertyID in 
(select * from QLDProperties) and PropertyID is not null
);

drop temporary table if exists QLDPropertiesCTs;
create temporary table QLDPropertiesCTs(
select  PropertyID, ConfirmedTenantID from Empirical_Prod.SuiteTenant 
where PropertyID in (select * from QLDProperties) and IsActive=1
);
drop temporary table if exists QLDPropertiesCTsRecords;
create temporary table QLDPropertiesCTsRecords(
select ctp.PropertyID, ct.CompanyID,ct.ConfirmedTenantID,ct.TenantName, ct.IsActive from Empirical_Tenants.ConfirmedTenants as ct
inner join QLDPropertiesCTs as ctp on ctp.ConfirmedTenantID =ct.ConfirmedTenantID
where ct.ProviderID=1
);

drop temporary table if exists QLDPropertiesCTsRecordsWithCompany;
create temporary table QLDPropertiesCTsRecordsWithCompany(
select ct.*,c.IsHidden,c.ModifiedDate as CModifiedDate,c.ModifiedBy as CModifiedBy from QLDPropertiesCTsRecords ct 
inner join Empirical_Prod.Company c on ct.CompanyID=c.CompanyID where c.IsHidden=0 or c.IsHidden is null
);

select count(*), IsActive from QLDPropertiesCTsRecordsWithCompany group by IsActive;

drop temporary table if exists ChangedCompaniesFromMay1st;
create temporary table ChangedCompaniesFromMay1st(
select * from Empirical_Prod.ChangeLogCompany where ChangedDate>"2024-05-01 00:00:00" order by CompanyChangeLogID Desc
);

Create table Empirical_DataStage.QLDCTsWithCompanyForGroupedTenantsFromTSA as
SELECT 
    *
FROM
    QLDPropertiesCTsRecordsWithCompany
WHERE
    (CompanyID IN (SELECT DISTINCT
            CompanyID
        FROM
            ChangedCompaniesFromMay1st
        WHERE
            ApplicationID IS NULL
                OR ApplicationID = 10)
	OR
    CompanyID IN (select DISTINCT CompanyID from Empirical_Prod.Company where ApplicationID=10))
        AND IsActive = 1;

drop temporary table if exists tempQLDCT;
create temporary table tempQLDCT(
SELECT 
    *
FROM
    QLDPropertiesCTsRecordsWithCompany
WHERE
    (CompanyID IN (SELECT DISTINCT
            CompanyID
        FROM
            ChangedCompaniesFromMay1st
        WHERE
            ApplicationID IS NULL
                OR ApplicationID = 10)
	OR
    CompanyID IN (select DISTINCT CompanyID from Empirical_Prod.Company where ApplicationID=10))
        AND IsActive = 1);

drop temporary table if exists tempTS;
create temporary table tempTS(
select count(*) as confirmations,BranchID from Empirical_DataStage.Tenants_Stage where BranchID IN (SELECT DISTINCT
            CompanyID
        FROM
            tempQLDCT)
    group by BranchID
);

select count(*) from tempTS where confirmations>=2;
select count(*) from tempTS where confirmations=1;

select * from tempQLDCT where CompanyID in (select BranchID from tempTS where confirmations>=2);