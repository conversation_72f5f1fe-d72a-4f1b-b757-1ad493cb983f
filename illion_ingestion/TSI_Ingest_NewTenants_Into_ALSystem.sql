CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `TSI_Ingest_NewTenants_Into_ALSystem`(IN P_BatchID INT, IN P_Limit int)
BEGIN
    -- Step 1: Create a temporary table to store raw Illion tenant data
    DROP TEMPORARY TABLE IF EXISTS temp_Raw_Illion_Tenants;
    
    CREATE TEMPORARY TABLE temp_Raw_Illion_Tenants AS (
        SELECT * 
        FROM Empirical_DataIngestion.Illion_New_2024_09_01
        WHERE ProviderID = 6
        AND BatchID = P_BatchID
        AND (IsProcessed IS NULL OR IsProcessed = 0) limit P_Limit
    );
    
    -- Step 2: Create an index on the 'VendorID' column in the temp table for faster lookup
    CREATE INDEX idx_VendorID_Illion ON temp_Raw_Illion_Tenants (VendorID);
    -- select * from temp_Raw_Illion_Tenants;
    -- Step 3: Insert selected columns into Tenants_Stage table
    INSERT INTO Empirical_DataStage.Tenants_Stage (
        VendorID,
        TenantName,
        Address1,
        Address2,
        City,
        State,
        StateAbbr,
        CountryCode,
        PostalCode,
        NationalID,
        OfficePhone,
        Fax,
        CEOName,
        CEOTitle,
        LineOfBusiness,
        SICCode,
        Revenue,
        EmployeesAtLocation,
        EmployeeCount,
        LegalStatus,
        SubsidiaryCode,
        PropertyID,
        Latitude,
        Longitude,
        BatchID,
        ProviderID,
        Email,
        WebsiteURL,
        ASICEntityStatus,
        ASICEntityType,
        ASICEntityClass,
        ABNStatus,
        ABN_StatusFromDate,
        GST_Status,
        GST_StatusFromDate,
        RegistrationOrIncorporationDate,
        EntityAge,
        EmployeeIndicator,
        RevenueIndicator,
        HQ_ID,
        HQ_CompanyName,
        NumberofMembersinHierarchy,
        ImmediateParentDUNS,
        ImmediateParentName,
        ImmediateParentCountry,
        DomesticParentDUNS,
        DomesticParentName,
        DomesticParentCountry,
        GlobalUltimateParentDUNS,
        GlobalUltimateParentName,
        GlobalUltimateParentCountry,
        PrimarySICDesc,
        PrimarySIC3Digit,
        PrimarySIC3DigitDesc,
        PrimarySIC2Digit,
        PrimarySIC2DigitDesc,
        PrimarySICDivision,
        PrimarySICDivisionDesc,
        ANZSICCode,
        CreatedDate,
        MarketableFlag
    )
    SELECT 
        VendorID,
        TenantName,
        Address1,
        Address2,
        City,
        State,
        StateAbbr,
        CountryCode,
        PostalCode,
        NationalID,
        OfficePhone,
        Fax,
        CEOName,
        CEOTitle,
        LineOfBusiness,
        SICCode,
        Revenue,
        EmployeesAtLocation,
        EmployeeCount,
        LegalStatus,
        SubsidiaryCode,
        PropertyID,
        Latitude,
        Longitude,
        BatchID,
        ProviderID,
        Email,
        WebsiteURL,
        ASICEntityStatus,
        ASICEntityType,
        ASICEntityClass,
        ABNStatus,
        ABN_StatusFromDate,
        GST_Status,
        GST_StatusFromDate,
        RegistrationOrIncorporationDate,
        EntityAge,
        EmployeeIndicator,
        RevenueIndicator,
        HQ_ID,
        HQ_CompanyName,
        NumberofMembersinHierarchy,
        ImmediateParentDUNS,
        ImmediateParentName,
        ImmediateParentCountry,
        DomesticParentDUNS,
        DomesticParentName,
        DomesticParentCountry,
        GlobalUltimateParentDUNS,
        GlobalUltimateParentName,
        GlobalUltimateParentCountry,
        PrimarySICDesc,
        PrimarySIC3Digit,
        PrimarySIC3DigitDesc,
        PrimarySIC2Digit,
        PrimarySIC2DigitDesc,
        PrimarySICDivision,
        PrimarySICDivisionDesc,
        ANZSICCode,
        NOW() AS CreatedDate,
        MarketableFlag
    FROM temp_Raw_Illion_Tenants;
   select "Insert";
    -- Step 4: Update the Tenant_Stage_ID and set IsProcessed flag in the Illion data
    UPDATE Empirical_DataIngestion.Illion_New_2024_09_01 a
    INNER JOIN Empirical_DataStage.Tenants_Stage b 
        ON a.VendorID = b.VendorID 
        AND b.ProviderID = 6 
        AND a.BatchID = P_BatchID AND b.BatchID = P_BatchID 
    SET 
        a.Tenant_Stage_Id = b.Tenant_Stage_Id,
        a.IsProcessed = 1;
	-- WHERE  a.ID IN (SELECT ID FROM temp_Raw_Illion_Tenants);
    select "Insert";
	set @cnt=null;
    select count(*) into @cnt from Empirical_DataIngestion.Illion_New_2024_09_01 where BatchID = P_BatchID and IsProcessed = 1 and Tenant_Stage_ID is not null;
        select count(*) into @cnt from Empirical_DataIngestion.Illion_New_2024_09_01 where BatchID = P_BatchID and IsProcessed = 1 and Tenant_Stage_ID is not null;
	SELECT CONCAT("Inserted",@cnt," new illion tenant records into Tenant Stage is done succesfully ");
    -- Step 5: Insert ABN and ACN for tenant stage records into NationalIdentifiers table
    INSERT INTO Empirical_Prod.NationalIdentifiers 
        (CompanyID, Tenant_Stage_ID, ConfirmedTenantID, ABN, ACN)
    SELECT 
        NULL, 
        Tenant_Stage_ID, 
        NULL, 
        ABN, 
        ACN 
    FROM 
        Empirical_DataIngestion.Illion_New_2024_09_01 
    WHERE 
        ID IN (SELECT ID FROM temp_Raw_Illion_Tenants)
        AND NationalID IS NOT NULL;
	set @cnt=null;
	select count(*) into @cnt from Empirical_DataIngestion.Illion_New_2024_09_01 where BatchID = P_BatchID and IsProcessed = 1 and Tenant_Stage_ID is not null  AND NationalID IS NOT NULL;
    -- Final output message
    SELECT  CONCAT("",@cnt," Ingestion of ABN and ACN into the NationalIdentifiers table for new tenants is completed.");

    -- Step 6: Drop the temporary table
    DROP TEMPORARY TABLE IF EXISTS temp_Raw_Illion_Tenants;

END
