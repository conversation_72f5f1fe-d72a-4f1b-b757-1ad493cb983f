drop temporary table if exists temp_grouped_Tenants_confirmations_to_be_deleted;
create temporary table temp_grouped_Tenants_confirmations_to_be_deleted(
    select Tenant_Stage_Id from Empirical_DataStage.Tenants_Stage 
    where Vendor<PERSON> in (select Vendor<PERSON> from Empirical_DataIngestion.Deleted_Tenants_2024_11_05) and BranchID is not null
);

select * from Empirical_DataStage.Tenants_Stage 
where Tenant_Stage_Id in (select * from temp_grouped_Tenants_confirmations_to_be_deleted) and BranchID is not null;

update Empirical_DataStage.Tenants_Stage 
set 
    BranchID=null,
    ParentCompanyID=null,
    ConfirmedTenantID=null,
    HidedBy = 22,
    HidedDate = now(),
    HideReasonID = 10,
    HideReasonComments = 'Hiding old illion tenants which are not in september illion file',
    IsHidden=1,
    ModifiedBy=22,
    ModifiedDate=now()
where Tenant_Stage_Id in (select * from temp_grouped_Tenants_confirmations_to_be_deleted) and BranchID is not null;

drop temporary table if exists temp_Tier3_Tenants_to_be_deleted;
create temporary table temp_Tier3_Tenants_to_be_deleted(
    select Tenant_Stage_Id from Empirical_DataStage.Tenants_Stage 
    where Vend<PERSON><PERSON> in (select VendorID from Empirical_DataIngestion.Deleted_Tenants_2024_11_05) and BranchID is null
);

select * from Empirical_DataStage.Tenants_Stage 
where Tenant_Stage_Id in (select * from temp_Tier3_Tenants_to_be_deleted) and BranchID is null;

update Empirical_DataStage.Tenants_Stage 
set 
    HidedBy = 22,
    HidedDate = now(),
    HideReasonID = 10,
    HideReasonComments = 'Hiding old illion tenants which are not in september illion file',
    IsHidden=1,
    ModifiedBy=22,
    ModifiedDate=now()
where Tenant_Stage_Id in (select * from temp_Tier3_Tenants_to_be_deleted) and BranchID is null;

