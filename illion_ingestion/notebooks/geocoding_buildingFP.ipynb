#%%
import pandas as pd
pd.set_option("display.max_columns",None)
pd.set_option("display.max_rows",None)
import geopandas as gpd
from shapely import Point
from sqlalchemy import create_engine
import yaml
from shapely import wkt
#%% md
## Tenants
#%%
df = pd.read_parquet("/home/<USER>/Desktop/arealytics/arealytics-notebooks/LAT_LONG")
#%%
df.shape
#%%
df.columns
#%%
df['StreetNoMax'].fillna(df['StreetNoMin'], inplace=True)
#%%
df['geometry'] = df.apply(lambda row: Point(row['Longitude'], row['Latitude']), axis=1)
gdf = gpd.GeoDataFrame(df, geometry='geometry', crs='EPSG:4326')
#%%
gdf.shape
#%% md
## Building_footPrints
#%%
source_db_config = {
    'username': 'imperium_admin',
    'password': 'CoVayKfSgNgq6n8HJU4O',
    'state_ID': '61',
    'host': 'localhost',
    'port': '3392',
    'database_name': 'Empirical_DataIngestion',
    'table_name': 'SA_Master_Parcel_Area_Geom_Data_2024_09_09'
}
#%%
# Connection String
source_connection_string = f"mysql+mysqlconnector://{source_db_config['username']}:{source_db_config['password']}@{source_db_config['host']}:{source_db_config['port']}/{source_db_config['database_name']}"
source_database_engine = create_engine(source_connection_string)
#%%
sql_query = '''
SELECT
    ps.PropertyID,
    buildingFootPrints.AL_Building_ID,
    p.PropertyName,
    ps.ResearchTypeName AS ResearchStatus,
    ps.UseTypeName AS UseType,
    p.Floors,
    CONCAT(a.AddressText, ' ', a.ZipCode) AS Address,
    c.ConstructionStatusName,
    ST_AsText(buildingFootPrints.Shape) AS geometry,
    s.StateName,
    buildingFootPrints.Strata_Type,
    a.AddressStreetName,
    a.StreetNumberMin,
    a.StreetNumberMax,
    a.ZipCode,
    S1.SuffixName,
    ps.ParcelInfo,
    p.LGA,
    ps.BuildingSizeSF,
    country.CountryName,
    city.CityName,l.Latitude,l.Longitude
FROM
    Empirical_Prod.Property p
    INNER JOIN Empirical_Prod.PropertySummary ps ON p.PropertyID = ps.PropertyID
    INNER JOIN Empirical_Prod.ConstructionStatus c ON p.ConstructionStatusID = c.ConstructionStatusID
    INNER JOIN Empirical_Prod.Address a ON a.ParentID = p.PropertyID and a.ParentTableID=1 and a.IsActive=1 and a.Sequence=1
    left join Empirical_Prod.Suffix S1 on S1.SuffixID = a.SuffixID
    LEFT JOIN Empirical_Prod.Location l on l.LocationID=a.LocationID
    left JOIN Empirical_GIS.BuildingFootPrints buildingFootPrints ON buildingFootPrints.CRE_PropertyID = p.PropertyID and buildingFootPrints.IsActive=1
    left JOIN Empirical_Prod.State s ON a.StateID = s.StateID
    left JOIN Empirical_Prod.Country country ON s.CountryID = country.CountryID
    left JOIN Empirical_Prod.City city ON a.CityID = city.CityID
WHERE
    ps.ResearchTypeID not in (1,4) and p.CondoTypeID in (1,3) and a.StateID in (57,58,59); 
''' 
#%%
BFP_df = pd.read_sql(sql_query, con=source_database_engine)
#%%
BFP_df['StreetNumberMax'].fillna(BFP_df['StreetNumberMin'], inplace=True)
#%%
BFP_df.shape
#%%
BFP_df['geometry'] = BFP_df['geometry'].apply(wkt.loads)
BFP_gdf = gpd.GeoDataFrame(BFP_df, geometry='geometry')

BFP_gdf = BFP_gdf.set_crs(epsg=4326)
BFP_gdf.info()
#%%
BFP_gdf['Strata_Type'].value_counts()
#%%
gdf.shape
#%% md
## Intersect BuildingFootPrints with Tenants
#%%
intersected_gdf = gpd.sjoin(BFP_gdf,gdf,predicate='contains')
#%%
intersected_gdf.shape
#%%
intersected_gdf[intersected_gdf['Strata_Type'].isna()].shape
#%%
intersected_gdf['VendorID'].nunique()
#%%
gdf.head()
#%%
intersected_gdf.columns
#%%
intersected_gdf = intersected_gdf.sort_values('VendorID')
#%%
intersected_gdf[intersected_gdf['VendorID'].duplicated(keep=False)].head()
#%%
intersected_gdf = intersected_gdf[~intersected_gdf['VendorID'].duplicated(keep=False)]

#%%
intersected_gdf.head()
#%%
intersected_gdf.to_file("Step_1.geojson")
#%%
intersected_gdf.shape
#%%
intersected_gdf[['MarketableFlag', 'City', 'Address2', 'ASICEntityClass',
       'ASICEntityStatus', 'ASICEntityType', 'DomesticParentCountry',
       'DomesticParentName', 'EmployeeIndicator',
       'GlobalUltimateParentCountry', 'GlobalUltimateParentName',
       'ImmediateParentCountry', 'ImmediateParentName', 'PrimarySIC2DigitDesc',
       'PrimarySIC3DigitDesc', 'PrimarySICDesc', 'PrimarySICDivision',
       'PrimarySICDivisionDesc', 'RevenueIndicator', 'TenantName',
       'StreetName', 'Email', 'GST_Status', 'GST_StatusFromDate', 'ABNStatus',
       'ABN', 'ACN', 'RegistrationOrIncorporationDate', 'ABN_StatusFromDate',
       'SourcedDate', 'Key_Decision_Maker_Middle_Name',
       'Key_Decision_Maker_First_Name', 'Key_Decision_Maker_Last_Name',
       'LegalStatus', 'StreetType', 'Address1', 'CEOName', 'CEOTitle',
       'LineOfBusiness', 'NationalID', 'OfficePhone', 'Fax', 'StreetNoMin',
       'StreetNoMax', 'StreetNo', 'PostalCode', 'Suffix', 'SuffixAbbr',
       'StateAbbr', 'State', 'WebsiteURL', 'CountryCode', 'ProviderID',
       'EmployeesAtLocation', 'StatusCode', 'IsProcessed', 'ConfirmedTenantID',
       'PropertyID_right', 'MatchingScore', 'CreatedDate', 'ModifiedDate',
       'Tenant_Stage_Id', 'ParentCompanyID', 'BranchID', 'BatchID',
       'IsDefault', 'NAICSCode', 'NACECode', 'ModifiedBy', 'IsHidden',
       'IsDeleted', 'HidedBy', 'HidedDate', 'HideReasonID',
       'HideReasonComments', 'SubHideReasonID', 'VendorID',
       'DomesticParentDUNS', 'EntityAge', 'GlobalUltimateParentDUNS',
       'SubsidiaryCode', 'ImmediateParentDUNS', 'NumberofMembersinHierarchy',
       'SICCode', 'PrimarySIC2Digit', 'PrimarySIC3Digit', 'HQ_CompanyName',
       'HQ_ID', 'Latitude_right', 'Longitude_right', 'Revenue', 'ANZSICCode',
       'EmployeeCount', 'Illion_version', 'geometry']].info()
#%%
gdf.shape
#%%
df_1 = gdf[gdf['VendorID'].isin(intersected_gdf['VendorID'])]
#%%
df_1.to_file("Step_1.geojson")
#%% md
## NSW
#%%
NSW_parcels = gpd.read_file('/home/<USER>/Downloads/NSW-20241111T065042Z-001/NSW')
#%%
NSW_parcels.shape
#%%
gdf.head()
#%%
step_2_df = gdf[~gdf['VendorID'].isin(df_1['VendorID'])]
#%%
nsw_df = step_2_df
#%%
NSW_BFP_gdf=BFP_gdf[BFP_gdf['StateName'] == 'New South Wales']
#%%
nsw_df.shape
#%%
NSW_parcels = NSW_parcels.set_crs(epsg=4326)
#%%
NSW_BFP_gdf.head()
#%%
NSW_BFP_gdf.columns
#%%
NSW_BFP_gdf = NSW_BFP_gdf[['PropertyID', 'AL_Building_ID', 'PropertyName', 'ResearchStatus',
       'UseType', 'Floors', 'Address', 'ConstructionStatusName',
       'StateName', 'Strata_Type', 'AddressStreetName', 'StreetNumberMin',
       'StreetNumberMax', 'ZipCode', 'ParcelInfo', 'LGA', 'BuildingSizeSF',
       'CountryName', 'CityName', 'Latitude', 'Longitude','SuffixName']]
#%%
NSW_BFP_gdf['geometry'] = NSW_BFP_gdf.apply(lambda row: Point(row['Longitude'], row['Latitude']), axis=1)
#%%
NSW_BFP_gdf['geometry'] = NSW_BFP_gdf.apply(lambda row: Point(row['Longitude'], row['Latitude']), axis=1)
NSW_BFP_gdf = gpd.GeoDataFrame(NSW_BFP_gdf, geometry='geometry', crs='EPSG:4326')
#%%
parcel_bfp_int = gpd.sjoin(NSW_parcels,NSW_BFP_gdf,predicate='contains')
#%%
parcel_bfp_int.shape
#%%
parcel_bfp_int.head()
#%%
parcel_bfp_int=parcel_bfp_int[['Lot_ID', 'Lot', 'Plan', 'Parcel_No', 'Strata_typ', 'Complex_Na',
       'Lot_Area', 'Strata_Pla', 'geometry', 'PropertyID',
       'AL_Building_ID', 'PropertyName', 'ResearchStatus', 'UseType', 'Floors',
       'Address', 'ConstructionStatusName', 'StateName', 'Strata_Type',
       'AddressStreetName', 'StreetNumberMin', 'StreetNumberMax', 'ZipCode',
       'ParcelInfo', 'LGA', 'BuildingSizeSF', 'CountryName', 'CityName',
       'Latitude', 'Longitude','SuffixName']]
#%%
point_parcel_int = gpd.sjoin(parcel_bfp_int,nsw_df,predicate='contains')
#%%
point_parcel_int[~point_parcel_int.duplicated('VendorID',keep=False)].to_file('NSW_point_parcel_int.geojson')
#%%
nsw_point_parcel_int_op=point_parcel_int[~point_parcel_int.duplicated('VendorID',keep=False)]
#%% md
## QLD
#%%
QLD_parcels = gpd.read_file('/home/<USER>/Downloads/QLD-20241111T073112Z-001/QLD')
#%%
qld_df = step_2_df
#%%
qld_df.shape
#%%
QLD_BFP_gdf=BFP_gdf[BFP_gdf['StateName'] == 'Queensland']
#%%
QLD_BFP_gdf.shape
#%%
QLD_BFP_gdf.columns
#%%
QLD_BFP_gdf = QLD_BFP_gdf[['PropertyID', 'AL_Building_ID', 'PropertyName', 'ResearchStatus',
       'UseType', 'Floors', 'Address', 'ConstructionStatusName', 
       'StateName', 'Strata_Type', 'AddressStreetName', 'StreetNumberMin',
       'StreetNumberMax', 'ZipCode', 'ParcelInfo', 'LGA', 'BuildingSizeSF',
       'CountryName', 'CityName', 'Latitude', 'Longitude','SuffixName']]
#%%
QLD_BFP_gdf['geometry'] = QLD_BFP_gdf.apply(lambda row: Point(row['Longitude'], row['Latitude']), axis=1)
#%%
QLD_BFP_gdf['geometry'] = QLD_BFP_gdf.apply(lambda row: Point(row['Longitude'], row['Latitude']), axis=1)
QLD_BFP_gdf = gpd.GeoDataFrame(QLD_BFP_gdf, geometry='geometry', crs='EPSG:4326')
#%%
qld_parcel_bfp_int = gpd.sjoin(QLD_parcels,QLD_BFP_gdf,predicate='contains')
#%%
qld_parcel_bfp_int.columns
#%%
qld_parcel_bfp_int=qld_parcel_bfp_int[['Lot_ID', 'Lot', 'Plan', 'Parcel_No', 'Strata_Typ', 'Complex_Na',
       'Lot_Area', 'Strata_Pla', 'geometry', 'PropertyID',
       'AL_Building_ID', 'PropertyName', 'ResearchStatus', 'UseType', 'Floors',
       'Address', 'ConstructionStatusName', 'StateName', 'Strata_Type',
       'AddressStreetName', 'StreetNumberMin', 'StreetNumberMax', 'ZipCode',
       'ParcelInfo', 'LGA', 'BuildingSizeSF', 'CountryName', 'CityName',
       'Latitude', 'Longitude','SuffixName']]
#%%
qld_point_parcel_int = gpd.sjoin(qld_parcel_bfp_int,qld_df,predicate='contains')
#%%
qld_point_parcel_int.shape
#%%
qld_point_parcel_int[~qld_point_parcel_int.duplicated('VendorID',keep=False)].to_file('QLD_point_parcel_int.geojson')
#%%
qld_point_parcel_int_op = qld_point_parcel_int[~qld_point_parcel_int.duplicated('VendorID',keep=False)]
#%%
qld_point_parcel_int[qld_point_parcel_int.duplicated('VendorID',keep=False)].sort_values('VendorID').head()
#%% md
## VIC
#%%
VIC_parcels = gpd.read_file('/home/<USER>/Downloads/VIC-20241111T074408Z-001/VIC')
#%%
VIC_parcels.shape
#%%
vic_df = step_2_df
#%%
vic_df.shape
#%%
VIC_BFP_gdf=BFP_gdf[BFP_gdf['StateName'] == 'Victoria']
#%%
VIC_BFP_gdf.shape
#%%
VIC_BFP_gdf.columns
#%%
VIC_BFP_gdf = VIC_BFP_gdf[['PropertyID', 'AL_Building_ID', 'PropertyName', 'ResearchStatus',
       'UseType', 'Floors', 'Address', 'ConstructionStatusName', 'geometry',
       'StateName', 'Strata_Type', 'AddressStreetName', 'StreetNumberMin',
       'StreetNumberMax', 'ZipCode', 'ParcelInfo', 'LGA', 'BuildingSizeSF',
       'CountryName', 'CityName', 'Latitude', 'Longitude','SuffixName']]
#%%
VIC_BFP_gdf.head()
#%%
VIC_BFP_gdf['geometry'] = VIC_BFP_gdf.apply(lambda row: Point(row['Longitude'], row['Latitude']), axis=1)
VIC_BFP_gdf = gpd.GeoDataFrame(VIC_BFP_gdf, geometry='geometry', crs='EPSG:4326')
#%%
vic_parcel_bfp_int = gpd.sjoin(VIC_parcels,VIC_BFP_gdf,predicate='contains')
#%%
vic_parcel_bfp_int.columns
#%%
vic_parcel_bfp_int=vic_parcel_bfp_int[['Lot_ID', 'Lot', 'Plan', 'Parcel_No', 'Strata_typ', 'Complex_Na',
       'Lot_Area', 'Strata_Pla', 'geometry', 'PropertyID',
       'AL_Building_ID', 'PropertyName', 'ResearchStatus', 'UseType', 'Floors',
       'Address', 'ConstructionStatusName', 'StateName', 'Strata_Type',
       'AddressStreetName', 'StreetNumberMin', 'StreetNumberMax', 'ZipCode',
       'ParcelInfo', 'LGA', 'BuildingSizeSF', 'CountryName', 'CityName',
       'Latitude', 'Longitude','SuffixName']]
#%%
vic_point_parcel_int = gpd.sjoin(vic_parcel_bfp_int,vic_df,predicate='contains')
#%%
vic_point_parcel_int.shape
#%%
vic_point_parcel_int.columns.tolist()
#%%
vic_point_parcel_int[~vic_point_parcel_int.duplicated('VendorID',keep=False)].to_file('VIC_point_parcel_int.geojson')
#%%
vic_point_parcel_int_op = vic_point_parcel_int[~vic_point_parcel_int.duplicated('VendorID',keep=False)]
#%%
vic_point_parcel_int_op.shape
#%%
nsw_point_parcel_int_op.shape
#%%
nsw_point_parcel_int_op[nsw_point_parcel_int_op['State']!='57'][['State']]
#%%
qld_point_parcel_int_op.shape
#%%
12206+94+564+155+1236+657
#%%
gdf[gdf['State']== '59'].shape
#%%
22065+2144+4023
#%%
(14912/28232)*100
#%%
564+657
#%%
df['State'].value_counts()
#%% md
## Other
#%%
df_1 = intersected_gdf
df_2 = gpd.read_file('NSW_point_parcel_int.geojson')
df_3 = gpd.read_file('QLD_point_parcel_int.geojson')
df_4 = gpd.read_file('VIC_point_parcel_int.geojson')
#%%
df_2.shape
#%%
df_3.shape
#%%
df_4.shape
#%%
req_cols = df_4.columns.tolist()
#%%
df_1.shape
#%%
concat_df = pd.concat([df_1,df_2,df_3,df_4])
#%%
concat_df.shape
#%%
concat_df['VendorID'].nunique()
#%%
concat_df[concat_df['VendorID'].isin(intersected_gdf['VendorID'])].shape
#%%
intersected_gdf[['StreetName','AddressStreetName','StreetNoMin','StreetNoMax','StreetNumberMin','StreetNumberMax','City','CityName']].head(10)
#%%
gdf[gdf['VendorID'].isin(concat_df['VendorID'])].info()
#%% md
## Property Tenant Verify
#%%
from fuzzywuzzy import fuzz
#%%
concat_df['fuzzy_city_score'] = concat_df.apply(lambda row: fuzz.ratio(row['City'], row['CityName']) if pd.notna(row['City']) and pd.notna(row['CityName']) else 0, axis=1)
#%%
concat_df[ (concat_df['fuzzy_city_score'] < 80) & (concat_df['VendorID'].isin(intersected_gdf['VendorID']))][['StreetName','AddressStreetName','StreetNoMin','StreetNoMax','StreetNumberMin','StreetNumberMax','City','CityName']].shape
#%%
concat_df['fuzzy_score'] = concat_df.apply(lambda row: fuzz.ratio(row['StreetName'], row['AddressStreetName']) if pd.notna(row['StreetName']) and pd.notna(row['AddressStreetName']) else 0, axis=1)
#%%
concat_df['fuzzy_suffix_score'] = concat_df.apply(lambda row: fuzz.ratio(row['Suffix'], row['SuffixName']) if pd.notna(row['Suffix']) and pd.notna(row['SuffixName']) else 0, axis=1)
#%%
import re
def extract_number(s):
    s=str(s)
    matches = re.findall(r'\d+', s)
    if matches:
        longest_match = max(matches, key=len)
        return longest_match
    else:
        return None
#%%
concat_df['StreetNumberMax'] = concat_df['StreetNumberMax'].apply(extract_number)
concat_df['StreetNumberMin'] = concat_df['StreetNumberMin'].apply(extract_number)
#%%
def StreetNumber_match_score(Tenant_street_min,Tenant_street_max,Property_street_min,Property_street_max):
    if pd.isna(Tenant_street_min) or pd.isna(Tenant_street_max) or pd.isna(Property_street_min) or pd.isna(Property_street_max):
        return 0
    tenant_num_min = int(Tenant_street_min)
    tenant_num_max = int(Tenant_street_max)
    prop_num_min = int(Property_street_min)
    prop_num_max = int(Property_street_max)
    if (tenant_num_min==prop_num_min) and (tenant_num_max==prop_num_max):
        return 100
    elif (tenant_num_min==prop_num_min):
        return 95
    elif (tenant_num_max==prop_num_max):
        return 90
    else:
        return 0
#%%
concat_df['StreetNumberMatchScore'] = concat_df.apply(
        lambda row: StreetNumber_match_score(row['StreetNoMin'],row['StreetNoMax'],row['StreetNumberMin'], row['StreetNumberMax']),
        axis=1
    )
#%%
import pandas as pd
import pandas as pd
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
from pandarallel import pandarallel
from fuzzywuzzy import fuzz

pandarallel.initialize(progress_bar=True,nb_workers=10)
#%%
concat_df.head()
#%%
c_df_1 = concat_df[(concat_df['fuzzy_city_score']>80)& (concat_df['fuzzy_score']>=80) & (concat_df['fuzzy_suffix_score']>80)] 
#%%
c_df_1[(c_df_1['fuzzy_score'] > 80) & (~(c_df_1['Street_no_check']))][['StreetName','AddressStreetName','StreetNoMin','StreetNoMax','StreetNumberMin','StreetNumberMax','Street_no_check']].head()
#%%
concat_df[(concat_df['fuzzy_city_score']>75)&(concat_df['fuzzy_score']>75)& (concat_df['Street_no_check']) & (concat_df['fuzzy_suffix_score']>75)].shape
#%%
c_df_2 = concat_df[(concat_df['fuzzy_city_score']>90)&(concat_df['fuzzy_score']>90)& (concat_df['Street_no_check']) & (concat_df['fuzzy_suffix_score']>90)]
#%%
c_df_3 = concat_df[(concat_df['fuzzy_city_score']>=80)&(concat_df['fuzzy_score']>=80)& (concat_df['StreetNumberMatchScore']>80) & (concat_df['fuzzy_suffix_score']>=80)]
#%%
c_df_3.shape
#%%
c_df_3[~c_df_3['VendorID'].isin(c_df_2['VendorID'])][['StreetName','AddressStreetName','StreetNoMin','StreetNoMax','StreetNumberMin','StreetNumberMax','Street_no_check']]
#%%
c_df_3.shape
#%%
c_df_2['fuzzy_suffix_score'].min()
#%%
gdf_req = gdf[~gdf['VendorID'].isin(c_df_3['VendorID'])]
#%%
gdf_req.shape
#%% md
## Other_1
#%%
fill_gdf = gdf_req[gdf_req['State'].isna() | (gdf_req['City'].isna())]
#%%
gdf_req[gdf_req['PostalCode'].isna()].shape
#%%
cities = gpd.read_file('/home/<USER>/Downloads/cities_final.geojson')
#%%
cities.rename(columns = {'State':'City_state','City':'City_city'},inplace = True)
#%%
cities.shape
#%%
city_intersection_gdf = gpd.sjoin(gdf_req,cities,predicate='within')
#%%
city_intersection_gdf.shape
#%%
COLUMNS = fill_gdf.columns.tolist()
#%%
city_intersection_gdf.head()
#%%
city_intersection_gdf['City_state'].value_counts()
#%%
import numpy as np
#%%
def flag_marker(row):
    if pd.notna(row['City_state']) and pd.isna(row['State']):
        row['Flag'] = 1
    else:
        row['Flag'] = 0
    return row
#%%
city_intersection_gdf = city_intersection_gdf.parallel_apply(flag_marker,axis=1)
#%%
city_intersection_gdf[city_intersection_gdf['Flag']==0][['State','City_state','StateAbbr']].head()
#%%
d={'New South Wales':'57','Queensland':'58','Victoria':'59','Australian Capital Territory':np.nan,
   'South Australia':np.nan,'Western Australia':np.nan,'Northern Territory':np.nan,'Tasmania':np.nan,'Other Territories':np.nan}
#%%
def state_fill(row):
    row['StateID'] = d[row['City_state']]
    return row
#%%
city_intersection_gdf = city_intersection_gdf.parallel_apply(state_fill,axis=1)
#%%
city_intersection_gdf['State'].fillna(city_intersection_gdf['StateID'], inplace=True)
#%%
city_intersection_gdf['City'].fillna(city_intersection_gdf['City_city'], inplace=True)
#%%
city_intersection_gdf[city_intersection_gdf['State'].isna() | (city_intersection_gdf['City'].isna())].shape
#%%
# city_intersection_gdf[COLUMNS].to_file('city_intersection_gdf.geojson')
#%%
city_intersection_gdf= city_intersection_gdf[COLUMNS]
#%% md
## ZipCode fill
#%%
zip_fill_df = city_intersection_gdf[city_intersection_gdf['PostalCode'].isna()]
#%%
zip_fill_df.shape
#%%
ZipCode = gpd.read_file("/home/<USER>/Downloads/arealytics_postalcode.geojson")
#%%
ZipCode.shape
#%%
ZipCode.columns
#%%
zipcode_intersection = gpd.sjoin(city_intersection_gdf,ZipCode,predicate='within')
#%%
zipcode_intersection.shape
#%%
zipcode_intersection.head()
#%%
ZipCode[~ZipCode['POA_CODE16'].str.isdigit()].shape
#%%
zipcode_intersection['PostalCode'] = zipcode_intersection['POA_CODE16']
#%%
zipcode_intersection[zipcode_intersection['PostalCode'].isna()].shape
#%%
zipcode_intersection = zipcode_intersection[COLUMNS]
#%%
zipcode_intersection.shape
#%%
zipcode_intersection['State'].value_counts()
#%%
zipcode_intersection[(zipcode_intersection['State'] == '57')].shape

#%%
zipcode_intersection[(zipcode_intersection['State'] == '57') | (zipcode_intersection['State'] == '58') | (zipcode_intersection['State'] == '59')].shape
#%%
zipcode_intersection[zipcode_intersection['State'].isna()].shape
#%%
zipcode_intersection['State'].value_counts()
#%%
zipcode_intersection.shape
#%%
gdf_req.shape
#%%
zipcode_intersection.rename(columns = {'PropertyID_right':'PropertyID','Latitude_right':'Latitude','Longitude_right':'Longitude'},inplace=True)
#%%
zipcode_intersection.to_parquet("zipcode_intersection.parquet")
#%% md
## PID and Address filling
#%%
c_df_3.shape
#%%
c_df_3.columns.tolist()
#%%
c_df_3['PropertyID_right'].fillna(c_df_3['PropertyID_left'], inplace=True)
c_df_3['PostalCode'].fillna(c_df_3['ZipCode'], inplace=True)
c_df_3['StreetNoMax'].fillna(c_df_3['StreetNumberMax'], inplace=True)
c_df_3['StreetNoMin'].fillna(c_df_3['StreetNumberMin'], inplace=True)
c_df_3['Suffix'].fillna(c_df_3['SuffixName'], inplace=True)
#%%
c_df_3['fuzzy_score'].min()
#%%
c_df_3.columns
#%%
c_df_3 = c_df_3[['MarketableFlag', 'City', 'Address2', 'ASICEntityClass',
       'ASICEntityStatus', 'ASICEntityType', 'DomesticParentCountry',
       'DomesticParentName', 'EmployeeIndicator',
       'GlobalUltimateParentCountry', 'GlobalUltimateParentName',
       'ImmediateParentCountry', 'ImmediateParentName', 'PrimarySIC2DigitDesc',
       'PrimarySIC3DigitDesc', 'PrimarySICDesc', 'PrimarySICDivision',
       'PrimarySICDivisionDesc', 'RevenueIndicator', 'TenantName',
       'StreetName', 'Email', 'GST_Status', 'GST_StatusFromDate', 'ABNStatus',
       'ABN', 'ACN', 'RegistrationOrIncorporationDate', 'ABN_StatusFromDate',
       'SourcedDate', 'Key_Decision_Maker_Middle_Name',
       'Key_Decision_Maker_First_Name', 'Key_Decision_Maker_Last_Name',
       'LegalStatus', 'StreetType', 'Address1', 'CEOName', 'CEOTitle',
       'LineOfBusiness', 'NationalID', 'OfficePhone', 'Fax', 'StreetNoMin',
       'StreetNoMax', 'StreetNo', 'PostalCode', 'Suffix', 'SuffixAbbr',
       'StateAbbr', 'State', 'WebsiteURL', 'CountryCode', 'ProviderID',
       'EmployeesAtLocation', 'StatusCode', 'IsProcessed', 'ConfirmedTenantID',
       'PropertyID_right', 'MatchingScore', 'CreatedDate', 'ModifiedDate',
       'Tenant_Stage_Id', 'ParentCompanyID', 'BranchID', 'BatchID',
       'IsDefault', 'NAICSCode', 'NACECode', 'ModifiedBy', 'IsHidden',
       'IsDeleted', 'HidedBy', 'HidedDate', 'HideReasonID',
       'HideReasonComments', 'SubHideReasonID', 'VendorID',
       'DomesticParentDUNS', 'EntityAge', 'GlobalUltimateParentDUNS',
       'SubsidiaryCode', 'ImmediateParentDUNS', 'NumberofMembersinHierarchy',
       'SICCode', 'PrimarySIC2Digit', 'PrimarySIC3Digit', 'HQ_CompanyName',
       'HQ_ID', 'Latitude_right', 'Longitude_right', 'Revenue', 'ANZSICCode',
       'EmployeeCount', 'Illion_version', 'geometry','fuzzy_score','StreetNumberMatchScore']]
#%%
c_df_3[c_df_3['fuzzy_score'] == 100].shape
#%%
c_df_3['Sequence'] = 1
#%%
c_df_3.info()
#%%
c_df_3.rename(columns = {'PropertyID_right':'PropertyID','Latitude_right':'Latitude','Longitude_right':'Longitude'},inplace=True)
#%%
final = pd.concat([c_df_3,zipcode_intersection,missing_data])
#%%
final.shape
#%%
final.rename(columns={'fuzzy_score':'StreetNameScore'},inplace=True)
#%%
gdf.shape
#%%
missing_data  = gdf[~gdf['VendorID'].isin(final['VendorID'])]
#%%
missing_data.shape
#%%
final.columns
#%%
final.shape
#%%
final.to_csv("Partial_address_enrichment.psv",sep='|')
#%%
