#%%
import pandas as pd
import geopandas as gpd
import numpy as np
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
from pandarallel import pandarallel
from fuzzywuzzy import fuzz
from sqlalchemy import create_engine
import yaml


pandarallel.initialize(progress_bar=True, nb_workers=12)
#%%
# Removes any non-numeric characters from a string and returns the longest sequence of digits. If no digits are found, it returns None.
import re
def extract_number(s):
    s=str(s)
    matches = re.findall(r'\d+', s)
    if matches:
        longest_match = max(matches, key=len)
        return longest_match
    else:
        return None
#%%
# Returns 'StreetNumberMin' if 'StreetNumberMax' is NaN, otherwise it returns 'StreetNumberMax`.
def add_street_max(row):
    if pd.isna(row['StreetNumberMax']):
        return row['StreetNumberMin']
    return row['StreetNumberMax']

#%%
# Fetch Property Data from AL-DB
'''
EXPORT DATA
OPTIONS (
    uri = 'gs://arealytics-data-lake-curated/Sep2024/NewIllion/PropertyDataForPIDMatch/File-*.parquet',
    format = 'PARQUET',
    overwrite = TRUE
)
AS
select P.PropertyID,P.PropertyName,A.AddressStreetName,S1.SuffixID as Suffix1 ,S1.SuffixName as SuffixName1,S2.SuffixID as Suffix2 ,S2.SuffixName as SuffixName2, A.Address1,A.Address2,A.StreetNumberMin,A.StreetNumberMax,C.CityName,CT.CondoTypeName,ST.StateName,A.ZipCode,A.Sequence  from ar_rep_Empirical_Prod.Property P
inner join ar_rep_Empirical_Prod.Address A on A.ParentID = P.PropertyID and A.Sequence=1 and A.ParentTableID=1 and A.IsActive=1
inner join ar_rep_Empirical_Prod.PropertySummary PR on PR.PropertyID = P.PropertyID
left join ar_rep_Empirical_Prod.Suffix S1 on S1.SuffixID = A.SuffixID
left join ar_rep_Empirical_Prod.Suffix S2 on S2.SuffixID = A.Suffix2ID
left join ar_rep_Empirical_Prod.City C on C.CityID = A.CityID
left join ar_rep_Empirical_Prod.CondoType CT on CT.CondoTypeID = P.CondoTypeID
left join ar_rep_Empirical_Prod.State ST on ST.StateID = A.StateID
where P.CondoTypeID in (1,3) and PR.ResearchTypeID not in (1,4);
'''
#%%
PropertyData = pd.read_parquet('/home/<USER>/Desktop/Arealytics Data/PID Matching Tenants/PropertyDataForPIDMatch',engine='pyarrow') 
#%%
PropertyData.info()
#%%
PropertyData.shape
#%%
PropertyData.groupby('StateName').size()
#%%
# At this Point , PID matching only for 'New South Wales','Queensland',"Victoria" 
PropertyData = PropertyData[PropertyData['StateName'].isin(['New South Wales','Queensland',"Victoria"])]
#%%
PropertyData.groupby('StateName').size()
#%%
PropertyData.info()
#%%
PropertyData['ZipCode'].value_counts(dropna=False)
#%%
PropertyData['PropertyID'].nunique()
#%%
PropertyData['CondoTypeName'].unique()
#%%
PropertyData.groupby('CondoTypeName').size()
#%%
PropertyData.head()
#%%
## return Concatenates two suffix columns if both are present, otherwise returns the non-null suffix, or None if both are missing.
def combining_suffix(row):
    if pd.notna(row['SuffixName1']) and pd.notna(row['SuffixName2']):
        print(row['PropertyID'])
        return row['SuffixName1'] + " " + row['SuffixName2']
    elif pd.notna(row['SuffixName1']) and pd.isna(row['SuffixName2']):
        return row['SuffixName1']
    elif pd.isna(row['SuffixName1']) and pd.notna(row['SuffixName2']):
        return row['SuffixName2']
    else:
        return None

#%%
PropertyData['SuffixName'] = PropertyData.parallel_apply(combining_suffix, axis=1)
#%%
PropertyData.shape
#%%
PropertyData.groupby('Sequence').size()
#%%
### Removing any string
#%%
#Removes any non-numeric characters from a string
PropertyData['StreetNumberMin'] = PropertyData['StreetNumberMin'].apply(extract_number)
#%%
#Removes any non-numeric characters from a string
PropertyData['StreetNumberMax'] = PropertyData['StreetNumberMax'].apply(extract_number)
#%%
# Adding 'StreetNumberMin' if 'StreetNumberMax' is NaN, otherwise it returns 'StreetNumberMax`.
PropertyData['StreetNumberMax'] = PropertyData.parallel_apply(add_street_max, axis=1)
#%%
PropertyData[(PropertyData['StreetNumberMin'].notna()) & (PropertyData.AddressStreetName.notna())].shape
#%%
#Ensuring that only records with valid street numbers and street names are retained
PropertyData = PropertyData[(PropertyData['StreetNumberMin'].notna()) & (PropertyData.AddressStreetName.notna())]
#%%
# Removing whitespace
PropertyData['CityName'] = PropertyData['CityName'].apply(lambda x: x.strip() if isinstance(x, str) else x)
#%%
# Convert the 'StreetNumberMin' and 'StreetNumberMax' columns in PropertyData to integer data types, 
PropertyData['StreetNumberMin'] = PropertyData['StreetNumberMin'].astype('int')
PropertyData['StreetNumberMax'] = PropertyData['StreetNumberMax'].astype('int')
#%%
PropertyData['StreetNumberMin'].isna().sum()
#%%
PropertyData.info()
#%%
PropertyData['ZipCode'] = PropertyData['ZipCode'].apply(extract_number)
#%%
PropertyData['ZipCode'] = PropertyData['ZipCode'].fillna(0).astype('int')
#%%
PropertyData.shape
#%%
### New Tenants
#%%
# new_illion data fetch from BigQuery 
# complete address but incomplete lat long
'''
EXPORT DATA
  OPTIONS (
    uri = 'gs://arealytics-data-lake-curated/Sep2024/NewIllion/NewIllionForIngestion/File-*.parquet',
    format = 'PARQUET',
    overwrite = TRUE
  )
AS
SELECT * 
FROM ar-sandbox-data-lakehouse.ar_curated_illion.new_illion 
WHERE Illion_version = 'V2'
  AND VendorID IS NOT NULL
  AND TenantName IS NOT NULL
  AND MarketableFlag = 1
  AND (ABNStatus <> 'CAN' OR ABNStatus IS NULL)
  AND (TRIM(LegalStatus) NOT IN ('TRT', 'NPF', 'DES', 'STR', 'HYT', 'CUT', 'SMF', 'FXT', 'CMT', 'SAF', 'FUT', 'NRF', 'PTT', 'FPT', 'DIT', 'PST', 'DTT', 'POF', 'PQT', 'DST') OR LegalStatus IS NULL); -- 428063
'''
#%%
# Valid Data address with Lat long
'''
  SELECT DISTINCT VendorID
    FROM `ar-sandbox-data-lakehouse.ar_curated_illion.new_illion`
    WHERE Illion_version = 'V2'
        AND VendorID IS NOT NULL
        AND TenantName IS NOT NULL
        AND StreetName IS NOT NULL
        AND PostalCode IS NOT NULL
        AND City IS NOT NULL
        AND StreetNoMin IS NOT NULL
        AND Latitude IS NOT NULL
         AND Longitude IS NOT NULL
        AND State IS NOT NULL
        AND Suffix IS NOT NULL
        AND MarketableFlag = 1
        AND (ABNStatus <> 'CAN' OR ABNStatus IS NULL)
        AND (TRIM(LegalStatus) NOT IN ('TRT', 'NPF', 'DES', 'STR', 'HYT', 'CUT', 'SMF', 
            'FXT', 'CMT', 'SAF', 'FUT', 'NRF', 'PTT', 'PDT', 'DIT', 'PST', 'DTT', 
            'POF', 'PQT', 'DST') OR LegalStatus IS NULL)

'''

#%%
new_illion = pd.read_parquet('/home/<USER>/Desktop/Arealytics Data/PID Matching Tenants/NewIllionForIngestion')
#%%
new_illion.shape
#%%
new_illion.info()
#%%
new_illion_columns = new_illion.columns
#%%
new_illion.shape
#%%
### incomplete address but having lat long
'''
select * from ar-sandbox-data-lakehouse.ar_curated_illion.new_illion 
where Illion_version='V2'
and VendorID IS NOT NULL
  AND TenantName IS NOT NULL
  AND MarketableFlag = 1
  AND (Latitude is not null and Longitude is not null)
  AND (ABNStatus <> "CAN" OR ABNStatus IS NULL)
   AND (TRIM(LegalStatus) not IN ('TRT', 'NPF', 'DES', 'STR', 'HYT', 'CUT', 'SMF', 'FXT', 'CMT', 'SAF', 'FUT', 'NRF', 'PTT', 'FPT', 'DIT', 'PST', 'DTT', 'POF', 'PQT', 'DST')  or LegalStatus is null)
  AND VendorID not in (

select VendorID from ar-sandbox-data-lakehouse.ar_curated_illion.new_illion 
where Illion_version='V2'
and VendorID IS NOT NULL
  AND TenantName IS NOT NULL
  AND MarketableFlag = 1
  AND (ABNStatus <> "CAN"
    OR ABNStatus IS NULL)
  AND (TRIM(LegalStatus) not IN ('TRT', 'NPF', 'DES', 'STR', 'HYT', 'CUT', 'SMF', 'FXT', 'CMT', 'SAF', 'FUT', 'NRF', 'PTT', 'FPT', 'DIT', 'PST', 'DTT', 'POF', 'PQT', 'DST')  or LegalStatus is null)
     AND StreetName IS NOT NULL
  AND PostalCode IS NOT NULL
  AND City IS NOT NULL
  AND StreetNoMin is NOT NULL
  AND State is NOT NULL
 AND Suffix is not null);


'''
# for the data get address  through lat long (by using building footpronts)
#%%
# After getting complete address add to the new_illion
#%%
# Set 1 : After intersecting Tenants with PropertyInformation then Matched Street name,Street No and Suffix attached PIDs 
# set 2 : Filled missing tenant data fields (PostalCode, State, City) using City and ZipCode information from GeoJSON data
#%%
Partial_address_enrichment = pd.read_parquet('/home/<USER>/Desktop/Arealytics Data/PID Matching Tenants/Partial_address_enrichment_new_tenants.parquet')
#%%
Partial_address_enrichment.shape
#%%
Partial_address_enrichment.info()
#%%
par_col = Partial_address_enrichment.columns.tolist()
#%%
x_columns = ['PostalCode_x', 'StreetNoMax_x', 'StreetNoMin_x', 'Suffix_x', 'City_x', 'State_x']
y_columns = ['PostalCode_y', 'StreetNoMax_y', 'StreetNoMin_y', 'Suffix_y', 'City_y', 'State_y']
#%%
par_col
#%%
merged_df  = pd.merge(new_illion,Partial_address_enrichment,on='VendorID',how='left')
#%%
merged_df.shape
#%%
merged_df.head()
#%%
merged_df['PostalCode_x'].fillna(merged_df['PostalCode_y'], inplace=True)
merged_df['StreetNoMax_x'].fillna(merged_df['StreetNoMax_y'], inplace=True)
merged_df['StreetNoMin_x'].fillna(merged_df['StreetNoMin_y'], inplace=True)
merged_df['Suffix_x'].fillna(merged_df['Suffix_y'], inplace=True)
merged_df['City_x'].fillna(merged_df['City_y'], inplace=True)
merged_df['State_x'].fillna(merged_df['State_y'], inplace=True)
#%%
# List all columns with the '_x' or '_y' suffix
columns_to_rename = [col for col in merged_df.columns if col.endswith(('_x'))]

# Rename them by removing the '_x' or '_y' suffix
merged_df.rename(columns={col: col[:-2] for col in columns_to_rename}, inplace=True)
#%%
merged_df.drop(y_columns,axis=1,inplace=True)
#%%
merged_df.shape
#%%
new_illion = merged_df
#%%

#%%
## Tenants with noLatLong from illion and filled LatLong from GNAF
'''
  SELECT DISTINCT VendorID
    FROM `ar-sandbox-data-lakehouse.ar_curated_illion.new_illion`
    WHERE Illion_version = 'V2'
        AND VendorID IS NOT NULL
        AND TenantName IS NOT NULL
        AND StreetName IS NOT NULL
        AND PostalCode IS NOT NULL
        AND City IS NOT NULL
        AND StreetNoMin IS NOT NULL
        AND Latitude IS  NULL
         AND Longitude IS  NULL
        AND State IS NOT NULL
        AND Suffix IS NOT NULL
        AND MarketableFlag = 1
        AND (ABNStatus <> 'CAN' OR ABNStatus IS NULL)
        AND (TRIM(LegalStatus) NOT IN ('TRT', 'NPF', 'DES', 'STR', 'HYT', 'CUT', 'SMF', 
            'FXT', 'CMT', 'SAF', 'FUT', 'NRF', 'PTT', 'PDT', 'DIT', 'PST', 'DTT', 
            'POF', 'PQT', 'DST') OR LegalStatus IS NULL)

'''
#%%
GNAF_DATA = pd.read_parquet('/home/<USER>/Desktop/Arealytics Data/PID Matching Tenants/New_Tenants_with_GNAF_data.parquet')
#%%
GNAF_DATA.shape
#%%
merged_df1  = pd.merge(new_illion,GNAF_DATA,on='VendorID',how='left')
#%%
merged_df1.shape
#%%
merged_df1['Latitude'].fillna(merged_df1['lat'], inplace=True)
merged_df1['Longitude'].fillna(merged_df1['long'], inplace=True)
#%%
merged_df1.drop(['lat','long'],axis=1,inplace=True)
#%%
merged_df1.shape
#%%
merged_df1.head()
#%%
new_illion=merged_df1
#%%
new_illion['StateAbbr'].unique()
#%%
new_illion['State'] = new_illion['State'].astype(str).str.split('.').str[0]
#%%
new_illion.replace(['None', "nan"], np.nan, inplace=True)
#%%
new_illion.head()
#%%
new_illion.groupby('State',dropna=False).size()
#%%
new_illion.groupby('StateAbbr',dropna=False).size()
#%%
# At this Point , PID matching only for 'New South Wales','Queensland',"Victoria" 
new_illion = new_illion[(new_illion['State'].isin(["57","58","59"])) & (new_illion['StateAbbr'].isin(["VIC","QLD","NSW"]))]
#%%
new_illion.groupby('State').size()
#%%
new_illion.shape
#%%
## for this data , need to perform ganf mapping to get Latitude and Longitude
#%%
## Ingest these new Illion into stage Table (Illion_new_2024_09_01)
#%%
Tenant_Stage_columns=["VendorID","TenantName","Address1","Address2","City","State","StateAbbr","CountryCode","PostalCode","NationalID","OfficePhone","Fax","CEOName","CEOTitle","LineOfBusiness","SICCode","Revenue","EmployeesAtLocation","EmployeeCount","LegalStatus","StatusCode","SubsidiaryCode","IsProcessed","ConfirmedTenantID","PropertyID","MatchingScore","CreatedDate","ModifiedDate","Tenant_Stage_Id","Latitude","Longitude","ParentCompanyID","BranchID","BatchID","ProviderID","IsDefault","NAICSCode","NACECode","Email","WebsiteURL","ModifiedBy","IsHidden","IsDeleted","HidedBy","HidedDate","HideReasonID","HideReasonComments","ASICEntityStatus","ASICEntityType","ASICEntityClass","ABNStatus","ABN_StatusFromDate","GST_Status","GST_StatusFromDate","RegistrationOrIncorporationDate","EntityAge","EmployeeIndicator","RevenueIndicator","HQ_ID","HQ_CompanyName","NumberofMembersinHierarchy","ImmediateParentDUNS","ImmediateParentName","ImmediateParentCountry","DomesticParentDUNS","DomesticParentName","DomesticParentCountry","GlobalUltimateParentDUNS","GlobalUltimateParentName","GlobalUltimateParentCountry","PrimarySICDesc","PrimarySIC3Digit","PrimarySIC3DigitDesc","PrimarySIC2Digit","PrimarySIC2DigitDesc","PrimarySICDivision","PrimarySICDivisionDesc","SubHideReasonID","ANZSICCode","MarketableFlag"]
#%%
len(Tenant_Stage_columns)
#%%
len(set(Tenant_Stage_columns).intersection(set(new_illion.columns.tolist())))
#%%
new_illion_ingestable = new_illion[Tenant_Stage_columns]
#%%
# Count total rows in the DataFrame
total_rows = len(new_illion_ingestable)

# Calculate null counts for each column
null_counts = new_illion_ingestable.count()

# Convert the result to a DataFrame
non_null_counts_df = null_counts.reset_index()
non_null_counts_df.columns = ['Column', 'NonNullCount']
non_null_counts_df.sort_values(by='Column',inplace=True)
# Display the DataFrame
non_null_counts_df

#%%
new_illion_ingestable.shape
#%%
new_illion_ingestable['PropertyID']= np.nan
#%%
new_illion_ingestable.info()
#%%
destination_db_config = {
    "username": "XXXXXXXXXXXXXXXXXXXXX",
    "password": "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    "host": "localhost",
    "port": "3392",
    "database_name": "Empirical_DataIngestion",
    "table_name": "Illion_New_2024_09_01"
}

#%%
# Connection String
destination_connection_string = f"mysql+mysqlconnector://{destination_db_config['username']}:{destination_db_config['password']}@{destination_db_config['host']}:{destination_db_config['port']}/{destination_db_config['database_name']}"
destination_database_engine = create_engine(destination_connection_string)
#%%
destination_db_config['port']
#%%
destination_db_config['database_name']
#%%
destination_db_config['table_name']
#%%
destination_database_engine
#%%
new_illion_ingestable[Tenant_Stage_columns].shape
#%%
# # Define chunk size
# chunk_size = 10000

# # Split the DataFrame into chunks and push each chunk individually
# for i in range(0, len(new_illion), chunk_size):
#     # Define the chunk



#     df_chunk = new_illion_ingestable.iloc[i:i+chunk_size]

#     # Push the chunk to SQL
#     df_chunk.to_sql(
#         name=destination_db_config['table_name'],
#         con=destination_database_engine,
#         if_exists='append',  # append after the first chunk
#         index=False
#     )
#     print(f"Records {i} Pushed")

# print("Data pushed to SQL in batches.")

#%%
##PID Matching
#%%
new_illion.PostalCode.info()
#%%
### Tenants having zipcodes
#%%
new_illion['PostalCode'] = new_illion['PostalCode'].apply(extract_number)
#%%
# new_illion = new_illion[(new_illion['PostalCode'].notna()) &(new_illion['PostalCode'].str.isdigit())]
#%%
new_illion[(new_illion['PostalCode'].notna())].shape
#%%
new_illion[(new_illion['PostalCode'].isna())].shape
#%%
# Retain only the rows where the 'PostalCode' column is not NaN
new_illion = new_illion[(new_illion['PostalCode'].notna())]
#%%
new_illion.shape
#%%

new_illion[new_illion['Suffix'].notna()].shape
#%%
# # Retain only the rows where the 'Suffix' column is not NaN
# new_illion = new_illion[new_illion['Suffix'].notna()]
#%%
# # Ensuring that only records with valid street numbers and names 
# new_illion = new_illion[(new_illion['StreetNoMin'].notna()) &(new_illion['StreetName'].notna())]
#%%
new_illion.shape
#%%
new_illion['PostalCode'] = new_illion['PostalCode'].astype('int')
#%%
# # Retain only the rows where the 'PostalCode' values exist in the 'ZipCode' column of the PropertyData
# new_illion = new_illion[new_illion['PostalCode'].isin(PropertyData['ZipCode'])]
#%%
new_illion.shape
#%%
new_illion[new_illion['Latitude'].isna() & new_illion['Longitude'].isna()].shape
#%%
(new_illion['StreetName'].isna() | new_illion['Suffix'].isna() | new_illion['StreetNo'].isna()).sum()
#%%
# Removing whitespace
new_illion['City'] = new_illion['City'].apply(lambda x: x.strip() if isinstance(x, str) else x)
#%%
# Clean the 'Suffix' and 'SuffixName' columns in the new_illion and PropertyData DataFrames, respectively, by stripping whitespace and converting the values to uppercase if they are strings.
new_illion ['Suffix'] = new_illion['Suffix'].strip().upper() if isinstance(new_illion['Suffix'], str) else new_illion['Suffix']
PropertyData['SuffixName'] = PropertyData['SuffixName'].strip().upper() if isinstance(PropertyData['SuffixName'], str) else PropertyData['SuffixName']
#%%
new_illion.info()
#%%
from fuzzywuzzy import fuzz
import numpy as np
import pandas as pd

def get_number_set(start, end):
    if pd.isna(start) or pd.isna(end):
        return set()
    start = int(start)
    end = int(end)
    return set(range(start, end + 1, 2))

def StreetNumber_match_score(Tenant_street_min,Tenant_street_max,Property_street_min,Property_street_max):
    if pd.isna(Tenant_street_min) or pd.isna(Tenant_street_max) or pd.isna(Property_street_min) or pd.isna(Property_street_max):
        return 0
    tenant_num_min = int(Tenant_street_min)
    tenant_num_max = int(Tenant_street_max)
    prop_num_min = int(Property_street_min)
    prop_num_max = int(Property_street_max)
    if (tenant_num_min==prop_num_min) and (tenant_num_max==prop_num_max):
        return 100
    elif (tenant_num_min==prop_num_min):
        return 95
    elif (tenant_num_max==prop_num_max):
        return 90
    else:
        return 0
        

def map_properties(tenant_row, prop_df):

    # Preprocess tenant data
    req_prop_list=[]
    if pd.isna(tenant_row['StreetName']) or pd.isna(tenant_row['Suffix']):
        return np.nan

    
    post_code = tenant_row['PostalCode']
    tenant_City = tenant_row['City']
    tenant_street_name = tenant_row['StreetName'].strip().upper()
    tenant_street_numbers = get_number_set(tenant_row['StreetNoMin'], tenant_row['StreetNoMax'])
    tenant_suffix = tenant_row['Suffix']
    
    
    
    
    # Filter properties by PostalCode
    properties = prop_df[(prop_df['ZipCode'] == post_code) & (prop_df['CityName'] == tenant_City)]
    
    if properties.empty:
        return np.nan

    # Vectorized string matching and filtering
    properties['StreetNameScore'] = properties['AddressStreetName'].str.strip().str.upper().apply(
    lambda x: fuzz.ratio(tenant_street_name, x) if pd.notna(tenant_street_name) else 0
)

    # Filter properties based on street name matching score
    matching_properties = properties[properties['StreetNameScore'] > 80]

    if matching_properties.empty:
        return np.nan

    # Check street number overlap and suffix match
    matching_properties['StreetNumberMatchScore'] = matching_properties.apply(
        lambda row: StreetNumber_match_score(tenant_row['StreetNoMin'],tenant_row['StreetNoMax'],row['StreetNumberMin'], row['StreetNumberMax']),
        axis=1
    )
    matching_properties = matching_properties[
        (matching_properties['StreetNumberMatchScore']>=90)
    ]
    # print(matching_properties)
    req_prop_list=[]
    for index,row in matching_properties.iterrows():
        if row['SuffixName'] == tenant_suffix:
            req_prop_list.append([row['Sequence'],row['PropertyID'],row['StreetNameScore'],row['StreetNumberMatchScore']])
    return req_prop_list if req_prop_list else np.nan


#%%
# Example usage
new_illion['MappedProperties'] = new_illion.parallel_apply(map_properties, prop_df=PropertyData, axis=1)

#%%
import numpy as np
new_illion['No_of_property_matches_with_range'] = new_illion['MappedProperties'].apply(lambda x: 0 if np.isnan(x).any() else len(x))
#%%
new_illion['No_of_property_matches_with_range'].unique()
#%%
new_illion.shape
#%%
new_illion[new_illion['No_of_property_matches_with_range']==0].shape
#%%

#%%
# # Ensuring that only records with at least one property match within the specified range are retained.
# new_illion = new_illion[new_illion['No_of_property_matches_with_range']!=0]
#%%
new_illion.head()
#%%
new_illion['No_of_property_matches_with_range'].unique()
#%%
new_illion[["VendorID","City","StateAbbr","TenantName","StreetName",'StreetNoMin',"StreetNoMax","Suffix","MappedProperties","No_of_property_matches_with_range"]].shape
#%%
new_illion = new_illion[["VendorID","City","StateAbbr","TenantName","StreetName",'StreetNoMin',"StreetNoMax","Suffix","MappedProperties","No_of_property_matches_with_range"]]
#%%
new_illion[new_illion['No_of_property_matches_with_range']==1].shape ## Tenant Matched to Single PID
#%%
new_illion[new_illion['No_of_property_matches_with_range']==1].groupby('StateAbbr').size()
#%%
new_illion.head()
#%%
expload_new_illion = new_illion.explode('MappedProperties')
#%%
expload_new_illion.shape
#%%
expload_new_illion.head()
#%%
expload_new_illion[expload_new_illion['VendorID']==747435202]
#%%
import numpy as np

def expand_PID(row):

    if type(row['MappedProperties']) != list:
        row['Sequence'] = np.nan
        row['PropertyID'] = np.nan
        row['StreetNameScore'] = np.nan
        row['StreetNumberMatchScore'] = np.nan
        return row
    
    row['Sequence'] = row['MappedProperties'][0]
    row['PropertyID'] = row['MappedProperties'][1]
    row['StreetNameScore'] = row['MappedProperties'][2]
    row['StreetNumberMatchScore'] = row['MappedProperties'][3]
    return row

#%%
expload_new_illion = expload_new_illion.parallel_apply(expand_PID,axis=1)
#%%
expload_new_illion['VendorID'].nunique()
#%%
expload_new_illion.head()
#%%
PropertyData.rename(columns={'SuffixName':'Suffix','CityName':'City'}, inplace=True)
#%%
# expload_new_illion.rename(columns={'Suffix':'SuffixName','City':'CityName'}, inplace=True)
#%%
expload_tenant_property_df = pd.merge(expload_new_illion,PropertyData, how='left', on=['PropertyID','Sequence','Suffix','City'])
#%%
expload_tenant_property_df.rename(columns={'Address2_x':'Address2','Address1_x':'Address1'},inplace=True)
#%%
expload_tenant_property_df.shape
#%%
expload_tenant_property_df.head()
#%%
expload_tenant_property_df.columns.tolist()
#%%
expload_tenant_property_df[(expload_tenant_property_df['StreetNoMin']!=expload_tenant_property_df["StreetNoMax"] )].head()
#%%
# expload_tenant_property_df.to_csv("Illion_Tenants_PID_Match.csv",index=False)
#%%
expload_tenant_property_df["MappedProperties"] = expload_tenant_property_df["MappedProperties"].apply(lambda x: str(x))
#%%
expload_tenant_property_df.drop_duplicates(subset=['VendorID', 'ZipCode', 'Sequence', 'PropertyID'], keep='first', inplace=True)

#%%
expload_tenant_property_df.shape
#%%
expload_tenant_property_df.groupby('StateAbbr').size()
#%%
expload_tenant_property_df[expload_tenant_property_df['StateAbbr']=='NT']
#%%
expload_tenant_property_df[expload_tenant_property_df['VendorID']==747435202]
#%%
PropertyData[PropertyData['PropertyID']==131777]
#%%
# expload_tenant_property_df[expload_tenant_property_df['PropertyID']==80].head()
#%%
# No_of_property_matches_with_range = 1 and StreetNumberMatchScore > 90 and StreetNameScore > 88
#%%
expload_tenant_property_df[(expload_tenant_property_df['No_of_property_matches_with_range']==1) & (expload_tenant_property_df['StreetNumberMatchScore']>90) &(expload_tenant_property_df['StreetNameScore']>=88) ].shape
#%%
expload_tenant_property_df[(expload_tenant_property_df['No_of_property_matches_with_range']==1) & (expload_tenant_property_df['StreetNumberMatchScore']>90) &(expload_tenant_property_df['StreetNameScore']>=88)].groupby('StateAbbr').size()
#%%
# 85 - 59776
# 80 - 59806
# >=90 - 59721
# >=88 - 59760
#%%
expload_tenant_property_df.shape
#%%
# Table 1 : VendorID with  No PID Match and Latitude is null and Longitude is null
#%%
# GNAF_Table = expload_tenant_property_df[(expload_tenant_property_df['No_of_property_matches_with_range']==0) & (expload_tenant_property_df['Latitude'].isna())&(expload_tenant_property_df['Longitude'].isna())]
#%%
# GNAF_Table.shape
#%%
# GNAF_Table = GNAF_Table[new_illion_columns]
#%%
## for this data , Gnaf neeed to perform
#%%
# GNAF_Table.to_parquet('NewTenantsForGnafMapping.parquet')
#%%

#%%
DB_Ingestable = expload_tenant_property_df
#%%
DB_Ingestable[(DB_Ingestable['No_of_property_matches_with_range']==1) & (DB_Ingestable['StreetNumberMatchScore']>=90) &(DB_Ingestable['StreetNameScore']>=88) ].shape
#%%
DB_Ingestable.shape
#%%
destination_db_config = {
    "username": "XXXXXXXXXXXXXXXXXXXXXXX",
    "password": "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    "host": "localhost",
    "port": "3392",
    "database_name": "Empirical_DataIngestion",
    "table_name": "Illion_New_PID_Mapped_2024_09_01"
}

#%%
# Connection String
destination_connection_string = f"mysql+mysqlconnector://{destination_db_config['username']}:{destination_db_config['password']}@{destination_db_config['host']}:{destination_db_config['port']}/{destination_db_config['database_name']}"
destination_database_engine = create_engine(destination_connection_string)
#%%
destination_db_config['port']
#%%
destination_db_config['database_name']
#%%
destination_db_config['table_name']
#%%
destination_database_engine
#%%
DB_Ingestable.shape
#%%
# Define chunk size
chunk_size = 10000

# Split the DataFrame into chunks and push each chunk individually
for i in range(0, len(DB_Ingestable), chunk_size):
    # Define the chunk
    df_chunk = DB_Ingestable.iloc[i:i+chunk_size]

    # Push the chunk to SQL
    df_chunk.to_sql(
        name=destination_db_config['table_name'],
        con=destination_database_engine,
        if_exists='append' if i > 0 else 'replace',  # append after the first chunk
        index=False
    )
    print(f"Records {i} Pushed")

print("Data pushed to SQL in batches.")

#%%
# DB_Ingestable.to_sql(destination_db_config['table_name'], destination_database_engine, if_exists='replace', index=False, chunksize=10000)
#%%

#%%
