#%%
import pandas as pd
import numpy as np
from fuzzywuzzy import fuzz
from tqdm import tqdm
from pandarallel import pandarallel
pandarallel.initialize(progress_bar=True)
pd.set_option("display.max_columns",None)
pd.set_option("display.max_rows",None)
#%%
import warnings
warnings.filterwarnings("ignore")
#%% md
## Import GNAF Data
#%%
df = pd.read_parquet("VIC/")
#%%
df.shape
#%%
df.head()
#%%
df['POSTCODE'] = df['POSTCODE'].astype('str')
#%%
df = df.set_index('POSTCODE')
#%%
df.info()
#%%
df[df['StreetNoMin'].isna()].shape
#%%
df[df['StreetNoMax'].isna()].shape
#%%
df[(df['StreetNoMin'].isna()) & (df['StreetNoMax'].isna())].shape
#%%
df[df['StreetNoMin'].isna()].index.isin(df[df['StreetNoMax'].isna()].index).all()
#%%
df.dropna(subset=['StreetNoMin'], inplace=True)
#%%
df['StreetNoMax'].fillna(df['StreetNoMin'], inplace=True)
#%%
df['StreetNoMin'] = df['StreetNoMin'].astype("int")
#%%
df['StreetNoMax'] = df['StreetNoMax'].astype('int')
#%%
df.query('StreetNoMin != StreetNoMax').head()
#%%
df.shape
#%% md
## Import Tenant data
#%%
target = pd.read_parquet("NewTenantsForGnafMapping.parquet")
#%%
target.shape
#%%
target.nunique()
#%%
target['PostalCode'] = target['PostalCode'].astype('str')
#%%
target = target[target['PostalCode'].isin(df.index)]
#%%
target.shape
#%%
target = target.set_index('PostalCode')
#%%
target.head()
#%%
target.index[0]
#%%
df.loc[target.index[0]].head()
#%% md
## Matching code 
Street Number ratio >= 80   
Street Name ratio >= 85    
State direct match
#%%
def get_number_set(start, end):
    if pd.isna(start) or pd.isna(end):
        return set()
    start, end = int(start), int(end)
    return set(range(start, end + 1, 2))

def check_streetno_match(df1_row, df2):
    # Pre-compute values from df1_row
    src_st_number_min = df1_row['StreetNoMin']
    src_st_number_max = df1_row['StreetNoMax']
    src_street_name = df1_row['StreetName'].lower()
    src_suffix = df1_row['StreetType'].lower()

    # Calculate number sets for StreetNoMin and StreetNoMax in parallel
    src_street_numbers = get_number_set(src_st_number_min, src_st_number_max)
    df2_street_numbers = df2.apply(lambda row: get_number_set(row['StreetNoMin'], row['StreetNoMax']), axis=1)
    intersection_condition = df2_street_numbers.apply(lambda x: bool(src_street_numbers.intersection(x))).to_numpy()

    # Vectorized street number ratio calculation
    street_number_ratio = np.where(
        (df2['StreetNoMin'] == src_st_number_min) & (df2['StreetNoMax'] == src_st_number_max), 100,
        np.where(df2['StreetNoMin'] == src_st_number_min, 95,
        np.where(df2['StreetNoMax'] == src_st_number_max, 90,
        np.where(intersection_condition, 80, 0)))
    )

    # Fuzzy street name matching in parallel
    street_name_ratio = df2['STREET_NAME'].apply(lambda x: fuzz.ratio(src_street_name, x.lower()))

    # Suffix match vectorization
    suffix_match = (df2['Suffix'].str.lower() == src_suffix)

    # Adding results as columns in df2
    df2['street_number_ratio'] = street_number_ratio
    df2['street_name_ratio'] = street_name_ratio
    df2['suffix_match'] = suffix_match

    # Sort and filter by criteria
    df2_mod = df2.sort_values(by=['street_name_ratio', 'street_number_ratio', 'PRIMARY_SECONDARY'], ascending=[False, False, True])

    # Return the best match if available
    if not df2_mod.empty:
        best_primary = df2_mod.iloc[0]
        return (best_primary.name, best_primary['LATITUDE'], best_primary['LONGITUDE'], best_primary['STREET_NAME'], 
                best_primary['Suffix'], best_primary['StreetNoMin'], best_primary['StreetNoMax'], 
                best_primary['street_name_ratio'], best_primary['street_number_ratio'], 
                best_primary['PRIMARY_SECONDARY'], best_primary['ADDRESS_DETAIL_PID'], best_primary['suffix_match'])
    else:
        return (None, None, None, None, None, None, None, 0, 0, None, None, None)

def process_single_record(index, df1_row, df2):
    # Extract the filtered df2 for the specific tenant_postcode
    df2_filtered = df2.loc[index] if index in df2.index else df2
    
    # Apply the check_streetno_match function
    pin, lat, long, st_name, suffix, st_no_min, st_no_max, street_name_ratio, street_number_ratio, primary, ID, suffix_ratio = check_streetno_match(df1_row, df2_filtered)
    
    # Return processed record as dictionary
    return {
        'tenant_postcode': index,
        **df1_row.to_dict(),
        'GNAF_postcode': pin,
        'lat': lat,
        'long': long,
        'GNAF_STREET_NAME': st_name,
        'GNAF_Suffix': suffix,
        'GNAF_StreetNoMin': st_no_min,
        'GNAF_StreetNoMax': st_no_max,
        'street_name_ratio': street_name_ratio,
        'street_number_ratio': street_number_ratio,
        'suffix_ratio': suffix_ratio,
        'PRIMARY_SECONDARY': primary,
        'ADDRESS_DETAIL_PID': ID
    }

def process_all_records(df1, df2):
    # Parallel processing for each row in df1
    results = df1.parallel_apply(lambda row: process_single_record(row.name, row, df2), axis=1)
    
    # Convert results to DataFrame
    result_df = pd.DataFrame(results.tolist())
    return result_df
#%%
result_df = process_all_records(target, df)
#%%
result_df.shape
#%%
result_df.head()
#%%
result_df.to_csv("VIC_lat_long_mapped.csv",index=False)
#%%
filtered = result_df[((result_df['street_number_ratio'] >= 80) & (result_df['street_name_ratio'] >= 85) & result_df['suffix_ratio'])]
#%%
filtered.shape
#%%
filtered.to_csv("VIC_lat_long_mapped.csv",index=False)