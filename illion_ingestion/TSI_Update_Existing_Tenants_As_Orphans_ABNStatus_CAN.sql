CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `TSI_Update_Existing_Tenants_As_Orphans_ABNStatus_CAN`(IN P_BatchIDs varchar(10000))
BEGIN

    -- Step 1: Create a temporary table to store raw batch IDs from input
    DROP TEMPORARY TABLE IF EXISTS RawIDs_T;
    CREATE TEMPORARY TABLE RawIDs_T(RawIDs TEXT);
    INSERT INTO RawIDs_T VALUES(P_BatchIDs);

    -- Step 2: Create a temporary table for Batch IDs
    DROP TEMPORARY TABLE IF EXISTS BatchIDs_T;
    CREATE TEMPORARY TABLE BatchIDs_T (Var INT PRIMARY KEY);

    -- Insert Batch IDs into the temporary table if they are not NULL
    IF P_BatchIDs IS NOT NULL THEN
        SET @sql = CONCAT("INSERT INTO BatchIDs_T (Var) VALUES ('", REPLACE((SELECT GROUP_CONCAT(DISTINCT RawIDs) AS data FROM RawIDs_T), ",", "'),('"), "');");
        PREPARE stmt1 FROM @sql;
        EXECUTE stmt1;
    END IF;

    -- Step 3: Get all Illion records with Tenant_Stage_ID that are not processed
    DROP TEMPORARY TABLE IF EXISTS tempRaw_illion_Updated_Tenants;
    CREATE TEMPORARY TABLE tempRaw_illion_Updated_Tenants AS (
        SELECT * FROM Empirical_DataIngestion.Illion_existing_2024_09_01
        WHERE (IsProcessed = 0 OR IsProcessed IS NULL) 
        AND BatchID IN (SELECT * FROM BatchIDs_T) AND (ABNStatus = 'CAN' or MarketableFlag=0)
    );
    
	CREATE INDEX idx_Tenant_Stage_Id_temp ON tempRaw_illion_Updated_Tenants (TS_Tenant_Stage_Id);
	CREATE INDEX idx_VendorId_temp ON tempRaw_illion_Updated_Tenants (VendorID);

    -- Step 4: Update Tenant Stage records with the data from Illion
    UPDATE Empirical_DataStage.Tenants_Stage AS TS
    INNER JOIN tempRaw_illion_Updated_Tenants AS t 
        ON TS.VendorID = t.VendorID AND TS.Tenant_Stage_Id = t.TS_Tenant_Stage_Id
    SET 
        TS.BatchID = t.BatchID,
        -- TS.PropertyID = t.PropertyID,
        TS.TenantName = t.TenantName,
        TS.Address1 = t.Address1,
        TS.Address2 = t.Address2,
        TS.City = t.City,
        TS.State = t.State,
        TS.StateAbbr = t.StateAbbr,
        TS.CountryCode = t.CountryCode,
        TS.PostalCode = t.PostalCode,
        TS.NationalID = t.NationalID,
        TS.OfficePhone = t.OfficePhone,
        TS.Fax = t.Fax,
        TS.CEOName = t.CEOName,
        TS.CEOTitle = t.CEOTitle,
        TS.LineOfBusiness = t.LineOfBusiness,
        TS.SICCode = t.SICCode,
        TS.Revenue = t.Revenue,
        TS.EmployeesAtLocation = t.EmployeesAtLocation,
        TS.EmployeeCount = t.EmployeeCount,
        TS.LegalStatus = t.LegalStatus,
        TS.SubsidiaryCode = t.SubsidiaryCode,
        TS.Latitude = t.Latitude,
        TS.Longitude = t.Longitude,
        TS.NAICSCode = t.NAICSCode,
        TS.NACECode = t.NACECode,
        TS.Email = t.Email,
        TS.WebsiteURL = t.WebsiteURL,
        TS.ASICEntityStatus = t.ASICEntityStatus,
        TS.ASICEntityType = t.ASICEntityType,
        TS.ASICEntityClass = t.ASICEntityClass,
        TS.ABNStatus = t.ABNStatus,
        TS.ABN_StatusFromDate = t.ABN_StatusFromDate,
        TS.GST_Status = t.GST_Status,
        TS.GST_StatusFromDate = t.GST_StatusFromDate,
        TS.RegistrationOrIncorporationDate = t.RegistrationOrIncorporationDate,
        TS.EntityAge = t.EntityAge,
        TS.EmployeeIndicator = t.EmployeeIndicator,
        TS.RevenueIndicator = t.RevenueIndicator,
        TS.HQ_ID = t.HQ_ID,
        TS.HQ_CompanyName = t.HQ_CompanyName,
        TS.NumberofMembersinHierarchy = t.NumberofMembersinHierarchy,
        TS.ImmediateParentDUNS = t.ImmediateParentDUNS,
        TS.ImmediateParentName = t.ImmediateParentName,
        TS.ImmediateParentCountry = t.ImmediateParentCountry,
        TS.DomesticParentDUNS = t.DomesticParentDUNS,
        TS.DomesticParentName = t.DomesticParentName,
        TS.DomesticParentCountry = t.DomesticParentCountry,
        TS.GlobalUltimateParentDUNS = t.GlobalUltimateParentDUNS,
        TS.GlobalUltimateParentName = t.GlobalUltimateParentName,
        TS.GlobalUltimateParentCountry = t.GlobalUltimateParentCountry,
        TS.PrimarySICDesc = t.PrimarySICDesc,
        TS.PrimarySIC3Digit = t.PrimarySIC3Digit,
        TS.PrimarySIC3DigitDesc = t.PrimarySIC3DigitDesc,
        TS.PrimarySIC2Digit = t.PrimarySIC2Digit,
        TS.PrimarySIC2DigitDesc = t.PrimarySIC2DigitDesc,
        TS.PrimarySICDivision = t.PrimarySICDivision,
        TS.PrimarySICDivisionDesc = t.PrimarySICDivisionDesc,
        TS.ANZSICCode = t.ANZSICCode,
        TS.ModifiedDate = CURRENT_TIME(),
        TS.ModifiedBy = 22,
        TS.IsProcessed = 1
        TS.MarketableFlag = t.MarketableFlag,
        TS.BranchID = null,
        TS.ParentCompanyID = null,
        TS.ConfirmedTenantID = null,
        TS.IsHidden=1,
        TS.HidedBy=22,
        TS.HidedDate=CURRENT_TIME(),
        TS.HideReasonID=12,
        TS.HideReasonComments='Tenant ABN is cancelled.';

	-- Step 5: Update Tenant_Stage_ID in the Illion Update Stage table and mark as processed
    UPDATE Empirical_DataIngestion.Illion_existing_2024_09_01 AS a
    INNER JOIN tempRaw_illion_Updated_Tenants AS b 
        ON a.VendorID = b.VendorID AND a.TS_Tenant_Stage_Id = b.Tenant_Stage_Id
    SET 
        a.IsProcessed = 1
    WHERE a.BatchID IN (SELECT * FROM BatchIDs_T);
    
    -- Step 6: Ingest ACN, ABN in National Identifiers Table
	DROP temporary TABLE IF EXISTS NationalIdentifiersExists;
    CREATE temporary TABLE NationalIdentifiersExists(
        SELECT ABN,ACN,TS_Tenant_Stage_ID FROM Empirical_DataIngestion.Illion_existing_2024_09_01 
        WHERE BatchID IN (SELECT * FROM BatchIDs_T) AND IsProcessed=1 AND TS_Tenant_Stage_ID IS NOT NULL AND (ABN IS NOT NULL OR ACN IS NOT NULL)
        AND TS_Tenant_Stage_ID IN (SELECT Tenant_Stage_ID FROM Empirical_Prod.NationalIdentifiers)
    );
        
    UPDATE Empirical_Prod.NationalIdentifiers ni INNER JOIN NationalIdentifiersExists nie ON ni.Tenant_Stage_ID=nie.TS_Tenant_Stage_ID
    SET ni.ABN=nie.ABN,ni.ACN=nie.ACN;
    
    INSERT INTO Empirical_Prod.NationalIdentifiers (ABN,ACN,Tenant_Stage_ID) 
    SELECT ABN,ACN,TS_Tenant_Stage_ID FROM Empirical_DataIngestion.Illion_existing_2024_09_01 
        WHERE BatchID IN (SELECT * FROM BatchIDs_T) AND IsProcessed=1 AND TS_Tenant_Stage_ID IS NOT NULL AND (ABN IS NOT NULL OR ACN IS NOT NULL)
        AND TS_Tenant_Stage_ID NOT IN (SELECT Tenant_Stage_ID FROM NationalIdentifiersExists);
        
END
