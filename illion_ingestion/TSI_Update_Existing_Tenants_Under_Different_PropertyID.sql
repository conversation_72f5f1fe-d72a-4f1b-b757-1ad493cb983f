CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `TSI_Update_Existing_Tenants_Under_Different_PropertyID`(IN P_BatchIDs varchar(10000))
BEGIN

	DECLARE JSONTEXT text;

    -- Step 1: Create a temporary table to store raw batch IDs from input
    DROP TEMPORARY TABLE IF EXISTS RawIDs_T;
    CREATE TEMPORARY TABLE RawIDs_T(RawIDs text);
    INSERT INTO RawIDs_T VALUES(P_BatchIDs);

    -- Step 2: Create a temporary table for Batch IDs
    DROP TEMPORARY TABLE IF EXISTS BatchIDs_T;
    CREATE TEMPORARY TABLE BatchIDs_T (Var INT PRIMARY KEY);

    -- Insert Batch IDs into the temporary table if they are not NULL
    IF P_BatchIDs IS NOT NULL THEN
        SET @sql = CONCAT("INSERT INTO BatchIDs_T (Var) VALUES ('", REPLACE((SELECT GROUP_CONCAT(DISTINCT RawIDs) AS data FROM RawIDs_T), ",", "'),('"),"');");
        PREPARE stmt1 FROM @sql;
        EXECUTE stmt1;
    END IF;

    -- Step 3: Get all Illion records with Tenant_Stage_ID that are not processed
    DROP TEMPORARY TABLE IF EXISTS tempRaw_Confirmed_Tenants;
    CREATE TEMPORARY TABLE tempRaw_Confirmed_Tenants AS (
        SELECT * FROM Empirical_DataIngestion.Updation_Illion_PID_Mapped_2024_09_01
        WHERE (IsProcessed = 0 OR IsProcessed IS NULL) 
        AND BatchID IN (SELECT * FROM BatchIDs_T)
    );
    /*
    select * from tempRaw_Confirmed_Tenants;
    select * from Empirical_DataStage.Tenants_Stage AS TS
    INNER JOIN tempRaw_Confirmed_Tenants AS t 
        ON TS.VendorID = t.VendorID 
        AND TS.Tenant_Stage_Id = t.Tenant_Stage_Id;
*/
    -- Step 4: Update Tenant Stage records with the data from Illion
    UPDATE Empirical_DataStage.Tenants_Stage AS TS
    INNER JOIN tempRaw_Confirmed_Tenants AS t 
        ON TS.VendorID = t.VendorID 
        AND TS.Tenant_Stage_Id = t.Tenant_Stage_Id
    SET 
        TS.BatchID = t.BatchID,
        TS.PropertyID = t.PropertyID,
        TS.TenantName = t.TenantName,
        TS.Address1 = t.Address1,
        TS.Address2 = t.Address2,
        TS.City = t.City,
        TS.State = t.State,
        TS.StateAbbr = t.StateAbbr,
        TS.CountryCode = t.CountryCode,
        TS.PostalCode = t.PostalCode,
        TS.NationalID = t.NationalID,
        TS.OfficePhone = t.OfficePhone,
        TS.Fax = t.Fax,
        TS.CEOName = t.CEOName,
        TS.CEOTitle = t.CEOTitle,
        TS.LineOfBusiness = t.LineOfBusiness,
        TS.SICCode = t.SICCode,
        TS.Revenue = t.Revenue,
        TS.EmployeesAtLocation = t.EmployeesAtLocation,
        TS.EmployeeCount = t.EmployeeCount,
        TS.LegalStatus = t.LegalStatus,
        TS.SubsidiaryCode = t.SubsidiaryCode,
        TS.Latitude = t.Latitude,
        TS.Longitude = t.Longitude,
        TS.NAICSCode = t.NAICSCode,
        TS.NACECode = t.NACECode,
        TS.Email = t.Email,
        TS.WebsiteURL = t.WebsiteURL,
        TS.ASICEntityStatus = t.ASICEntityStatus,
        TS.ASICEntityType = t.ASICEntityType,
        TS.ASICEntityClass = t.ASICEntityClass,
        TS.ABNStatus = t.ABNStatus,
        TS.ABN_StatusFromDate = t.ABN_StatusFromDate,
        TS.GST_Status = t.GST_Status,
        TS.GST_StatusFromDate = t.GST_StatusFromDate,
        TS.RegistrationOrIncorporationDate = t.RegistrationOrIncorporationDate,
        TS.EntityAge = t.EntityAge,
        TS.EmployeeIndicator = t.EmployeeIndicator,
        TS.RevenueIndicator = t.RevenueIndicator,
        TS.HQ_ID = t.HQ_ID,
        TS.HQ_CompanyName = t.HQ_CompanyName,
        TS.NumberofMembersinHierarchy = t.NumberofMembersinHierarchy,
        TS.ImmediateParentDUNS = t.ImmediateParentDUNS,
        TS.ImmediateParentName = t.ImmediateParentName,
        TS.ImmediateParentCountry = t.ImmediateParentCountry,
        TS.DomesticParentDUNS = t.DomesticParentDUNS,
        TS.DomesticParentName = t.DomesticParentName,
        TS.DomesticParentCountry = t.DomesticParentCountry,
        TS.GlobalUltimateParentDUNS = t.GlobalUltimateParentDUNS,
        TS.GlobalUltimateParentName = t.GlobalUltimateParentName,
        TS.GlobalUltimateParentCountry = t.GlobalUltimateParentCountry,
        TS.PrimarySICDesc = t.PrimarySICDesc,
        TS.PrimarySIC3Digit = t.PrimarySIC3Digit,
        TS.PrimarySIC3DigitDesc = t.PrimarySIC3DigitDesc,
        TS.PrimarySIC2Digit = t.PrimarySIC2Digit,
        TS.PrimarySIC2DigitDesc = t.PrimarySIC2DigitDesc,
        TS.PrimarySICDivision = t.PrimarySICDivision,
        TS.PrimarySICDivisionDesc = t.PrimarySICDivisionDesc,
        TS.ANZSICCode = t.ANZSICCode,
        TS.ModifiedDate = CURRENT_TIME(),
        TS.ModifiedBy = 22,
        TS.IsProcessed = 1;
        
	-- select 'completed update';

    -- Step 5: Ingest ACN, ABN in National Identifiers Table
	DROP temporary TABLE IF EXISTS NationalIdentifiersExists;
    CREATE temporary TABLE NationalIdentifiersExists(
        SELECT ABN,ACN,Tenant_Stage_ID FROM Empirical_DataIngestion.Updation_Illion_PID_Mapped_2024_09_01 
        WHERE BatchID IN (SELECT * FROM BatchIDs_T) AND IsProcessed=1 AND Tenant_Stage_ID IS NOT NULL AND (ABN IS NOT NULL OR ACN IS NOT NULL)
        AND Tenant_Stage_ID IN (SELECT Tenant_Stage_ID FROM Empirical_Prod.NationalIdentifiers)
    );

    -- select * from NationalIdentifiersExists;
        
    UPDATE Empirical_Prod.NationalIdentifiers ni INNER JOIN NationalIdentifiersExists nie ON ni.Tenant_Stage_ID=nie.Tenant_Stage_ID
    SET ni.ABN=nie.ABN,ni.ACN=nie.ACN;
/*
    SELECT ABN,ACN,Tenant_Stage_ID FROM Empirical_DataIngestion.Updation_Illion_PID_Mapped_2024_09_01 
        WHERE BatchID IN (SELECT * FROM BatchIDs_T) AND IsProcessed=1 AND Tenant_Stage_ID IS NOT NULL AND (ABN IS NOT NULL OR ACN IS NOT NULL)
        AND Tenant_Stage_ID NOT IN (SELECT Tenant_Stage_ID FROM NationalIdentifiersExists);
*/
    INSERT INTO Empirical_Prod.NationalIdentifiers (ABN,ACN,Tenant_Stage_ID) 
    SELECT ABN,ACN,Tenant_Stage_ID FROM Empirical_DataIngestion.Updation_Illion_PID_Mapped_2024_09_01 
        WHERE BatchID IN (SELECT * FROM BatchIDs_T) AND IsProcessed=1 AND Tenant_Stage_ID IS NOT NULL AND (ABN IS NOT NULL OR ACN IS NOT NULL)
        AND Tenant_Stage_ID NOT IN (SELECT Tenant_Stage_ID FROM NationalIdentifiersExists);

    -- Step 6: Get Tenant_Stage records of updated Illion records
    DROP TEMPORARY TABLE IF EXISTS tempTenant_Stage;
    CREATE TEMPORARY TABLE tempTenant_Stage AS (
        SELECT ts.*,ct.ACN,ct.ABN FROM Empirical_DataStage.Tenants_Stage ts inner join tempRaw_Confirmed_Tenants ct on 
        ts.VendorID =ct.VendorID and ts.Tenant_Stage_Id=ct.Tenant_Stage_Id
    );
    -- select * from tempTenant_Stage;

    -- Step 7: Get confirmed tenants from SuiteTenant table where property has changed
    DROP TEMPORARY TABLE IF EXISTS tempMerged_View_Confirmed_Tenants;
    CREATE TEMPORARY TABLE tempMerged_View_Confirmed_Tenants AS (
        SELECT ct.ConfirmedTenantID, ts.Tenant_Stage_Id,ts.TenantName,a.Address1,a.Address2,a.CityID,a.StateID,a.ZipCode,a.CountryID,ts.ABN,ts.ACN
        FROM Empirical_Tenants.ConfirmedTenants ct 
        INNER JOIN tempTenant_Stage ts ON ts.BranchID = ct.CompanyID
        INNER JOIN Empirical_Prod.Address a ON a.ParentID=ts.PropertyID AND a.ParentTableID=1 AND a.Sequence=1 AND a.IsActive=1
        WHERE ct.ProviderID = 5
    );
    -- select * from tempMerged_View_Confirmed_Tenants;

    select min(Tenant_Stage_Id) INTO @minTenantStageID from tempMerged_View_Confirmed_Tenants;

    WHILE @minTenantStageID is not null do
    -- select @minTenantStageID;

        SET @NewConfirmedTenantID=null,@NewTenantName=null,@NewAddress1=null,@NewAddress2=null,@NewCityID=null,@NewStateID=null,@NewZipCode=null,@NewCountryID=null,@ACN=null,@ABN=null;

        SELECT 
        ConfirmedTenantID,TenantName,Address1,Address2,CityID,StateID,ZipCode,CountryID,ACN,ABN
        INTO
        @NewConfirmedTenantID,@NewTenantName,@NewAddress1,@NewAddress2,@NewCityID,@NewStateID,@NewZipCode,@NewCountryID,@ACN,@ABN
        from tempMerged_View_Confirmed_Tenants Where Tenant_Stage_Id=@minTenantStageID;

        -- select @NewConfirmedTenantID,@NewTenantName,@NewAddress1,@NewAddress2,@NewCityID,@NewStateID,@NewZipCode,@NewCountryID,@ACN,@ABN;

        -- temporary list to hold changed fiels for auditing
        DROP TEMPORARY TABLE IF EXISTS tempChangeLog;
        CREATE TEMPORARY TABLE tempChangeLog
        (
			ConfirmedTenantID INT,
            ProviderID INT,
            Source_Tenant_Record_ID INT,
            FieldID INT            	
        );
        
        -- Prepare Change Log fields by comparing with merged view fields. if merged view field is missing data and empirical does, then update
        -- merged view with empirical data and track change log.
		SET @ConfirmedTenantID=null,@TenantName=null,@Address1=null,@Address2=null,@CityID=null,@StateID=null,@ZipCode=null,@CountryID=null;

        SELECT 
        ConfirmedTenantID,TenantName,Address1,Address2,CityID,StateID,ZipCode,CountryID,CompanyID
        INTO
        @ConfirmedTenantID,@TenantName,@Address1,@Address2,@CityID,@StateID,@ZipCode,@CountryID,@CompanyID
        from Empirical_Tenants.ConfirmedTenants Where ConfirmedTenantID=@NewConfirmedTenantID and ProviderID=5;

        -- select @ConfirmedTenantID,@TenantName,@Address1,@Address2,@CityID,@StateID,@ZipCode,@CountryID,@CompanyID;


		Update Empirical_Tenants.ConfirmedTenants 
        Set Address1=@NewAddress1,Address2=null,FloorNumber=null,CityID=@NewCityID,StateID=@NewStateID,CountryID=@NewCountryID,ModifiedDate=current_timestamp(),ModifiedBy=22 
        Where CompanyID=@CompanyID and ProviderID=1;

		 IF @ConfirmedTenantID IS NOT NULL THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,1,@minTenantStageID,5 -- Provider 1 , Getting from Property
            UNION
            SELECT @ConfirmedTenantID,1,@minTenantStageID,7
            UNION
            SELECT @ConfirmedTenantID,1,@minTenantStageID,8
            UNION
			SELECT @ConfirmedTenantID,1,@minTenantStageID,9
            UNION
            SELECT @ConfirmedTenantID,1,@minTenantStageID,10
            UNION
			SELECT @ConfirmedTenantID,1,@minTenantStageID,11
            UNION
			SELECT @ConfirmedTenantID,1,@minTenantStageID,12
            UNION
            SELECT @ConfirmedTenantID,1,@minTenantStageID,13;
            Update Empirical_Tenants.ConfirmedTenants Set Address1=@NewAddress1,Address2=null,FloorNumber=null,CityID=@NewCityID,StateID=@NewStateID,ZipCode=@NewZipCode,CountryID=@NewCountryID,ModifiedDate=current_timestamp(),ModifiedBy=22 Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;

    -- Prepare JSON object from change log to pass it to the change log save SP        

			SELECT 
				CONCAT('[', FirstPass, ']') INTO JSONTEXT
			FROM
				(SELECT 
					GROUP_CONCAT('{', Jason1, '}'
							SEPARATOR ',') AS FirstPass
				FROM
					(
						SELECT 
						CONCAT('"ConfirmedTenantID":', '"', CL.ConfirmedTenantID, '"', ',',
								'"ProviderID":', '"', CL.ProviderID, '"', ',',
                                '"Source_Tenant_Record_ID":', '"', CL.Source_Tenant_Record_ID, '"', ',',
                                '"FieldID":', '"', CL.FieldID, '"'
							   ) AS Jason1
				FROM
					tempChangeLog CL) AS Jason2) AS Jason3;
        -- select JSONTEXT;
		if JSONTEXT IS NOT NULL THEN
		 --  save change log
        -- select JSONTEXT;
		  CALL Empirical_Prod.CRE_Property_Tenant_Matrix_Save_MergedView_Changelog_1(JSONTEXT,22);
		end if;
        if @ACN IS NOT NULL or @ABN IS NOT NULL THEN
         -- select @ACN,@ABN;
		  Call Empirical_Prod.CRE_NationalIdentifiers_Save(@CompanyID,@minTenantStageID,@ConfirmedTenantID,@ACN,@ABN);
		end if;
        
    select min(Tenant_Stage_Id) INTO @minTenantStageID from tempMerged_View_Confirmed_Tenants where Tenant_Stage_Id>@minTenantStageID;

    end while;
    -- select 'end while';

    -- Step 7: Get confirmed tenants from SuiteTenant table where property has changed
    DROP TEMPORARY TABLE IF EXISTS tempConfirmed_Tenants;
    CREATE TEMPORARY TABLE tempConfirmed_Tenants AS (
        SELECT st.*, ts.PropertyID AS NewPropertyID 
        FROM Empirical_Prod.SuiteTenant st
        INNER JOIN Empirical_Tenants.ConfirmedTenants ct 
            ON st.ConfirmedTenantID = ct.ConfirmedTenantID 
            AND ct.ProviderID = 1
        INNER JOIN tempTenant_Stage ts 
            ON ts.BranchID = ct.CompanyID
        WHERE st.IsActive = 1 
        AND st.PropertyID != ts.PropertyID
    );
    /*
    select * from tempConfirmed_Tenants;
    select * from Empirical_Prod.SuiteTenant 
    WHERE SuiteTenantID IN (SELECT SuiteTenantID FROM tempConfirmed_Tenants);
*/
    -- Step 8: Deactivate old SuiteTenant records where property has changed
    UPDATE Empirical_Prod.SuiteTenant 
    SET IsActive = 0, TenantStatusID = 2, ModifiedBy = 22, ModifiedDate = NOW() 
    WHERE SuiteTenantID IN (SELECT SuiteTenantID FROM tempConfirmed_Tenants);

    -- Step 9: Insert new SuiteTenant records for updated tenants
    DROP TEMPORARY TABLE IF EXISTS Suite_Tenant_To_be_Ingested;
    CREATE TEMPORARY TABLE Suite_Tenant_To_be_Ingested AS (
        SELECT 
            NULL AS SuiteTenantID,
            SuiteID,
            FloorID,
            NewPropertyID AS PropertyID,
            ConfirmedTenantID,
            1 AS IsActive,
            22 AS CreatedBy,
            NOW() AS CreatedDate,
            22 AS ModifiedBy,
            NOW() AS ModifiedDate,
            MoveInDate,
            MoveOutDate,
            1 AS TenantStatusID,
            Comments 
        FROM tempConfirmed_Tenants
    );
    -- select * from Suite_Tenant_To_be_Ingested;

    INSERT INTO Empirical_Prod.SuiteTenant 
    SELECT * FROM Suite_Tenant_To_be_Ingested;
/*
    select * from Empirical_DataIngestion.Updation_Illion_PID_Mapped_2024_09_01 AS a
    INNER JOIN Empirical_DataStage.Tenants_Stage AS b 
        ON a.VendorID = b.VendorID and a.Tenant_Stage_ID = b.Tenant_Stage_ID
    WHERE b.BatchID IN (SELECT * FROM BatchIDs_T);
*/
    -- Step 10: Update the Tenant_Stage_ID in the Illion update stage table to reflect the changes
    UPDATE Empirical_DataIngestion.Updation_Illion_PID_Mapped_2024_09_01 AS a
    INNER JOIN Empirical_DataStage.Tenants_Stage AS b 
        ON a.VendorID = b.VendorID and a.Tenant_Stage_ID = b.Tenant_Stage_ID
    SET a.IsProcessed = 1 
    WHERE b.BatchID IN (SELECT * FROM BatchIDs_T);

END