-- Reset BatchID and IsProcessed fields in preparation for new updates.
update Empirical_DataIngestion.Updation_Illion_2024_09_01 set BatchID=null,IsProcessed=0;

-- Add columns PropertyID,IsAutoEnrichmentDone and ID, set ID as primary key.
ALTER TABLE `Empirical_DataIngestion`.`Updation_Illion_2024_09_01` 
ADD COLUMN `PropertyID` INT Default null AFTER `Tenant_Stage_Id`,
ADD COLUMN `ID` INT NOT NULL AUTO_INCREMENT AFTER `Tenant_Stage_Id`,
ADD PRIMARY KEY (`ID`);
;

ALTER TABLE `Empirical_DataIngestion`.`Updation_Illion_2024_09_01` 
ADD COLUMN `IsAutoEnrichmentDone` TINYINT(1) Default null AFTER `PropertyID`
;

ALTER TABLE `Empirical_DataIngestion`.`Updation_Illion_2024_09_01` 
CHANGE COLUMN `VendorID` `VendorID` VARCHAR(50) NULL DEFAULT NULL 
;

-- Add indexes for VendorID,Tenant_Stage_ID and BatchID
ALTER TABLE `Empirical_DataIngestion`.`Updation_Illion_2024_09_01` 
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE,
ADD INDEX `idx_Tenant_stage_id` (`TS_Tenant_Stage_Id` ASC) VISIBLE,
ADD INDEX `idx_Batch_id` (`BatchID` ASC) VISIBLE;
;

select BatchId,count(*),IsProcessed from Empirical_DataIngestion.Updation_Illion_2024_09_01 group by BatchID,IsProcessed;

-- Set batchID 99999 for tenants with address change
update Empirical_DataIngestion.Updation_Illion_2024_09_01 set BatchID=99999 where Address_Status='change';

-- temporary table of distinct BranchIDs for tenants we are updating
drop temporary table if exists BranchesOfIllion;
create temporary table BranchesOfIllion(
	select distinct BranchID,Tenant_Stage_Id from Empirical_DataStage.Tenants_Stage where Tenant_Stage_Id IN (
		SELECT TS_Tenant_Stage_Id FROM Empirical_DataIngestion.Updation_Illion_2024_09_01 
    )
);
    
-- temporary table of ConfirmedTenantIDs of distinct BranchIDs for tenants we are updating
drop temporary table if exists tempCompanyCT;
create temporary table tempCompanyCT(
	select ct.*,bi.Tenant_Stage_Id from Empirical_Tenants.ConfirmedTenants ct inner join BranchesOfIllion bi on ct.CompanyID=bi.BranchID and ProviderID=1 group by ConfirmedTenantID
);
    
-- temporary table of tenant IDs to do auto enrichment
drop temporary table if exists tempIDs;
create temporary table tempIDs(
    select ID from Empirical_DataIngestion.Updation_Illion_2024_09_01 where TS_Tenant_Stage_Id in (select Tenant_Stage_Id from tempCompanyCT) and (BatchID!=99999 or BatchID is null)
);
    
-- Set batchID 40000 for the tenants for which auto enrichment has to be done
update Empirical_DataIngestion.Updation_Illion_2024_09_01 set BatchID=40000 where ID in (select ID from tempIDs);
    
-- Set different BatchIDs for tenants based on state to which they belong to
UPDATE Empirical_DataIngestion.Updation_Illion_2024_09_01 set BatchID=10000 where State=57 and BatchID is null;
UPDATE Empirical_DataIngestion.Updation_Illion_2024_09_01 set BatchID=20000 where State=58 and BatchID is null;
UPDATE Empirical_DataIngestion.Updation_Illion_2024_09_01 set BatchID=30000 where State=59 and BatchID is null;

CALL Empirical_DataIngestion.TSI_Illion_Stage_Batch(40000,10000);

-- temporary table of tenants_stage to update propertyID in illion updation table
drop temporary table if exists temp1;
create temporary table temp1(
select VendorID,Tenant_Stage_Id,PropertyID from Empirical_DataStage.Tenants_Stage 
where VendorID in (select VendorID from Empirical_DataIngestion.Updation_Illion_2024_09_01 where BatchID=40000) and ProviderID=6
);

CREATE INDEX idx_VendorId_temp ON temp1 (VendorID);

-- Update propertyID for tenants based on vendorID in tenants stage
UPDATE Empirical_DataIngestion.Updation_Illion_2024_09_01 AS a
INNER JOIN temp1 AS b ON a.VendorID = b.VendorID and a.TS_Tenant_Stage_ID = b.Tenant_Stage_ID
SET a.PropertyID = b.PropertyID where BatchID=40000;

        
select * from Empirical_DataIngestion.Updation_Illion_2024_09_01 where BatchID=10000;

select BatchId,count(*),IsProcessed,IsAutoEnrichmentDone from Empirical_DataIngestion.Updation_Illion_2024_09_01 group by BatchID,IsProcessed,IsAutoEnrichmentDone;

CALL Empirical_DataIngestion.TSI_Update_Existing_Tenants_Under_Same_PropertyID(10000);
CALL Empirical_DataIngestion.TSI_Tenant_Auto_Enrichment_For_Already_Grouped_Tenants(10000);

-- sample call via pm2 script
"pm2 start Illion_Updation.js --name illion-updation-10000 -- 10000 0" -- arguments are batchID and isAutoEnrichmentRequired(0 or 1)
