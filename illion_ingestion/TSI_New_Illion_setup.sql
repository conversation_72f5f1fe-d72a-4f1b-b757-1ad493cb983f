-- Step 1: Alter the table structure to add a primary key, indexes, and modify columns
ALTER TABLE Empirical_DataIngestion.Illion_New_2024_09_01
  ADD COLUMN ID INT NOT NULL AUTO_INCREMENT FIRST,     -- Add 'ID' column as a primary key
  ADD PRIMARY KEY (ID),                                -- Set 'ID' as the primary key
  ADD INDEX idx_BatchID (BatchID ASC) invisible,       -- Add an invisible index on 'BatchID'
  ADD INDEX idx_IsProcess (IsProcessed ASC) invisible, -- Add an invisible index on 'IsProcessed'
  MODIFY COLUMN VendorID VARCHAR(200),                -- Modify 'VendorID' to VARCHAR(1000)
  ADD INDEX idx_VendorID (VendorID ASC) VISIBLE;       -- Add a visible index on 'VendorID'
  
ALTER TABLE Empirical_DataIngestion.Illion_New_PID_Mapped_2024_09_01
  MODIFY COLUMN VendorID VARCHAR(200), 
  ADD INDEX idx_VendorID (VendorID ASC) VISIBLE;       -- Add a visible index on 'VendorID'

-- Step 2: Verify the total record count in the main table
SELECT COUNT(*) 
FROM Empirical_DataIngestion.Illion_New_2024_09_01; -- 289018

-- Step 3: Reset 'BatchID' and 'IsProcessed' fields to NULL
UPDATE Empirical_DataIngestion.Illion_New_2024_09_01 
SET BatchID = NULL,
    IsProcessed = NULL;

-- Step 4: Count records grouped by 'BatchID' to verify the distribution
SELECT BatchID, COUNT(*) 
FROM Empirical_DataIngestion.Illion_New_2024_09_01 
GROUP BY BatchID;

-- Step 5: Count records where 'BatchID' is NULL and latitude/longitude are missing
SELECT COUNT(*)
FROM Empirical_DataIngestion.Illion_New_2024_09_01
WHERE BatchID IS NULL
  AND Latitude IS NULL
  AND Longitude IS NULL; -- 4138

-- Step 6: Set 'BatchID' to 0 for records missing latitude and longitude
UPDATE Empirical_DataIngestion.Illion_New_2024_09_01 
SET BatchID = 0
WHERE Latitude IS NULL AND Longitude IS NULL;

-- Step 7: Verify updated record count grouped by 'BatchID'
SELECT BatchID, COUNT(*) 
FROM Empirical_DataIngestion.Illion_New_2024_09_01 
GROUP BY BatchID;
  
-- Step 8: Count matching records in both tables based on conditions
SELECT COUNT(*) 
FROM Empirical_DataIngestion.Illion_New_2024_09_01 I
INNER JOIN Empirical_DataIngestion.Illion_New_PID_Mapped_2024_09_01 N 
  ON N.VendorID = I.VendorID
WHERE I.BatchID IS NULL 
  AND N.No_of_property_matches_with_range = 1 
  AND N.StreetNumberMatchScore >= 90 
  AND N.StreetNameScore >= 88; -- 79009

-- Step 9: Update 'BatchID' for matching records based on conditions
UPDATE Empirical_DataIngestion.Illion_New_2024_09_01 AS I
INNER JOIN Empirical_DataIngestion.Illion_New_PID_Mapped_2024_09_01 AS N
  ON N.VendorID = I.VendorID
SET I.BatchID = 1
WHERE I.BatchID IS NULL 
  AND N.No_of_property_matches_with_range = 1
  AND N.StreetNumberMatchScore >= 90
  AND N.StreetNameScore >= 88;

-- Step 10: Verify updated record count grouped by 'BatchID'
SELECT BatchID, COUNT(*) 
FROM Empirical_DataIngestion.Illion_New_2024_09_01 
GROUP BY BatchID;

-- Step 11: Update 'PropertyID' in the main table for records with 'BatchID' = 1
UPDATE Empirical_DataIngestion.Illion_New_2024_09_01 AS main
JOIN Empirical_DataIngestion.Illion_New_PID_Mapped_2024_09_01 AS PID
  ON main.VendorID = PID.VendorID
SET main.PropertyID = PID.PropertyID
WHERE main.BatchID = 1
  AND PID.No_of_property_matches_with_range = 1
  AND PID.StreetNumberMatchScore >= 90
  AND PID.StreetNameScore >= 88;

-- Step 12: Set 'BatchID' = 2 for remaining unmatched records (orphan tenants)
UPDATE Empirical_DataIngestion.Illion_New_2024_09_01
SET BatchID = 2
WHERE BatchID IS NULL;

-- Verify 'BatchID' distribution
SELECT BatchID, COUNT(*) 
FROM Empirical_DataIngestion.Illion_New_2024_09_01 
GROUP BY BatchID;

-- Step : Further processing of records in 'BatchID' = 0
-- - After geocoding, update 'BatchID' = 0 records with latitude and longitude.
-- - Assign 'PropertyID' to 'BatchID' = 0 records if available.
-- - Ingest 'BatchID' = 0 records if both latitude and longitude are populated.

-- Step 13: Bronze Ingestion
UPDATE Empirical_DataIngestion.Illion_New_2024_09_01 
SET BatchID = 5 
WHERE BatchID = 1 AND NationalID IS NOT NULL LIMIT 2;

SELECT * 
FROM Empirical_DataIngestion.Illion_New_2024_09_01 
WHERE BatchID = 5;

CALL Empirical_DataIngestion.TSI_Ingest_NewTenants_Into_ALSystem(5, 2);

-- Step 14: Bronze all ingestion
SELECT COUNT(*) 
FROM Empirical_DataIngestion.Illion_New_2024_09_01 
WHERE BatchID = 1;

CALL Empirical_DataIngestion.TSI_Ingest_NewTenants_Into_ALSystem(1, 79005);

-- Step 15: Orphans all ingestion
SELECT COUNT(*) 
FROM Empirical_DataIngestion.Illion_New_2024_09_01 
WHERE BatchID = 2;

CALL Empirical_DataIngestion.TSI_Ingest_NewTenants_Into_ALSystem(2, 205871);

-- Step 18: Verification Data
SELECT * 
FROM Empirical_DataIngestion.Illion_New_2024_09_01 
WHERE BatchID = 1 LIMIT 100;

