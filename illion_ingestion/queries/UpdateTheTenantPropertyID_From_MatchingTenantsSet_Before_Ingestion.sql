ALTER TABLE `Empirical_DataIngestion`.`Tenant_Stage_Illion_2024_09_01` 
ADD COLUMN `ID` INT NOT NULL AUTO_INCREMENT FIRST,
ADD PRIMARY KEY (`ID`),
ADD INDEX `idx_BatchID` (`BatchID` ASC) INVISIBLE,
ADD INDEX `idx_IsProcess` (`IsProcessed` ASC) INVISIBLE,
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;
;

ALTER TABLE `Empirical_DataIngestion`.`Tenant_Stage_Illion_2024_09_01_PID_Matched` 
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;
;

select count(*),VendorID from Empirical_DataIngestion.Tenant_Stage_Illion_2024_09_01 ;-- group by Vendor<PERSON> having count(*)>1; -- 242
select count(*) from Empirical_DataIngestion.Tenant_Stage_Illion_2024_09_01_PID_Matched;

drop temporary table if exists tenantPropertyMatch;
create temporary table tenantPropertyMatch
select distinct tsi.ID,tsi.VendorID,tsi.TenantName,pidmatch.PropertyID,pidmatch.Sequence,tsi.StateAbbr 
from Empirical_DataIngestion.Tenant_Stage_Illion_2024_09_01 tsi
inner join Empirical_DataIngestion.Tenant_Stage_Illion_2024_09_01_PID_Matched pidmatch on tsi.VendorID=pidmatch.VendorID
group by tsi.VendorID having count(*)=1;

-- Create Indexes on the Temporary Table
CREATE INDEX idx_VendorID_Illon ON tenantPropertyMatch (VendorID);
CREATE INDEX idx_ID_Illon ON tenantPropertyMatch (ID);

/* Updating Base Table with Matched Property IDs*/
update Empirical_DataIngestion.Tenant_Stage_Illion_2024_09_01 tsp 
inner join tenantPropertyMatch pidmatch on tsp.VendorID=pidmatch.VendorID and tsp.ID=pidmatch.ID
set tsp.PropertyID=pidmatch.PropertyID;

-- batch making
update Empirical_DataIngestion.Tenant_Stage_Illion_2024_09_01 set BatchID=2 where StateAbbr="NSW";
