CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `TSI_Tenant_Auto_Enrichment_For_Already_Grouped_Tenants`(IN P_BatchID int)
BEGIN

	drop temporary table if exists BranchesOfIllion;
	create temporary table BranchesOfIllion(
		select distinct BranchID,Tenant_Stage_Id from Empirical_DataStage.Tenants_Stage where Tenant_Stage_Id IN (
			SELECT TS_Tenant_Stage_Id FROM Empirical_DataIngestion.Updation_Illion_2024_09_01 
            where BatchID=P_BatchID and (IsAutoEnrichmentDone=0 or IsAutoEnrichmentDone is null)
            )
	);
    
    drop temporary table if exists tempTS;
	create temporary table tempTS(
		select count(*) as confirmations,BranchID from Empirical_DataStage.Tenants_Stage where BranchID IN (
			SELECT BranchID FROM BranchesOfIllion) group by BranchID
	);
    
	drop temporary table if exists tempCompanyCT;
	create temporary table tempCompanyCT(
		select ct.*,bi.Tenant_Stage_Id from Empirical_Tenants.ConfirmedTenants ct inner join BranchesOfIllion bi on ct.CompanyID=bi.BranchID and ProviderID=1 group by ConfirmedTenantID
	);
    
    select min(ConfirmedTenantID) into @minConfirmedtenantID from tempCompanyCT;
    
    while @minConfirmedTenantID is not null do

    set @Tenant_StageID=null;

		select Tenant_Stage_Id into @Tenant_StageID from tempCompanyCT where ConfirmedTenantID=@minConfirmedtenantID;

		call TSI_Tenant_Auto_Enrichment(@minConfirmedTenantID,10,22);

        update Empirical_DataIngestion.Updation_Illion_2024_09_01 set IsAutoEnrichmentDone=1
            where TS_Tenant_Stage_Id=@Tenant_StageID and BatchID=P_BatchID;
        select min(ConfirmedTenantID) into @minConfirmedtenantID from tempCompanyCT where ConfirmedTenantID>@minConfirmedtenantID;
        
	end while;

END