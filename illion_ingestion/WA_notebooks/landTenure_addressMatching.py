import pandas as pd
import geopandas as gpd
from shapely import Point
from fuzzywuzzy import fuzz
import re
from pandarallel import pandarallel

# Initialize parallel processing
pandarallel.initialize(progress_bar=True, nb_workers=10)

# Set pandas display options
pd.set_option("display.max_columns", None)
pd.set_option("display.max_rows", None)

# Load tenant data
wa_df = pd.read_parquet("/home/<USER>/Documents/Arealytics/notebooks/new_illion/final_files_WA/address_enrichment.parquet")
print("Tenant data columns:", wa_df.columns.tolist())
print("WA tenant data shape:", wa_df.shape)

# Create point geometry for tenants
wa_df['geometry'] = wa_df.apply(lambda row: Point(row['Longitude'], row['Latitude']), axis=1)
gdf = gpd.GeoDataFrame(wa_df, geometry='geometry', crs='EPSG:4326')
print("Tenant GeoDataFrame shape:", gdf.shape)

# Load Perth shapefile (polygons)
perth_gdf = gpd.read_file("/home/<USER>/Downloads/Land_Tenure_LGATE_226_WA_GDA2020_Subscription_Geopackage-29Jul/Land_Tenure_LGATE_226_WA_GDA2020_Subscription.gpkg")
perth_gdf = perth_gdf.set_crs(epsg=4326, allow_override=True)
print("Perth shapefile shape:", perth_gdf.shape)
print("Perth shapefile columns:", perth_gdf.columns.tolist())

perth_gdf.head()

perth_gdf['road_number_2'].fillna(perth_gdf['road_number_1'], inplace=True)
perth_gdf = perth_gdf.dropna(subset=['geometry'])
perth_gdf.shape

required_columns = ['road_name', 'road_type', 'locality', 'road_number_1', 'road_number_2', 'postcode']
perth_gdf = perth_gdf.dropna(subset=required_columns, how='all')

perth_gdf.shape

perth_gdf = perth_gdf.rename(columns={
    'road_name': 'StreetName',
    'road_type': 'Suffix',  # Combine road_type and road_suffix in verification
    'locality': 'City',
    'road_number_1': 'StreetNoMin',
    'road_number_2': 'StreetNoMax',
    'postcode': 'PostalCode'})

perth_gdf.shape

gdf.head()



# Spatial join: Match tenant points to Perth polygons
# intersected_gdf = gpd.sjoin(perth_gdf, gdf, predicate='within', how='inner')
intersected_gdf = gpd.sjoin(perth_gdf,gdf,predicate='contains')
print("Intersected GeoDataFrame shape:", intersected_gdf.shape)

# Remove duplicate VendorID entries
intersected_gdf = intersected_gdf.sort_values('VendorID')
intersected_gdf = intersected_gdf[~intersected_gdf['VendorID'].duplicated(keep=False)]
print("Intersected GeoDataFrame after deduplication shape:", intersected_gdf.shape)
intersected_gdf.columns.to_list()

def extract_number(s):
    s = str(s)
    matches = re.findall(r'\d+', s)
    return max(matches, key=len) if matches else None

intersected_gdf['StreetNoMin_left'] = intersected_gdf['StreetNoMin_left'].apply(extract_number)
intersected_gdf['StreetNoMax_left'] = intersected_gdf['StreetNoMax_left'].apply(extract_number)
intersected_gdf['StreetNoMin_right'] = intersected_gdf['StreetNoMin_right'].apply(extract_number)
intersected_gdf['StreetNoMax_right'] = intersected_gdf['StreetNoMax_right'].apply(extract_number)

# Update tenant columns only where null
intersected_gdf['StreetNoMin_right'] = intersected_gdf['StreetNoMin_right'].mask(
    intersected_gdf['StreetNoMin_right'].isna(), intersected_gdf['StreetNoMin_left']
)
intersected_gdf['StreetNoMax_right'] = intersected_gdf['StreetNoMax_right'].mask(
    intersected_gdf['StreetNoMax_right'].isna(), intersected_gdf['StreetNoMax_left']
)
intersected_gdf['StreetName_right'] = intersected_gdf['StreetName_right'].mask(
    intersected_gdf['StreetName_right'].isna(), intersected_gdf['StreetName_left']
)
intersected_gdf['City_right'] = intersected_gdf['City_right'].mask(
    intersected_gdf['City_right'].isna(), intersected_gdf['City_left']
)
intersected_gdf['PostalCode_right'] = intersected_gdf['PostalCode_right'].mask(
    intersected_gdf['PostalCode_right'].isna(), intersected_gdf['PostalCode_left']
)
intersected_gdf['Suffix_right'] = intersected_gdf['Suffix_right'].mask(
    intersected_gdf['Suffix_right'].isna(), intersected_gdf['Suffix_left']
)

tenant_columns = [
    'MarketableFlag', 'City_right', 'Address2', 'ASICEntityClass', 'ASICEntityStatus', 'ASICEntityType',
    'DomesticParentCountry', 'DomesticParentName', 'EmployeeIndicator', 'GlobalUltimateParentCountry',
    'GlobalUltimateParentName', 'ImmediateParentCountry', 'ImmediateParentName', 'PrimarySIC2DigitDesc',
    'PrimarySIC3DigitDesc', 'PrimarySICDesc', 'PrimarySICDivision', 'PrimarySICDivisionDesc',
    'RevenueIndicator', 'TenantName', 'StreetName_right', 'Email', 'GST_Status', 'GST_StatusFromDate',
    'ABNStatus', 'ABN', 'ACN', 'RegistrationOrIncorporationDate', 'ABN_StatusFromDate', 'SourcedDate',
    'Key_Decision_Maker_Middle_Name', 'Key_Decision_Maker_First_Name', 'Key_Decision_Maker_Last_Name',
    'LegalStatus', 'StreetType', 'Address1', 'CEOName', 'CEOTitle', 'LineOfBusiness', 'NationalID',
    'OfficePhone', 'Fax', 'StreetNoMin_right', 'StreetNoMax_right', 'StreetNo', 'PostalCode_right',
    'Suffix_right', 'SuffixAbbr', 'StateAbbr', 'State', 'WebsiteURL', 'CountryCode', 'ProviderID',
    'EmployeesAtLocation', 'StatusCode', 'IsProcessed', 'ConfirmedTenantID', 'PropertyID', 'MatchingScore',
    'CreatedDate', 'ModifiedDate', 'Tenant_Stage_Id', 'ParentCompanyID', 'BranchID', 'BatchID', 'IsDefault',
    'NAICSCode', 'NACECode', 'ModifiedBy', 'IsHidden', 'IsDeleted', 'HidedBy', 'HidedDate', 'HideReasonID',
    'HideReasonComments', 'SubHideReasonID', 'VendorID', 'DomesticParentDUNS', 'EntityAge',
    'GlobalUltimateParentDUNS', 'SubsidiaryCode', 'ImmediateParentDUNS', 'NumberofMembersinHierarchy',
    'SICCode', 'PrimarySIC2Digit', 'PrimarySIC3Digit', 'HQ_CompanyName', 'HQ_ID', 'Latitude', 'Longitude',
    'Revenue', 'ANZSICCode', 'EmployeeCount', 'Illion_version', 'geometry'
]
enriched_df = intersected_gdf[tenant_columns]
enriched_df = enriched_df.rename(columns={
    'City_right': 'City',
    'StreetName_right': 'StreetName',
    'StreetNoMin_right': 'StreetNoMin',
    'StreetNoMax_right': 'StreetNoMax',
    'PostalCode_right': 'PostalCode',
    'Suffix_right': 'Suffix'
})
enriched_df.to_csv('enriched_df.csv')
# Combine with unmatched tenants
missing_data = gdf[~gdf['VendorID'].isin(enriched_df['VendorID'])]
missing_data.to_csv('missing_data.csv')

enriched_df.shape

missing_data.shape

