import pandas as pd
from sqlalchemy import create_engine

# Database configuration
source_db_config = {
    'username': 'imperium_admin',
    'password': '',
    'state_ID': '',
    'host': 'localhost',
    'port': '3306',
    'database_name': 'Empirical_DataStage',
    'table_name': 'Tenants_Stage'
}

# Connection string
source_connection_string = f"mysql+mysqlconnector://{source_db_config['username']}:{source_db_config['password']}@{source_db_config['host']}:{source_db_config['port']}/{source_db_config['database_name']}"

# Create database engine
try:
    source_database_engine = create_engine(source_connection_string)
    print("Successfully connected to the database")
except Exception as e:
    print(f"Error connecting to database: {e}")
    exit()

# Read CSV file
csv_file_path = '/home/<USER>/Downloads/tenant_geocoded_addresses.csv'
try:
    df = pd.read_csv(csv_file_path)
    print(f"Successfully read CSV file. Shape: {df.shape}")
    print("CSV columns:", df.columns.tolist())
except Exception as e:
    print(f"Error reading CSV file: {e}")
    exit()

# Insert data into Tenants_Stage table
try:
    df = df.where(pd.notnull(df), None)
    df.to_sql(
        source_db_config['table_name'],
        con=source_database_engine,
        if_exists='append',
        index=False,
        chunksize=1000
    )
    print(f"Successfully inserted {len(df)} rows into {source_db_config['table_name']}")
except Exception as e:
    print(f"Error inserting data into table: {e}")
finally:
    source_database_engine.dispose()
    print("Database connection closed")