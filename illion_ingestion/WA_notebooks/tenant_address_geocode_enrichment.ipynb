#%%
import pandas as pd
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
import googlemaps
import numpy as np
from concurrent.futures import ThreadPoolExecutor
from functools import partial
import time

# Initialize Google Maps client
GMAPS = googlemaps.Client(key="************************")  # Replace with your Google Maps API key

# Load tenant data
df = pd.read_csv("/home/<USER>/Documents/Arealytics/notebooks/existing_illion/address_enrichment/address_enrichment.csv")
print("Tenant data columns:", df.columns.tolist())
wa_df = df
print("WA tenant data shape:", wa_df.shape)
print("WA tenant data columns:", wa_df.columns.tolist())
print("WA tenant data sample:\n", wa_df[['StreetNoMin', 'StreetName', 'City', 'PostalCode', 'State', 'Latitude', 'Longitude', 'Suffix']].head())
#%%
# Check missing values
print("Missing Latitude values:", wa_df['Latitude'].isna().sum())
print("Missing Longitude values:", wa_df['Longitude'].isna().sum())

# Address construction (for reference, not used for enrichment logic)
suffix_col = next((col for col in ['Suffix', 'StreetType', 'SuffixAbbr'] if col in wa_df.columns), None)
print(f"Using suffix column: {suffix_col}")
wa_df['address'] = wa_df.apply(
    lambda row: f"{row['StreetNoMin'] or ''} {row['StreetName'] or ''} {row[suffix_col] or ''}, {row['City'] or ''}, WA {row['PostalCode'] or ''}".strip().replace('  ', ' '),
    axis=1
)
print("Sample constructed addresses:\n", wa_df[['StreetNoMin', 'StreetName', 'City', 'PostalCode', 'Suffix', 'address']].head())

# Define columns for enrichment checks
address_columns = ['StreetNoMin', 'StreetName', 'City', 'Suffix']
lat_long_columns = ['Latitude', 'Longitude']

# Rows with complete address and lat/long (no enrichment needed)
complete_rows = wa_df[~(wa_df[address_columns + lat_long_columns].isna().any(axis=1))]
print("Rows with complete address and lat/long:", len(complete_rows))
print("Sample complete rows:\n", complete_rows[address_columns + lat_long_columns + ['address']].head())

# Rows needing enrichment: non-null lat/long but at least one null address field
Address_Enrichement_Needed = wa_df[
    (~wa_df['Latitude'].isna()) & (~wa_df['Longitude'].isna()) & 
    (wa_df[address_columns].isna().any(axis=1))
].copy()  # Use .copy() to avoid SettingWithCopyWarning
print("Rows needing enrichment shape:", Address_Enrichement_Needed.shape)
print("Sample rows needing enrichment:\n", Address_Enrichement_Needed[address_columns + lat_long_columns + ['address']].head())

#%%
# Geocode addresses from lat/long for missing address details
def get_location_details_from_lat_lng(latitude, longitude):
    if pd.isna(latitude) or pd.isna(longitude):
        return '****'  # Return placeholder for 5 empty components
    try:
        time.sleep(0.2)  # Rate limiting
        location_details = GMAPS.reverse_geocode((latitude, longitude))
        if location_details:
            data = location_details[0]["address_components"]
            res = {}
            for d in data:
                res[d["types"][0]] = d["long_name"]
            res["formatted_address"] = location_details[0]["formatted_address"]
            st_no = res.get("street_number", "")
            st_name = res.get("route", "")
            postal = res.get("postal_code", "")
            city = res.get("locality", "")
            format_addr = res.get("formatted_address", "")
            return f"{st_name}*{st_no}*{postal}*{city}*{format_addr}"
        return '****'  # Return placeholder for failed geocoding
    except Exception as e:
        print(f"Geocoding error for ({latitude}, {longitude}): {e}")
        return '****'  # Return placeholder for errors

def apply_geocode_multithreaded(row, utility_mapping):
    lat_idx = utility_mapping['Latitude']
    lon_idx = utility_mapping['Longitude']
    return get_location_details_from_lat_lng(row[lat_idx], row[lon_idx])

# Map column names to indices
utility_mapping = {
    'Latitude': Address_Enrichement_Needed.columns.get_loc('Latitude'),
    'Longitude': Address_Enrichement_Needed.columns.get_loc('Longitude')
}
#%%
# Initialize geocoded columns in Address_Enrichement_Needed
Address_Enrichement_Needed[['Geo_Street_Name', 'Geo_Street_Number', 'Geo_Postal_Code', 'Geo_City', 'Geo_Formatted_Address']] = ''

# Apply geocoding
partial_geocode = partial(apply_geocode_multithreaded, utility_mapping=utility_mapping)
with ThreadPoolExecutor(max_workers=5) as executor:
    results = list(executor.map(partial_geocode, Address_Enrichement_Needed.itertuples(index=False, name=None)))
Address_Enrichement_Needed['Geocode'] = pd.Series(results, dtype='string')  # Ensure string dtype

# Merge Geocode column back into wa_df
wa_df = wa_df.merge(
    Address_Enrichement_Needed[['Geocode']],
    left_index=True,
    right_index=True,
    how='left'
)

# Initialize geocoded columns in wa_df
wa_df[['Geo_Street_Name', 'Geo_Street_Number', 'Geo_Postal_Code', 'Geo_City', 'Geo_Formatted_Address']] = ''

# Split Geocode column in wa_df
split_df = wa_df['Geocode'].str.split('*', expand=True)
split_df = split_df.reindex(columns=range(5), fill_value='')
split_df.columns = ['Geo_Street_Name', 'Geo_Street_Number', 'Geo_Postal_Code', 'Geo_City', 'Geo_Formatted_Address']
wa_df[['Geo_Street_Name', 'Geo_Street_Number', 'Geo_Postal_Code', 'Geo_City', 'Geo_Formatted_Address']] = split_df

# Debug Geocode values
print("Sample Geocode values:\n", wa_df['Geocode'].head())
print("Successful geocodes:", (wa_df['Geocode'] != '****').sum())

# Extract street type and clean street name
wa_df['Geo_Street_Type'] = wa_df['Geo_Street_Name'].str.split(' ').str[-1]
wa_df['Geo_Street_Name'] = wa_df['Geo_Street_Name'].map(
    lambda x: '' if pd.isna(x) or x == '' else ' '.join(x.split(' ')[:-1]) or x
)

# Fill missing address details
wa_df['StreetNoMin'] = np.where(wa_df['StreetNoMin'].isna(), wa_df['Geo_Street_Number'], wa_df['StreetNoMin'])
wa_df['StreetName'] = np.where(wa_df['StreetName'].isna(), wa_df['Geo_Street_Name'], wa_df['StreetName'])
wa_df['City'] = np.where(wa_df['City'].isna(), wa_df['Geo_City'], wa_df['City'])
wa_df['PostalCode'] = np.where(wa_df['PostalCode'].isna(), wa_df['Geo_Postal_Code'], wa_df['PostalCode'])
wa_df['Suffix'] = np.where(wa_df['Suffix'].isna(), wa_df['Geo_Street_Type'], wa_df['Suffix'])

# Map street types to standardized suffixes
suffix_df = pd.read_csv('/home/<USER>/Downloads/suffix_prod.csv')
suffix_df['SuffixName'] = suffix_df['SuffixName'].str.lower()
wa_df['Geo_Street_Type'] = wa_df['Geo_Street_Type'].str.lower()
wa_df = pd.merge(
    wa_df,
    suffix_df[['Suffix', 'SuffixName']].rename(columns={'Suffix': 'Standardized_Suffix'}),
    left_on='Geo_Street_Type',
    right_on='SuffixName',
    how='left'
)

print("Records with missing Standardized_Suffix:\n", wa_df[wa_df['Standardized_Suffix'].isna()][['Geo_Street_Name', 'StreetName', 'Geo_Street_Type', 'Standardized_Suffix', 'SuffixName']].head())
geocoded_columns = ['Geocode', 'Geo_Street_Name', 'Geo_Street_Number', 'Geo_Postal_Code', 'Geo_City', 'Geo_Formatted_Address', 'Geo_Street_Type']
wa_df = wa_df.drop(columns=[col for col in geocoded_columns if col in wa_df.columns])
# Save results
wa_df.to_csv('tenant_geocoded_addresses.csv', index=False)
print("Saved geocoded tenant data to /home/<USER>/Downloads/tenant_geocoded_addresses.csv")