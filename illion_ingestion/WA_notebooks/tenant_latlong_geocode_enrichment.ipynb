#%%
import googlemaps
import pandas as pd
from tqdm import tqdm
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import logging
import json
import os
import numpy as np
#%%
# Set pandas display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
# Initialize Google Maps client
GMAPS = googlemaps.Client(key="AIzaSyBC3tdHBUhifQD6lW8WTPOG-Q1AMNdW99w")
#%%
# Create logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.handlers.clear()  # Remove default console handlers
log_file = 'tenant_geocoding_progress.log'
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)
#%%
def print_log(input: Any) -> None:
    logger.info(input)

def timestamped_string(input_string: str = "") -> str:
    return datetime.now().strftime("%H:%M:%S.%f")[:-3] + " -- " + input_string
#%%
def geocode_address(address_tuple: Tuple[str, str]) -> Dict[str, Any]:
    index, value = address_tuple
    try:
        geocoded_address = GMAPS.geocode(value)
        if geocoded_address:
            processed = process_geocoded_result(index, value, geocoded_address)
            logger.info(f"Processed record: {json.dumps(processed)}")
            return {index: processed}
        return {}
    except Exception as e:
        logger.error(f"Error geocoding address {value}: {str(e)}")
        return {}

#%%
def process_geocoded_result(id: str, address: str, results: List[Dict[str, Any]]) -> Dict[str, Any]:
    data = {
        'id': id,
        'formatted_address': results[0]['formatted_address'],
        'unit': None,
        'street_number': None,
        'street_name': None,
        'suburb': None,
        'postal_code': None,
        'latitude': results[0]['geometry']['location']['lat'],
        'longitude': results[0]['geometry']['location']['lng']
    }
    for component in results[0]['address_components']:
        if 'subpremise' in component['types']:
            data['unit'] = component['short_name']
        elif 'street_number' in component['types']:
            data['street_number'] = component['short_name']
        elif 'route' in component['types']:
            data['street_name'] = component['long_name']
        elif 'locality' in component['types']:
            data['suburb'] = component['short_name']
        elif 'postal_code' in component['types']:
            data['postal_code'] = component['short_name']
    return data
#%%
def google_geocoder(input_series: pd.Series, max_workers: int = 10) -> Dict[str, Any]:
    logger.info("Geocoding addresses")
    input_series = input_series.dropna()
    geocode_input = input_series.to_dict()
    geocoded = {}
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_address = {executor.submit(geocode_address, item): item for item in geocode_input.items()}
        for future in tqdm(as_completed(future_to_address), total=len(geocode_input)):
            result = future.result()
            geocoded.update(result)
    return geocoded
#%%
def load_progress(log_file: str) -> set[str]:
    processed_ids = set()
    if os.path.exists(log_file):
        with open(log_file, 'r') as f:
            for line in f:
                if 'Processed record' in line:
                    try:
                        record = json.loads(line.split('Processed record: ')[1])
                        processed_ids.add(record['id'])
                    except json.JSONDecodeError:
                        continue
    return processed_ids
#%%
def main():
    # Load tenant data
    wa_df = pd.read_parquet("/content/latlong_enrichment.parquet")

    print("WA tenant data shape:", wa_df.shape)
    print("WA tenant data columns:\n", wa_df.columns.tolist())
    print("WA tenant data sample:\n", wa_df[['StreetNoMin', 'StreetName', 'City', 'PostalCode', 'State']].head())

    # Check for Suffix column
    suffix_col = 'Suffix' if 'Suffix' in wa_df.columns else 'StreetType' if 'StreetType' in wa_df.columns else None
    print(f"Using suffix column: {suffix_col}")

    # Construct full address
    if suffix_col:
        wa_df['address'] = wa_df.apply(
            lambda row: f"{row['StreetNoMin'] or ''} {row['StreetName'] or ''} {row[suffix_col] or ''}, {row['City'] or ''}, WA {row['PostalCode'] or ''}".strip().replace('  ', ' '),
            axis=1
        )
    else:
        wa_df['address'] = wa_df.apply(
            lambda row: f"{row['StreetNoMin'] or ''} {row['StreetName'] or ''}, {row['City'] or ''}, WA {row['PostalCode'] or ''}".strip().replace('  ', ' '),
            axis=1
        )
    print("Sample constructed addresses:\n", wa_df[['StreetNoMin', 'StreetName', 'City', 'PostalCode', 'address']].head())

    # Set VendorID as index
    wa_df['id'] = wa_df['VendorID']
    wa_df.set_index('id', inplace=True)

    # Load progress from log file
    processed_ids = load_progress(log_file)
    logger.info(f"Loaded {len(processed_ids)} previously processed records")

    # Create existing_df from log
    existing_records = []
    if os.path.exists(log_file):
        with open(log_file, 'r') as f:
            for line in f:
                if 'Processed record' in line:
                    try:
                        record = json.loads(line.split('Processed record: ')[1])
                        existing_records.append(record)
                    except json.JSONDecodeError:
                        continue
    existing_df = pd.DataFrame(existing_records).set_index('id') if existing_records else pd.DataFrame()
    print("Existing geocoded records shape:", existing_df.shape)

    # Filter out already processed records
    df_to_process = wa_df[~wa_df.index.isin(processed_ids)]
    print("Records to process:", len(df_to_process))

    # Geocode addresses (only missing lat/long or all for verification)
    if 'Latitude' in df_to_process.columns and 'Longitude' in df_to_process.columns:
        df_to_process = df_to_process[df_to_process['Latitude'].isna() | df_to_process['Longitude'].isna()]
        print("Records with missing lat/long:", len(df_to_process))

    geocoded_addresses = google_geocoder(df_to_process['address'])
    if not geocoded_addresses:
        logger.error("No addresses were successfully geocoded. Exiting.")
        return

    # Create new geocoded DataFrame
    new_geocoded_df = pd.DataFrame.from_dict(geocoded_addresses, orient='index')
    new_geocoded_df.index.name = 'id'
    print("New geocoded records shape:", new_geocoded_df.shape)

    # Combine with existing geocoded records
    if not existing_df.empty:
        geocoded_df = pd.concat([existing_df, new_geocoded_df])
    else:
        geocoded_df = new_geocoded_df
    print("Total geocoded records shape:", geocoded_df.shape)

    # Merge with original tenant data
    merged_set = pd.merge(
        wa_df,
        geocoded_df[['street_number', 'street_name', 'unit', 'suburb', 'postal_code', 'latitude', 'longitude']],
        left_index=True,
        right_index=True,
        how='left'
    )
    print("Merged data shape:", merged_set.shape)
    print(merged_set.columns)
    # # Map street types to suffixes
    merged_set['Street_Type'] = merged_set['street_name'].str.split(' ').str[-1].str.lower()
    suffix_df = pd.read_csv('/content/suffix_prod.csv')
    print(suffix_df.columns)
    suffix_df['SuffixName'] = suffix_df['SuffixName'].str.lower()
    req_df = pd.merge(
        merged_set,
        suffix_df[['Suffix', 'SuffixName']],
        left_on='Suffix',
        right_on='SuffixName',
        how='left'
    )
    req_df = pd.merge(
        merged_set,
        suffix_df[['Suffix', 'SuffixName']].rename(columns={'Suffix': 'Standardized_Suffix'}),
        left_on='Street_Type',
        right_on='SuffixName',
        how='left'
    )
    print(req_df.columns)
    print("Records with missing Suffix:\n", req_df[req_df['Suffix'].isna()][['street_name', 'StreetName', 'Street_Type', 'Suffix', 'SuffixName']])

    # Clean Street_Name
    def street_name_map(street):
        if pd.isnull(street):
            return np.nan
        req_street = ' '.join(street.split(' ')[:-1])
        return req_street if req_street else street

    req_df['Street_Number'] = req_df['street_number']
    req_df['Street_Name'] = req_df['street_name'].apply(street_name_map)
    req_df['Unit'] = req_df['unit']

    # Select output columns
    columns = [
        'VendorID', 'StreetName', 'City', 'StreetNoMin', 'StreetNoMax', 'PostalCode', 'State',
        'Latitude', 'Longitude', 'address', 'Street_Number', 'Street_Name', 'Unit', 'suburb', 'postal_code',
        'MarketableFlag', 'Address2', 'ASICEntityClass', 'ASICEntityStatus', 'ASICEntityType',
        'DomesticParentCountry', 'DomesticParentName', 'EmployeeIndicator', 'GlobalUltimateParentCountry',
        'GlobalUltimateParentName', 'ImmediateParentCountry', 'ImmediateParentName', 'PrimarySIC2DigitDesc',
        'PrimarySIC3DigitDesc', 'PrimarySICDesc', 'PrimarySICDivision', 'PrimarySICDivisionDesc',
        'RevenueIndicator', 'TenantName', 'Email', 'GST_Status', 'GST_StatusFromDate', 'ABNStatus', 'ABN', 'ACN','latitude','longitude'
    ]
    # Add Suffix if available
    if suffix_col:
        columns.append(suffix_col)
    output_df = req_df[columns].copy()

    # Fill missing lat/long from geocoded results
    output_df['Latitude'] = output_df['Latitude'].fillna(output_df['latitude'])
    output_df['Longitude'] = output_df['Longitude'].fillna(output_df['longitude'])
    output_df.drop(columns=['latitude', 'longitude'], inplace=True, errors='ignore')

    # Generate timestamp for output file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"WA_latlong_tenant_geocoded_enriched_{timestamp}.csv"
    output_df.to_csv(output_file, index=False)
    logger.info(f"Saved {len(output_df)} records to {output_file}")

    # Print statistics
    print("Filled Latitude:", (~output_df['Latitude'].isna()).sum())
    print("Filled Longitude:", (~output_df['Longitude'].isna()).sum())
    print("Filled Street_Name:", (~output_df['Street_Name'].isna()).sum())
    print("Filled Suffix:", (~output_df[suffix_col].isna()).sum() if suffix_col else "No Suffix column")

if __name__ == "__main__":
    main()
#%%
lat_long_geocoded_df= pd.read_csv('/home/<USER>/Documents/Arealytics/notebooks/geocoding_WA/WA_latlong_tenant_geocoded_enriched_20250807_082853.csv')
#%%
print(len(lat_long_geocoded_df[lat_long_geocoded_df[['Latitude', 'Longitude']].isna().any(axis=1)]))
#%%
lat_long_geocoded_df.shape
#%%
