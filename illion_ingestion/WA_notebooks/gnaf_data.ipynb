#%%
import pandas as pd
import os

# Define file paths
input_directory = "/home/<USER>/Documents/Arealytics/notebooks/SA"
output_file = "/home/<USER>/Documents/Arealytics/notebooks/SA/gnaf_data_SA.psv"

# Load data with error handling
try:
    address_detail = pd.read_csv(os.path.join(input_directory, "SA_ADDRESS_DETAIL_psv.psv"), sep="|")
    address_site_geocode = pd.read_csv(os.path.join(input_directory, "SA_ADDRESS_SITE_GEOCODE_psv.psv"), sep="|")
    locality = pd.read_csv(os.path.join(input_directory, "SA_LOCALITY_psv.psv"), sep="|")
    street_locality = pd.read_csv(os.path.join(input_directory, "SA_STREET_LOCALITY_psv.psv"), sep="|")
except FileNotFoundError as e:
    print(f"Error: File not found - {e}")
    exit(1)
except pd.errors.ParserError as e:
    print(f"Error: Failed to parse file - {e}")
    exit(1)

# Print column names for verification
print("Columns in address_detail:", address_detail.columns.tolist())
print("Columns in address_site_geocode:", address_site_geocode.columns.tolist())
print("Columns in street_locality:", street_locality.columns.tolist())
print("Columns in locality:", locality.columns.tolist())

# Merge operations
# Step 1: Merge address_detail with address_site_geocode for LONGITUDE and LATITUDE
merged_df = address_detail.merge(
    address_site_geocode[["ADDRESS_SITE_PID", "LONGITUDE", "LATITUDE"]],
    on="ADDRESS_SITE_PID",
    how="left"
)

# Step 2: Merge with street_locality for STREET_NAME and STREET_TYPE_CODE
merged_df = merged_df.merge(
    street_locality[["STREET_LOCALITY_PID", "STREET_NAME", "STREET_TYPE_CODE"]],
    on="STREET_LOCALITY_PID",
    how="left"
)

# Step 3: Merge with locality for STATE_PID
merged_df = merged_df.merge(
    locality[["LOCALITY_PID", "STATE_PID"]],
    on="LOCALITY_PID",
    how="left"
)

# Select and rename columns for output
output_df = merged_df[[
    "ADDRESS_DETAIL_PID",
    "ADDRESS_SITE_PID",
    "STREET_LOCALITY_PID",
    "NUMBER_FIRST",
    "NUMBER_LAST",
    "STREET_NAME",
    "STREET_TYPE_CODE",
    "POSTCODE",
    "STATE_PID",
    "PRIMARY_SECONDARY",
    "LONGITUDE",
    "LATITUDE"
]].rename(columns={
    "NUMBER_FIRST": "StreetNoMin",
    "NUMBER_LAST": "StreetNoMax",
    "STREET_TYPE_CODE": "Suffix",
    "STATE_PID": "State"
})

# Handle missing StreetNoMax by filling with StreetNoMin
output_df["StreetNoMax"] = output_df["StreetNoMax"].fillna(output_df["StreetNoMin"])

# Ensure PRIMARY_SECONDARY is None for null values to match desired output
output_df["PRIMARY_SECONDARY"] = output_df["PRIMARY_SECONDARY"].where(output_df["PRIMARY_SECONDARY"].notna(), None)
print(output_df.head(5))
# Data quality checks
print("Rows with missing STREET_NAME:", output_df["STREET_NAME"].isna().sum())
print("Rows with missing Suffix:", output_df["Suffix"].isna().sum())
print("Rows with missing State:", output_df["State"].isna().sum())
print("Rows with missing LONGITUDE/LATITUDE:", output_df[["LONGITUDE", "LATITUDE"]].isna().any(axis=1).sum())

# Save output to CSV file
try:
    output_df.to_csv(output_file, index=False, encoding="utf-8")
    print(f"Created PSV file with {len(output_df)} records at {output_file}")
except Exception as e:
    print(f"Error writing to output file: {e}")
    exit(1)