CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `TSI_Tenant_Auto_Enrichment`(IN P_PrimaryConfirmedTenantID INT,IN P_ApplicationID INT,IN P_LoginEntityID INT)
BEGIN

	DECLARE PrimaryCompanyID INT default 0;
    DECLARE P_PropertyID INT default 0;
    DECLAR<PERSON> JSONTEXT text;
    SET SESSION group_concat_max_len = 1000000;
    -- select P_PrimaryConfirmedTenantID;
    set @MV_ConfirmedTenantID=null,@MV_TenantName=null,@MV_CompanyName=null,@MV_AlternateCompanyName=null,
		@MV_Address1=null,@MV_Address2=null,@MV_AddressStreetNumber=null,@MV_AddressStreetName=null,@MV_CityID=null,
        @MV_StateID=null,@MV_ZipCode=null,@MV_CountyID=null,@MV_CountryID=null,@MV_AddressText=null,@MV_ExtVendorID=null,
        @MV_ProviderID=null,@MV_ModifiedBy=null,@MV_ModifiedDate=null,@MV_IsActive=null,@MV_MetroID=null,@MV_OfficePhone=null,
        @MV_CompanyID=null,@MV_SaleComp_Stage_ID=null,@MV_Fax=null,@MV_CEOName=null,@MV_CEOTitle=null,@MV_LineOfBusiness=null,
        @MV_SICCode=null,@MV_Revenue=null,@MV_EmployeesAtLocation=null,@MV_EmployeeCount=null,@MV_LegalStatus=null,@MV_StatusCode=null,
        @MV_SubsidiaryCode=null,@MV_NAICSCode=null,@MV_NACECode=null,@MV_NationalID=null,@MV_Email=null,@MV_WebsiteUrl=null,
        @MV_FloorNumber=null,@MV_PrimarySICDivisionDesc=null,@MV_PrimarySIC2DigitDesc=null,@MV_PrimarySIC3DigitDesc=null,
        @MV_RegistrationOrIncorporationDate=null,@MV_ANZSICCode=null,@MV_RevenueIndicator=null;
	SET @ref_TenantName=null,@ref_Address1=null,@ref_Address2=null,@ref_CityID=null,@ref_StateID=null,@ref_CountryID=null,@ref_OfficePhone=null,@ref_Fax=null,@ref_Email=null,@ref_WebsiteURL=null,
        @ref_EmployeeCount=null,@ref_Revenue=null,@ref_CEOName=null,@ref_CEOTitle=null,@ref_LineOfBusiness=null,@ref_SICCode=null,@ref_EmployeesAtLocation=null,@ref_LegalStatus=null,
        @ref_SubsidiaryCode=null,@ref_ABN=null,@ref_ACN =null,@ref_PrimarySICDivisionDesc=null,@ref_PrimarySIC2DigitDesc=null,@ref_PrimarySIC3DigitDesc=null,
        @ref_RegistrationOrIncorporationDate=null,@ref_RevenueIndicator=null,@ref_ANZSICCode=null,@ref_PostalCode=null,@ref_StateAbbr=null;
        
	-- Get Company Id based on the ConfirmedTenantID
	SELECT CompanyID into PrimaryCompanyID FROM Empirical_Tenants.ConfirmedTenants where ConfirmedTenantID=P_PrimaryConfirmedTenantID;
    -- select PrimaryCompanyID;
    -- Get merged view confirmed tenant details
    SELECT ConfirmedTenantID,TenantName,CompanyName,AlternateCompanyName,Address1,Address2,AddressStreetNumber,AddressStreetName,
			CityID,StateID,ZipCode,CountyID,CountryID,AddressText,ExtVendorID,ProviderID,ModifiedBy,ModifiedDate,IsActive,MetroID,
            OfficePhone,CompanyID,SaleComp_Stage_ID,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
            LegalStatus,StatusCode,SubsidiaryCode,NAICSCode,NACECode,NationalID,Email,WebsiteUrl,FloorNumber,PrimarySICDivisionDesc,
            PrimarySIC2DigitDesc,PrimarySIC3DigitDesc,RegistrationOrIncorporationDate,ANZSICCode,RevenueIndicator 
	into @MV_ConfirmedTenantID,@MV_TenantName,@MV_CompanyName,@MV_AlternateCompanyName,
			@MV_Address1,@MV_Address2,@MV_AddressStreetNumber,@MV_AddressStreetName,@MV_CityID,@MV_StateID,@MV_ZipCode,@MV_CountyID,
            @MV_CountryID,@MV_AddressText,@MV_ExtVendorID,@MV_ProviderID,@MV_ModifiedBy,@MV_ModifiedDate,@MV_IsActive,@MV_MetroID,
            @MV_OfficePhone,@MV_CompanyID,@MV_SaleComp_Stage_ID,@MV_Fax,@MV_CEOName,@MV_CEOTitle,@MV_LineOfBusiness,@MV_SICCode,
            @MV_Revenue,@MV_EmployeesAtLocation,@MV_EmployeeCount,@MV_LegalStatus,@MV_StatusCode,@MV_SubsidiaryCode,@MV_NAICSCode,
            @MV_NACECode,@MV_NationalID,@MV_Email,@MV_WebsiteUrl,@MV_FloorNumber,@MV_PrimarySICDivisionDesc,@MV_PrimarySIC2DigitDesc,
            @MV_PrimarySIC3DigitDesc,@MV_RegistrationOrIncorporationDate,@MV_ANZSICCode,@MV_RevenueIndicator 
	FROM Empirical_Tenants.ConfirmedTenants where CompanyID=PrimaryCompanyID and ProviderID=5;
    
    -- Get property id from suite tenant having moved in Tenant Status
  --  SELECT distinct PropertyID into P_PropertyID from Empirical_Prod.SuiteTenant where ConfirmedTenantID=P_PrimaryConfirmedTenantID and TenantStatusID=1 and IsActive=1;
	
    -- fetch all tenant stage records order by TenantAutoEnrichmentPriority in provider
    drop temporary table if exists tempAccPriority;
    create temporary table tempAccPriority(
		select ts.*,P.TenantAutoEnrichmentPriority,P.TenantVendorIDAutoEnrichmentPriority from Empirical_DataStage.Tenants_Stage as ts
        inner join Empirical_Prod.Providers as P on P.ProviderID=ts.ProviderID
        where BranchID=PrimaryCompanyID and P.TenantAutoEnrichmentPriority is not null 
        order by P.TenantAutoEnrichmentPriority
	);
 --   select * from tempAccPriority;
    SET @jsonArrayTCTS =  JSON_ARRAY();
	set @Address1=null,@Address2=null,@PostalCode=null,@CountryCode=null,@OfficePhone=null,@Email=null,@WebsiteURL=null,@Fax=null,
		@NationalID=null,@EmployeeCount=null,@Revenue=null,@ANZSICCode=null,@PropertyID=null,@ProviderID=null,@CEOName=null,@CEOTitle=null,
        @LineOfBusiness=null,@SICCode=null,@EmployeesAtLocation=null,@LegalStatus=null,@StatusCode=null,@SubsidiaryCode=null,@NAICSCode=null,
        @NACECode=null,@PrimarySICDivisionDesc=null,@PrimarySIC2DigitDesc=null,@PrimarySIC3DigitDesc=null,@RegistrationOrIncorporationDate=null,
        @RevenueIndicator=null;
	
    set @ref_Address1=null,@ref_Address2=null,@ref_PostalCode=null,@ref_CountryCode=null,@ref_OfficePhone=null,@ref_Email=null,@ref_WebsiteURL=null,
		@ref_Fax=null,@ref_NationalID=null,@ref_EmployeeCount=null,@ref_Revenue=null,@ref_ANZSICCode=null,@ref_PropertyID=null,@ref_ProviderID=null,
        @ref_CEOName=null,@ref_CEOTitle=null,@ref_LineOfBusiness=null,@ref_SICCode=null,@ref_EmployeesAtLocation=null,@ref_LegalStatus=null,
        @ref_StatusCode=null,@ref_SubsidiaryCode=null,@ref_NAICSCode=null,@ref_NACECode=null,@ref_PrimarySICDivisionDesc=null,
        @ref_PrimarySIC2DigitDesc=null,@ref_PrimarySIC3DigitDesc=null,@ref_RegistrationOrIncorporationDate=null,@ref_RevenueIndicator=null;
        
	set @ref_Address1_TS_ID=null, @ref_Address2_TS_ID=null, @ref_PostalCode_TS_ID=null, @ref_CountryCode_TS_ID=null, @ref_OfficePhone_TS_ID=null,
		@ref_Email_TS_ID=null, @ref_WebsiteURL_TS_ID=null, @ref_Fax_TS_ID=null, @ref_NationalID_TS_ID=null, @ref_EmployeeCount_TS_ID=null, 
        @ref_Revenue_TS_ID=null, @ref_ANZSICCode_TS_ID=null, @ref_PropertyID_TS_ID=null, @ref_ProviderID_TS_ID=null, @ref_CEOName_TS_ID=null, 
        @ref_CEOTitle_TS_ID=null, @ref_LineOfBusiness_TS_ID=null, @ref_SICCode_TS_ID=null, @ref_EmployeesAtLocation_TS_ID=null,
        @ref_LegalStatus_TS_ID=null, @ref_StatusCode_TS_ID=null, @ref_SubsidiaryCode_TS_ID=null, @ref_NAICSCode_TS_ID=null, @ref_NACECode_TS_ID=null,
        @ref_PrimarySICDivisionDesc_TS_ID=null, @ref_PrimarySIC2DigitDesc_TS_ID=null, @ref_PrimarySIC3DigitDesc_TS_ID=null,
        @ref_RegistrationOrIncorporationDate_TS_ID=null, @ref_RevenueIndicator_TS_ID=null;
      select * from tempAccPriority;  
    -- fetch individual fields of the tenant stage based in the highest auto enrichment priority field values
    select Tenant_Stage_Id,ProviderID into @Tenant_Stage_Id,@ref_TS_Id	from tempAccPriority where 	TenantName	 is not null order by TenantAutoEnrichmentPriority limit 1;
	select Address1,ProviderID,Tenant_Stage_Id into @Address1,@ref_Address1,@ref_Address1_TS_ID from tempAccPriority where Address1 is not null order by TenantAutoEnrichmentPriority limit 1;
	select Address2,ProviderID,Tenant_Stage_Id into @Address2,@ref_Address2,@ref_Address2_TS_ID from tempAccPriority where Address2 is not null order by TenantAutoEnrichmentPriority limit 1;
	select PostalCode,ProviderID,Tenant_Stage_Id into @PostalCode,@ref_PostalCode,@ref_PostalCode_TS_ID from tempAccPriority where PostalCode is not null order by TenantAutoEnrichmentPriority limit 1;
	select CountryCode,ProviderID,Tenant_Stage_Id into @CountryCode,@ref_CountryCode,@ref_CountryCode_TS_ID from tempAccPriority where CountryCode is not null order by TenantAutoEnrichmentPriority limit 1;
	select OfficePhone,ProviderID,Tenant_Stage_Id into @OfficePhone,@ref_OfficePhone,@ref_OfficePhone_TS_ID from tempAccPriority where OfficePhone is not null order by TenantAutoEnrichmentPriority limit 1;
	select Email,ProviderID,Tenant_Stage_Id into @Email,@ref_Email,@ref_Email_TS_ID from tempAccPriority where Email is not null order by TenantAutoEnrichmentPriority limit 1;
	select WebsiteURL,ProviderID,Tenant_Stage_Id into @WebsiteURL,@ref_WebsiteURL,@ref_WebsiteURL_TS_ID from tempAccPriority where WebsiteURL is not null order by TenantAutoEnrichmentPriority limit 1;
	select Fax,ProviderID,Tenant_Stage_Id into @Fax,@ref_Fax,@ref_Fax_TS_ID from tempAccPriority where Fax is not null order by TenantAutoEnrichmentPriority limit 1;
	select NationalID,ProviderID,Tenant_Stage_Id into @NationalID,@ref_NationalID,@ref_NationalID_TS_ID from tempAccPriority where NationalID is not null order by TenantAutoEnrichmentPriority limit 1;
	select EmployeeCount,ProviderID,Tenant_Stage_Id into @EmployeeCount,@ref_EmployeeCount,@ref_EmployeeCount_TS_ID from tempAccPriority where EmployeeCount is not null order by TenantAutoEnrichmentPriority limit 1;
	select Revenue,ProviderID,Tenant_Stage_Id into @Revenue,@ref_Revenue,@ref_Revenue_TS_ID from tempAccPriority where Revenue is not null order by TenantAutoEnrichmentPriority limit 1;
	select ANZSICCode,ProviderID,Tenant_Stage_Id into @ANZSICCode,@ref_ANZSICCode,@ref_ANZSICCode_TS_ID from tempAccPriority where ANZSICCode is not null order by TenantAutoEnrichmentPriority limit 1;
	select PropertyID,ProviderID,Tenant_Stage_Id into @PropertyID,@ref_PropertyID,@ref_PropertyID_TS_ID from tempAccPriority where PropertyID is not null order by TenantAutoEnrichmentPriority limit 1;
	select ProviderID,ProviderID,Tenant_Stage_Id into @ProviderID,@ref_ProviderID,@ref_ProviderID_TS_ID from tempAccPriority where ProviderID is not null order by TenantAutoEnrichmentPriority limit 1;
	select CEOName,ProviderID,Tenant_Stage_Id into @CEOName,@ref_CEOName,@ref_CEOName_TS_ID from tempAccPriority where CEOName is not null order by TenantAutoEnrichmentPriority limit 1;
	select CEOTitle,ProviderID,Tenant_Stage_Id into @CEOTitle,@ref_CEOTitle,@ref_CEOTitle_TS_ID from tempAccPriority where CEOTitle is not null order by TenantAutoEnrichmentPriority limit 1;
	select LineOfBusiness,ProviderID,Tenant_Stage_Id into @LineOfBusiness,@ref_LineOfBusiness,@ref_LineOfBusiness_TS_ID from tempAccPriority where LineOfBusiness is not null order by TenantAutoEnrichmentPriority limit 1;
	select SICCode,ProviderID,Tenant_Stage_Id into @SICCode,@ref_SICCode,@ref_SICCode_TS_ID from tempAccPriority where SICCode is not null order by TenantAutoEnrichmentPriority limit 1;
	select EmployeesAtLocation,ProviderID,Tenant_Stage_Id into @EmployeesAtLocation,@ref_EmployeesAtLocation,@ref_EmployeesAtLocation_TS_ID from tempAccPriority where EmployeesAtLocation is not null order by TenantAutoEnrichmentPriority limit 1;
	select LegalStatus,ProviderID,Tenant_Stage_Id into @LegalStatus,@ref_LegalStatus,@ref_LegalStatus_TS_ID from tempAccPriority where LegalStatus is not null order by TenantAutoEnrichmentPriority limit 1;
	select StatusCode,ProviderID,Tenant_Stage_Id into @StatusCode,@ref_StatusCode,@ref_StatusCode_TS_ID from tempAccPriority where StatusCode is not null order by TenantAutoEnrichmentPriority limit 1;
	select SubsidiaryCode,ProviderID,Tenant_Stage_Id into @SubsidiaryCode,@ref_SubsidiaryCode,@ref_SubsidiaryCode_TS_ID from tempAccPriority where SubsidiaryCode is not null order by TenantAutoEnrichmentPriority limit 1;
	select NAICSCode,ProviderID,Tenant_Stage_Id into @NAICSCode,@ref_NAICSCode,@ref_NAICSCode_TS_ID from tempAccPriority where NAICSCode is not null order by TenantAutoEnrichmentPriority limit 1;
	select NACECode,ProviderID,Tenant_Stage_Id into @NACECode,@ref_NACECode,@ref_NACECode_TS_ID from tempAccPriority where NACECode is not null order by TenantAutoEnrichmentPriority limit 1;
	select PrimarySICDivisionDesc,ProviderID,Tenant_Stage_Id into @PrimarySICDivisionDesc,@ref_PrimarySICDivisionDesc,@ref_PrimarySICDivisionDesc_TS_ID from tempAccPriority where PrimarySICDivisionDesc is not null order by TenantAutoEnrichmentPriority limit 1;
	select PrimarySIC2DigitDesc,ProviderID,Tenant_Stage_Id into @PrimarySIC2DigitDesc,@ref_PrimarySIC2DigitDesc,@ref_PrimarySIC2DigitDesc_TS_ID from tempAccPriority where PrimarySIC2DigitDesc is not null order by TenantAutoEnrichmentPriority limit 1;
	select PrimarySIC3DigitDesc,ProviderID,Tenant_Stage_Id into @PrimarySIC3DigitDesc,@ref_PrimarySIC3DigitDesc,@ref_PrimarySIC3DigitDesc_TS_ID from tempAccPriority where PrimarySIC3DigitDesc is not null order by TenantAutoEnrichmentPriority limit 1;
	select RegistrationOrIncorporationDate,ProviderID,Tenant_Stage_Id into @RegistrationOrIncorporationDate,@ref_RegistrationOrIncorporationDate,@ref_RegistrationOrIncorporationDate_TS_ID from tempAccPriority where RegistrationOrIncorporationDate is not null order by TenantAutoEnrichmentPriority limit 1;
	select RevenueIndicator,ProviderID,Tenant_Stage_Id into @RevenueIndicator,@ref_RevenueIndicator,@ref_RevenueIndicator_TS_ID from tempAccPriority where RevenueIndicator is not null order by TenantAutoEnrichmentPriority limit 1;
    select 	VendorID,ProviderID,Tenant_Stage_Id  into @VendorID,@ref_VendorID,@ref_VendorID_TS_ID	from tempAccPriority where 	VendorID	 is not null order by TenantVendorIDAutoEnrichmentPriority limit 1;
	select ProviderID,Tenant_Stage_Id into @ref_Default,@ref_Default_TS_ID from tempAccPriority order by TenantAutoEnrichmentPriority limit 1;

    -- temporary list to hold changed fiels for auditing
     DROP TEMPORARY TABLE IF EXISTS tempChangeLog;
	CREATE TEMPORARY TABLE tempChangeLog
	(
		ConfirmedTenantID INT,
		ProviderID INT,
		Source_Tenant_Record_ID INT,
		FieldID INT,
        CTValue varchar(40)
	);
    
        -- fetching address 1 and address 2 details from the Property
	/*	 IF @MV_ConfirmedTenantID IS NOT NULL AND trim(IFNULL(@Address1,'')) <> '' THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_Address1,@ref_Address1_TS_ID,5; -- Provider 1 , Getting from Property
            Update Empirical_Tenants.ConfirmedTenants Set Address1=@Address1,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        END IF;
		 IF @MV_ConfirmedTenantID IS NOT NULL AND trim(IFNULL(@Address2,'')) <> '' THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_Address2,@ref_Address2_TS_ID,6; -- Provider 1 , Getting from Property
            Update Empirical_Tenants.ConfirmedTenants Set Address1=@Address1,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        END IF;*/
        IF @MV_ConfirmedTenantID IS NOT NULL  and @ref_OfficePhone is not null THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_OfficePhone,@ref_OfficePhone_TS_ID,14,@OfficePhone;
            Update Empirical_Tenants.ConfirmedTenants Set OfficePhone=@OfficePhone,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_OfficePhone is null or @ref_OfficePhone_TS_ID is null THEN
			 INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_OfficePhone,@ref_OfficePhone_TS_ID,14,@OfficePhone;
            END IF;
        END IF;
         IF @MV_ConfirmedTenantID IS NOT NULL and @ref_Fax is not null THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_Fax,@ref_Fax_TS_ID,15,@Fax;
            Update Empirical_Tenants.ConfirmedTenants Set Fax=@Fax,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_Fax is null or @ref_Fax_TS_ID is null THEN
			 INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_Fax,@ref_Fax_TS_ID,15,@Fax;
            END IF;
        END IF;
        IF @MV_ConfirmedTenantID IS NOT NULL  and @ref_Email is not null THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_Email,@ref_Email_TS_ID,28,@Email;
            Update Empirical_Tenants.ConfirmedTenants Set Email=@Email,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_Email is null or @ref_Email_TS_ID is null THEN
			 INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_Email,@ref_Email_TS_ID,28,@Email;
            END IF;
        END IF;
        IF @MV_ConfirmedTenantID IS NOT NULL  and  @ref_WebsiteURL is not null  THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_WebsiteURL,@ref_WebsiteURL_TS_ID,29,@WebsiteURL;
            Update Empirical_Tenants.ConfirmedTenants Set WebsiteURL=@WebsiteURL,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_WebsiteURL is null or @ref_WebsiteURL_TS_ID is null THEN
			 INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_WebsiteURL,@ref_WebsiteURL_TS_ID,29,@WebsiteURL;
            END IF;
        END IF;
 		IF @MV_ConfirmedTenantID IS NOT NULL AND @ref_FloorNumber is not null THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_FloorNumber,@ref_FloorNumber_TS_ID,30,@FloorNumber;
            Update Empirical_Tenants.ConfirmedTenants Set FloorNumber=@FloorNumber,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_FloorNumber is null or @ref_FloorNumber_TS_ID is null THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_FloorNumber,@ref_FloorNumber_TS_ID,30,@FloorNumber;
             END IF;
        END IF;
		IF @MV_ConfirmedTenantID IS NOT NULL  and (@ref_NationalID is not null )THEN
			-- extract ABN and ACN from National ID
			 SET @ABN = NULL;
			 SET @ACN = NULL;
			-- fetching ABN and ACN from National ID independent of its orders
			 SELECT IF(LOCATE('ACN:', @NationalID) > 0,TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(REPLACE(@NationalID,": ",":"), 'ACN:', -1), ' ', 1)), NULL) INTO @ACN;
			 select  IF(LOCATE('ABN:', @NationalID) > 0,TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(REPLACE(@NationalID,": ",":"), 'ABN:', -1), ' ', 1)),NULL) INTO @ABN;
             INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_NationalID,@ref_NationalID_TS_ID,32,@NationalID;
             if @ABN is not null then
             			INSERT INTO tempChangeLog
				SELECT @MV_ConfirmedTenantID,@ref_NationalID,@ref_NationalID_TS_ID,44,@NationalID;
             end if;
             if @ACN is not null then
             			 INSERT INTO tempChangeLog
				SELECT @MV_ConfirmedTenantID,@ref_NationalID,@ref_NationalID_TS_ID,43,@NationalID;
             end if;
			 
             -- create National Identifiers for the ABN and ACN
			Call Empirical_Prod.CRE_NationalIdentifiers_Save(NULL,NULL,@MV_ConfirmedTenantID,@ACN,@ABN);
            Update Empirical_Tenants.ConfirmedTenants Set NationalID=@NationalID,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
            
        END IF;
        IF @MV_ConfirmedTenantID IS NOT NULL and @ref_EmployeeCount is not null  THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_EmployeeCount,@ref_EmployeeCount_TS_ID,24,@EmployeeCount;
            Update Empirical_Tenants.ConfirmedTenants Set EmployeeCount =@EmployeeCount,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_EmployeeCount is null or @ref_EmployeeCount_TS_ID THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_EmployeeCount,@ref_EmployeeCount_TS_ID,24,@EmployeeCount;
             END IF;
        END IF; 
        IF @MV_ConfirmedTenantID IS NOT NULL and @ref_VendorID is not null THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_VendorID,@ref_VendorID_TS_ID,38,@VendorID;
            Update Empirical_Tenants.ConfirmedTenants Set ExtVendorID =@VendorID,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_VendorID is null or @ref_VendorID_TS_ID THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_VendorID,@ref_VendorID_TS_ID,38,@VendorID;
             END IF;
        END IF; 
		IF @MV_ConfirmedTenantID IS NOT NULL and @ref_Revenue is not null THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_Revenue,@ref_Revenue_TS_ID,22,@Revenue;
            Update Empirical_Tenants.ConfirmedTenants Set Revenue =@Revenue,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_Revenue is null or @ref_Revenue_TS_ID THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_Revenue,@ref_Revenue_TS_ID,22,@Revenue;
             END IF;
        END IF;  
		IF @MV_ConfirmedTenantID IS NOT NULL and @ref_CEOName is not null  THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_CEOName,@ref_CEOName_TS_ID,16,@CEOName;
            Update Empirical_Tenants.ConfirmedTenants Set CEOName =@CEOName,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_CEOName is null or @ref_CEOName_TS_ID THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_CEOName,@ref_CEOName_TS_ID,16,@CEOName;
             END IF;
        END IF;  
		IF @MV_ConfirmedTenantID IS NOT NULL and @ref_CEOTitle is not null THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_CEOTitle,@ref_CEOTitle_TS_ID,17,@CEOTitle;
            Update Empirical_Tenants.ConfirmedTenants Set CEOTitle =@CEOTitle,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_CEOTitle is null or @ref_CEOTitle_TS_ID THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_CEOTitle,@ref_CEOTitle_TS_ID,17,@CEOTitle;
             END IF;
        END IF;  	
        IF @MV_ConfirmedTenantID IS NOT NULL  and @ref_LineOfBusiness is not null THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_LineOfBusiness,@ref_LineOfBusiness_TS_ID,18,@LineOfBusiness;
            Update Empirical_Tenants.ConfirmedTenants Set LineOfBusiness=@LineOfBusiness,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_LineOfBusiness is null or @ref_LineOfBusiness_TS_ID THEN
			 INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_LineOfBusiness,@ref_LineOfBusiness_TS_ID,18,@LineOfBusiness;
            END IF;
        END IF;  
         IF @MV_ConfirmedTenantID IS NOT NULL  and @ref_SICCode is not null THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_SICCode,@ref_SICCode_TS_ID,19,@SICCode;
            Update Empirical_Tenants.ConfirmedTenants Set SICCode=@SICCode,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_SICCode is null or @ref_SICCode_TS_ID THEN
			 INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_SICCode,@ref_SICCode_TS_ID,19,@SICCode;
            END IF;
        END IF;  
        IF @MV_ConfirmedTenantID IS NOT NULL  and @ref_EmployeesAtLocation is not null  THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_EmployeesAtLocation,@ref_EmployeesAtLocation_TS_ID,23,@EmployeesAtLocation;
            Update Empirical_Tenants.ConfirmedTenants Set EmployeesAtLocation=@EmployeesAtLocation,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_EmployeesAtLocation is null or @ref_EmployeesAtLocation_TS_ID THEN
			 INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_EmployeesAtLocation,@ref_EmployeesAtLocation_TS_ID,23,@EmployeesAtLocation;
            END IF;
        END IF;  
        IF @MV_ConfirmedTenantID IS NOT NULL  and @ref_LegalStatus is not null   THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_LegalStatus,@ref_LegalStatus_TS_ID,25,@LegalStatus;
            Update Empirical_Tenants.ConfirmedTenants Set LegalStatus=@LegalStatus,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_LegalStatus is null or @ref_LegalStatus_TS_ID THEN
			 INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_LegalStatus,@ref_LegalStatus_TS_ID,25,@LegalStatus;
            END IF;
        END IF;  
        IF @MV_ConfirmedTenantID IS NOT NULL AND @ref_StatusCode is not null    THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_StatusCode,@ref_StatusCode_TS_ID,26,@StatusCode;
            Update Empirical_Tenants.ConfirmedTenants Set StatusCode=@StatusCode,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_StatusCode is null or @ref_StatusCode_TS_ID THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_StatusCode,@ref_StatusCode_TS_ID,26,@StatusCode;
             END IF;
        END IF;  
		IF @MV_ConfirmedTenantID IS NOT NULL  AND @ref_SubsidiaryCode_TS_ID is not null     THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_SubsidiaryCode,@ref_SubsidiaryCode_TS_ID,27,@SubsidiaryCode;
            Update Empirical_Tenants.ConfirmedTenants Set SubsidiaryCode=@SubsidiaryCode,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_SubsidiaryCode is null or @ref_SubsidiaryCode_TS_ID THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_SubsidiaryCode,@ref_SubsidiaryCode_TS_ID,27,@SubsidiaryCode;
             END IF;
        END IF;  
        IF @MV_ConfirmedTenantID IS NOT NULL and @ref_NAICSCode is not null  THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_NAICSCode,@ref_NAICSCode_TS_ID,20,@NAICSCode;
            Update Empirical_Tenants.ConfirmedTenants Set NAICSCode =@NAICSCode,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_NAICSCode is null or @ref_NAICSCode_TS_ID THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_NAICSCode,@ref_NAICSCode_TS_ID,20,@NAICSCode;
             END IF;
        END IF;  
		IF @MV_ConfirmedTenantID IS NOT NULL and @ref_NACECode is not null   THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_NACECode,@ref_NACECode_TS_ID,21,@NACECode;
            Update Empirical_Tenants.ConfirmedTenants Set NACECode =@NACECode,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_NACECode is null or @ref_NACECode_TS_ID THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_NACECode,@ref_NACECode_TS_ID,21,@NACECode;
             END IF;
        END IF;  
        IF @MV_ConfirmedTenantID IS NOT NULL  and @ref_PrimarySICDivisionDesc is not null   THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_PrimarySICDivisionDesc,@ref_PrimarySICDivisionDesc_TS_ID,39,@PrimarySICDivisionDesc;
            Update Empirical_Tenants.ConfirmedTenants Set PrimarySICDivisionDesc=@PrimarySICDivisionDesc,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_PrimarySICDivisionDesc is null or @ref_PrimarySICDivisionDesc_TS_ID THEN
			 INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_PrimarySICDivisionDesc,@ref_PrimarySICDivisionDesc_TS_ID,39,@PrimarySICDivisionDesc;
            END IF;
        END IF;  
         IF @MV_ConfirmedTenantID IS NOT NULL  and @ref_PrimarySIC2DigitDesc is not null  THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_PrimarySIC2DigitDesc,@ref_PrimarySIC2DigitDesc_TS_ID,40,@PrimarySIC2DigitDesc;
            Update Empirical_Tenants.ConfirmedTenants Set PrimarySIC2DigitDesc=@PrimarySIC2DigitDesc,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_PrimarySIC2DigitDesc is null or @ref_PrimarySIC2DigitDesc_TS_ID THEN
			 INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_PrimarySIC2DigitDesc,@ref_PrimarySIC2DigitDesc_TS_ID,40,@PrimarySIC2DigitDesc;
            END IF;
        END IF;  
        IF @MV_ConfirmedTenantID IS NOT NULL  and @ref_PrimarySIC3DigitDesc is not null  THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_PrimarySIC3DigitDesc,@ref_PrimarySIC3DigitDesc_TS_ID,41,@PrimarySIC3DigitDesc;
            Update Empirical_Tenants.ConfirmedTenants Set PrimarySIC3DigitDesc=@PrimarySIC3DigitDesc,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_PrimarySIC3DigitDesc is null or @ref_PrimarySIC3DigitDesc_TS_ID THEN
			 INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_PrimarySIC3DigitDesc,@ref_PrimarySIC3DigitDesc_TS_ID,41,@PrimarySIC3DigitDesc;
            END IF;
        END IF; 
        IF @MV_ConfirmedTenantID IS NOT NULL  and @ref_RegistrationOrIncorporationDate is not null THEN
			INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_RegistrationOrIncorporationDate,@ref_RegistrationOrIncorporationDate_TS_ID,42,@RegistrationOrIncorporationDate;
            Update Empirical_Tenants.ConfirmedTenants Set RegistrationOrIncorporationDate=@RegistrationOrIncorporationDate,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_RegistrationOrIncorporationDate is null or @ref_RegistrationOrIncorporationDate_TS_ID THEN
			 INSERT INTO tempChangeLog
			SELECT @MV_ConfirmedTenantID,@ref_RegistrationOrIncorporationDate,@ref_RegistrationOrIncorporationDate_TS_ID,42,@RegistrationOrIncorporationDate;
			END IF;
        END IF; 
        
		IF @MV_ConfirmedTenantID IS NOT NULL AND @ref_RevenueIndicator is not null THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_RevenueIndicator,@ref_RevenueIndicator_TS_ID ,49,@RevenueIndicator;
            Update Empirical_Tenants.ConfirmedTenants Set RevenueIndicator=@RevenueIndicator,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_RevenueIndicator is null or @ref_RevenueIndicator_TS_ID THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_RevenueIndicator,@ref_RevenueIndicator_TS_ID ,49,@RevenueIndicator;
             END IF;
        END IF; 
		IF @MV_ConfirmedTenantID IS NOT NULL AND @ref_ANZSICCode is not null THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_ANZSICCode,@ref_ANZSICCode_TS_ID,45,@ANZSICCode;
              INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_ANZSICCode,@ref_ANZSICCode_TS_ID,46,@ANZSICCode;
             INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_ANZSICCode,@ref_ANZSICCode_TS_ID,47,@ANZSICCode;
              INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_ANZSICCode,@ref_ANZSICCode_TS_ID,48,@ANZSICCode;
            Update Empirical_Tenants.ConfirmedTenants Set ANZSICCode=@ANZSICCode,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@MV_ConfirmedTenantID;
        ELSE IF @ref_ANZSICCode is null or @ref_ANZSICCode_TS_ID THEN
			 INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_ANZSICCode,@ref_ANZSICCode_TS_ID,45,@ANZSICCode;
              INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_ANZSICCode,@ref_ANZSICCode_TS_ID,46,@ANZSICCode;
             INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_ANZSICCode,@ref_ANZSICCode_TS_ID,47,@ANZSICCode;
              INSERT INTO tempChangeLog
			 SELECT @MV_ConfirmedTenantID,@ref_ANZSICCode,@ref_ANZSICCode_TS_ID,48,@ANZSICCode;
             END IF;
        END IF; 
		-- Prepare JSON object from change log to pass it to the change log save SP        
			select * from tempChangeLog;
			SELECT 
				CONCAT('[', FirstPass, ']') INTO JSONTEXT
			FROM
				(SELECT 
					GROUP_CONCAT('{', Jason1, '}'
							SEPARATOR ',') AS FirstPass
				FROM
					(
						SELECT 
						CONCAT('"ConfirmedTenantID":', '"', CL.ConfirmedTenantID, '"', ',',
								'"ProviderID":', '"', ifnull(CL.ProviderID,@ref_Default), '"', ',',
                                '"Source_Tenant_Record_ID":', '"', ifnull(CL.Source_Tenant_Record_ID,@ref_Default_TS_ID), '"', ',',
                                '"FieldID":', '"', CL.FieldID, '"', ',',
                                '"CTValue":', '"', ifnull(CL.CTValue,"NAN"), '"'
							   ) AS Jason1
				FROM
					tempChangeLog CL) AS Jason2) AS Jason3;
               select JSONTEXT;
		if JSONTEXT IS NOT NULL THEN
		 --  save change log
		  CALL Empirical_DataIngestion.TSI_Property_Tenant_Matrix_Save_MergedView_Changelog(JSONTEXT,P_LoginEntityID);
		END IF;
        
END