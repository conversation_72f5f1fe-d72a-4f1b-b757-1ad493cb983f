const sql = require('mysql');

// Database configuration
const config = {
  user: 'imperium_admin',
  password: '*****',
  server: 'localhost',
  database: 'Empirical_DataIngestion',
  port: 3343,
  options: {
    encrypt: true, // For SQL Server
  },
};

const args = process.argv.slice(1);

// Stored procedure name
const tenantStageUpdateStoredProcedureName = 'TSI_Update_Existing_Tenants_Under_Same_PropertyID';
const autoEnrichmentStoredProcudureName = 'TSI_Tenant_Auto_Enrichment_For_Already_Grouped_Tenants';

const IllionUpdation = async (BatchID,AutoEnrichmentRequired) => {

  return new Promise(function (resolve, reject) {
    try {
      //connect to the database
      const pool = sql.createPool(config);
      console.log(BatchID,'batch started',new Date());
      let spcall1 = `CALL Empirical_DataIngestion.${tenantStageUpdateStoredProcedureName}(${BatchID});`;
      pool.getConnection(async function (error, connection) {
        if (connection) {
          connection.query(spcall1, async (error, results) => {
            if (error) {
              // console.log(spcall1, 'Error', error);
              resolve("reject");
            }
            if (results && AutoEnrichmentRequired==1) {
                console.log("auto enrichment Started", new Date());
                let spcall = `CALL Empirical_DataIngestion.${autoEnrichmentStoredProcudureName}(${BatchID});`;
                connection.query(spcall, (error, results) => {
                  if (error) {
                    //   console.log(spcall, 'Error', error);
                    resolve(error);
                  }
                  if (results) {
                    console.log(spcall, 'Success', new Date());
                    resolve(results);
                  }
                });
            } else if (results){
                console.log(spcall, 'Success', new Date());
                resolve(results);
            }
          });
          connection.release();
        } else if (error) {
          // console.log(spcall1, 'Error', error);
          resolve(error);
        }
      });
    } catch (error) {
      // console.log(spcall1, 'Error', error);
      resolve(error);
    }
  });
}

IllionUpdation(args[1],args[2]);
