# Bulk Upload images to S3
# Condensed Steps for Transferring, Unzipping, and Uploading Files on an EC2 Instance

# Step 1: Copy the file 'output-001.zip' to the EC2 instance.
sudo scp -i chandrashekar.bemagoni_UAT.pem /home/<USER>/Desktop/arealytics/MediaUpload/output-001.zip  chandrashekar.bemagoni@*************:/home/<USER>/

# Step 2: Connect to the EC2 instance. 
ssh -i chandrashekar.bemagoni_UAT.pem chandrashekar.bemagoni@ec2-54-71-138-182

# Step 3: Install 'unzip' and 'awscli'.
sudo apt update
sudo apt install unzip awscli -y

# Step 4: Unzip the file 'output-001.zip'.
unzip output-001.zip

# Step 5: Configure AWS CLI , Add AccessKey , SecretKey and region.
aws configure

# Step 6: Upload images to the S3 bucket.
aws s3 cp output s3://<bucketname>/<path to images> --recursive