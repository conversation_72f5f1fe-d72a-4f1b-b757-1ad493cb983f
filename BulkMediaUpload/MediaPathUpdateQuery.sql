select media.MediaID as mediaid,fixedMedia.MediaID as fixedMediaID,
media.MediaSourceID as mediasource,fixedMedia.MediaSourceID as fixedMediaSourceID,
media.Path as mediapath,fixedMedia.Path as fixedMediaPath 
from Empirical_Prod.Media media inner join Empirical_DataStage.Media_08_07_2024 fixedMedia on media.MediaID=fixedMedia.MediaID;


update Empirical_Prod.Media media inner join Empirical_DataStage.Media_08_07_2024 fixedMedia on media.MediaID=fixedMedia.MediaID
set media.Path=fixedMedia.Path;