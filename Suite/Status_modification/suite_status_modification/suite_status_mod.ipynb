#%%
import pandas as pd
import geopandas as gpd
from sqlalchemy import create_engine
import numpy as np
import yaml
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
'''
-- Create a  table for ActiveSuites
DROP TABLE IF EXISTS ActiveSuites;
CREATE TABLE ActiveSuites AS
SELECT
    SuiteListingID,
    COUNT(SuiteID) AS ActiveSuites
FROM 
    Empirical_Prod.vw_Suite
WHERE 
    SuiteStatusName = 'Available'
    AND IsActive = 1
GROUP BY
    SuiteListingID;

'''
#%%
# Load configuration from config.yaml
with open('./config.yaml', 'r') as f:
    config = yaml.safe_load(f)
#%%
# Extract database connection details
source_db_config = config['source_db']
source_db_config
#%%
# Connection String
source_connection_string = f"mysql+mysqlconnector://{source_db_config['username']}:{source_db_config['password']}@{source_db_config['host']}:{source_db_config['port']}/{source_db_config['database_name']}"
source_database_engine = create_engine(source_connection_string)
#%%
sql_query = f'''
SELECT
    L.ListingID,
    L.PropertyID,
    L.RecordTypeName,
    L.GeneralUse,
    L.ListingStatusName,
    B.ParentCompanyName,
    L.CreatedDate,
    IFNULL(L.AskingLeaseRatePerMonthMin, 0) AS AskingLeaseRatePerMonthMin,
    IFNULL(L.AskingLeaseRatePerMonthMax, 0) AS AskingLeaseRatePerMonthMax,
    IFNULL(L.TotalMonthlyAskingMin, 0) AS TotalMonthlyAskingMin,
    IFNULL(L.TotalMonthlyAskingMax, 0) AS TotalMonthlyAskingMax,
    L.TotalAvailableSM,
    PD.PropertyName,
    PD.Address,
    PD.CityName,
    PD.StateName,
    PD.ZipCode,
    PD.PropertyUse,
    PD.CondoTypeName AS Strata,
    S.ActiveSuites,
    CONCAT(CAST(L.ListingID AS CHAR), '-', CAST(L.PropertyID AS CHAR)) AS UniqueID,
    ROW_NUMBER() OVER (PARTITION BY CONCAT(CAST(L.ListingID AS CHAR), '-', CAST(L.PropertyID AS CHAR)) ORDER BY L.ListingID) as rn
FROM 
    Empirical_Prod.vw_Listing L
LEFT JOIN 
    Empirical_Prod.vw_PropertyDetail PD ON PD.propertyid = L.propertyid
LEFT JOIN 
    Empirical_Prod.vw_CompanyBranch B ON B.companyid = L.branchid
LEFT JOIN
    Empirical_DataStage.ActiveSuites S ON S.SuiteListingID = L.ListingID
WHERE 
    L.IsActive = 1 
    AND L.ListingStatusName != 'Active'
    AND S.ActiveSuites IS NOT NULL;
'''
#%%
listing_prop_df = pd.read_sql(sql_query, con=source_database_engine)
#%%
listing_prop_df.info()
#%%
listing_prop_df[listing_prop_df['rn'] == 1].shape
#%%
listing_prop_df.sort_values('rn', ascending=False).head()
#%%
listing_prop_df[listing_prop_df['ListingID'] == 94037]
#%%
unique_listing_prop_df = listing_prop_df[listing_prop_df['rn'] == 1][['ListingID','PropertyID','ListingStatusName','ParentCompanyName','ActiveSuites']]
#%%
sql_query_2 = '''
select SuiteListingID,SuiteID,SuiteStatusID,SuiteStatusName from Empirical_Prod.vw_Suite 
WHERE SuiteStatusName = 'Available' AND IsActive = 1;'''
#%%
listing_suite_df =  pd.read_sql(sql_query_2, con=source_database_engine)
#%%
listing_suite_df.head()
#%%
listing_suite_merge_df = pd.merge(unique_listing_prop_df,listing_suite_df, left_on='ListingID', right_on='SuiteListingID')
#%%
listing_suite_merge_df.info()
#%%
check = listing_suite_merge_df.groupby(['ListingID','ActiveSuites']).size().reset_index(name='Count')
#%%
(check['ActiveSuites'] != check['Count']).sum()
#%%
listing_suite_merge_df.head()
#%%
listing_suite_merge_df['New_SuiteStatusName'] = listing_suite_merge_df['ListingStatusName']
#%%
listing_suite_merge_df['New_SuiteStatusName'].unique()
#%%
listing_suite_merge_df['New_SuiteStatusID'] = 2
#%%
listing_suite_merge_df.to_sql('Listing_Suite_relation_2024_07_24', con=source_connection_string, if_exists='replace', index=False, chunksize=10000)
#%%
listing_suite_merge_df['SuiteID'].nunique()
#%%
