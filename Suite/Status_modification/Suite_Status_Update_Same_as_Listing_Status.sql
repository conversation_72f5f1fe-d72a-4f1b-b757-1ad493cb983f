-- 21392 Leases for updating Lease status 
SELECT count(*)
FROM Empirical_Prod.Suite AS s 
INNER JOIN Empirical_DataStage.Listing_Suite_relation_2024_07_24 ls ON s.SuiteID = ls.SuiteID
INNER JOIN Empirical_Prod.SuiteStatus As ss ON ss.SuiteStatusID = s.SuiteStatusID
 ;

-- Change log
-- Drop the temporary table if it exists
DROP TEMPORARY TABLE IF EXISTS ChangeLogSuiteStatus;

-- Create a temporary table for change logs
CREATE TEMPORARY TABLE ChangeLogSuiteStatus AS
SELECT 
    NULL AS ChangeLogSuiteID,
    s.SuiteID,
    4 AS ChangeLogFieldID,
    ss.SuiteStatusName AS OldValue,
    ls.New_SuiteStatusName AS NewValue,
    22 AS ChangedBy,
    NOW() AS ChangedDate,
    1 AS ApplictionID,
    2 AS ActionID
FROM Empirical_Prod.Suite AS s 
INNER JOIN Empirical_DataStage.Listing_Suite_relation_2024_07_24 AS ls ON s.SuiteID = ls.SuiteID
INNER JOIN Empirical_Prod.SuiteStatus As ss ON ss.SuiteStatusID = s.SuiteStatusID;

-- Insert change log entries 
INSERT INTO Empirical_Prod.ChangeLogSuite 
SELECT * FROM ChangeLogSuiteStatus 
WHERE OldValue != NewValue;

-- Update SuiteStatusID in the Suite table
UPDATE Empirical_Prod.Suite s
JOIN Empirical_DataStage.Listing_Suite_relation_2024_07_24 ls ON s.SuiteID = ls.SuiteID
SET s.SuiteStatusID = ls.New_SuiteStatusID,
s.ModifiedBy = 22,
s.ModifiedDate = NOW()
-- WHERE s.SuiteID = 26261;
