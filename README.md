# Tenant_Ingestion


### 1 Illiion Raw data to Illion Stage

call Empirical_DataStage.CRE_Tenant_Import_ILLION_From_Raw_Data_DEC2023(9001);
Tenants_Stage_ILLION_NOV_2023 to Tenants_Stage_ILLION_Zessta

### 2 Illion Stage to Tenant Stage
call Empirical_DataStage.CRE_Tenant_Import_ILLION_Zessta(9001);

Tenants_Stage_ILLION_Zessta to Empirical_DataStage.Tenants_Stage

### 3 Tenant Stage to application tables
call Empirical_DataStage.CRE_Tenant_Import_Zessta(9001); -- P_BatchID

Tenants_Stage to 

select * from Empirical_Prod.Company order by CompanyID desc; --  Yes, Parent Company yes
select * from Empirical_Prod.CompanyRelationship order by CompanyRelationshipID desc;
select * from Empirical_Prod.Location order by LocationID desc;
select * from Empirical_Prod.Address order by AddressID desc ;
select * from Empirical_Prod.CompanyContact order by CompanyContactID desc;
select * from Empirical_Prod.CompanySupport order by CompanySupportID desc ;
select * from Empirical_Tenants.ConfirmedTenants order by ConfirmedTenantID desc ;
select * from Empirical_Prod.NationalIdentifiers order by ID desc;
SELECT * FROM Empirical_Tenants.ConfirmedTenantsFieldAudit order by ConfirmedTenantFieldAuditID desc;

 #### 3.1 Insert the Tenants with Multiple Providers

### 4 Tenants and Property Match



