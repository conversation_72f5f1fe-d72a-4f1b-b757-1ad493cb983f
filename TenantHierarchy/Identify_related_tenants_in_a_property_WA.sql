


with WATenantsStageData as (
select PropertyID,VendorID,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,GlobalUltimateParentCountry,TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,NationalID,
OfficePhone,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,StatusCode,LegalStatus,EmployeeCount,Tenant_Stage_Id,Latitude,Longitude,Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,PrimarySICDivision,PrimarySICDivisionDesc,SubHideReasonID,
ANZSICCode,NAICSCode,NACECode,HQ_ID,HQ_CompanyName,NumberofMembersinHierarchy
from ar-sandbox-data-lakehouse.ar_rep_Empirical_DataStage.Tenants_Stage where BatchID=20250813)
,
withoutGlobalUltimateParents as (select VendorID, GlobalUltimateParentDUNS
from WATenantsStageData
where GlobalUltimateParentDUNS is null and PropertyID is not null
),
globalUltimateParents as (select distinct GlobalUltimateParentDUNS --7160
from WATenantsStageData
where GlobalUltimateParentDUNS is not null and PropertyID is not null
),
globalUltimateParentsTenants as (select "DirectParent" as category ,VendorID as parentDuns,*
from WATenantsStageData
where VendorID in (select GlobalUltimateParentDUNS from globalUltimateParents)  and PropertyID is not null
)
,
globalUltimateParentsTenantsProperties as (select PropertyID,count(*) as propertyCount
from WATenantsStageData
where VendorID in (select GlobalUltimateParentDUNS from globalUltimateParents)  and PropertyID is not null group by PropertyID
)
,
subsidiaries as (
  select "DirectSubsidiary" as category,wt.GlobalUltimateParentDUNS as parentDuns, wt.*
  from WATenantsStageData as wt
  inner join globalUltimateParentsTenants p on p.VendorID =wt.GlobalUltimateParentDUNS   and p.PropertyID=wt.PropertyID-- 755912271
),
subsidiariesTenants as (
  select wt.GlobalUltimateParentDUNS as parentDuns, wt.*
  from WATenantsStageData as wt
  left join globalUltimateParentsTenants p on p.VendorID =wt.GlobalUltimateParentDUNS   and p.PropertyID=wt.PropertyID
  where p.VendorID is null and wt.GlobalUltimateParentDUNS is not null-- 755912271
),
subsidiariesTenantsGroupBy as (
  select wt.GlobalUltimateParentDUNS,wt.PropertyID
  from WATenantsStageData as wt
  left join globalUltimateParentsTenants p on p.VendorID =wt.GlobalUltimateParentDUNS   and p.PropertyID=wt.PropertyID
  where p.VendorID is null and wt.GlobalUltimateParentDUNS is not null and wt.PropertyID is not null-- 755912271
  group by wt.GlobalUltimateParentDUNS,wt.PropertyID having count(*)>1
),
rankedSubsidiaries as (
  select st.*,
         row_number() over (
           partition by st.GlobalUltimateParentDUNS, st.PropertyID
           order by st.Tenant_Stage_Id -- 👈 define what "first" means
         ) as rn
  from subsidiariesTenants st
  join subsidiariesTenantsGroupBy g
    on st.GlobalUltimateParentDUNS = g.GlobalUltimateParentDUNS
   and st.PropertyID = g.PropertyID
),
finalSubsidieriesWithoutUltimateTenant as (
  select "AutoSelectedParent" as category, * except (rn)
  from rankedSubsidiaries
  where rn = 1
),
skippedSubsidieriesWithoutUltimateTenant as (
  select "AutoSelectedChild" as category,* except (rn)
  from rankedSubsidiaries
  where rn > 1
),
finalTenantsToShow as(
select * from globalUltimateParentsTenants
 union all
 select * from finalSubsidieriesWithoutUltimateTenant
),
finalTenantsToHideAsSubsideries as(
 select * from subsidiaries where parentDuns!=VendorID
 union all
 select * from skippedSubsidieriesWithoutUltimateTenant
)
,
finalTenantsClassified as(

select * from globalUltimateParentsTenants  where parentDuns in (select parentDuns from subsidiaries where parentDuns!=VendorID)
 union all
 select * from finalSubsidieriesWithoutUltimateTenant
union all
 select * from subsidiaries where parentDuns!=VendorID
 union all
 select * from skippedSubsidieriesWithoutUltimateTenant
)


select * from -- finalSubsidieriesWithoutUltimateTenant
-- subsidiaries where parentDuns="755912271"
   finalTenantsClassified
order by PropertyID desc,parentDuns desc,category desc
 ;