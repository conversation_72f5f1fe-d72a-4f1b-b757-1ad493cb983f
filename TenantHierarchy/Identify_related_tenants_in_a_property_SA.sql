    drop temporary table if exists WATenantsStageData;
    create temporary table WATenantsStageData
    SELECT PropertyID, VendorID, ImmediateParentDUNS, ImmediateParentName, ImmediateParentCountry,
           DomesticParentDUNS, DomesticParentName, DomesticParentCountry,
           GlobalUltimateParentDUNS, GlobalUltimateParentName, GlobalUltimateParentCountry,
           TenantName, Address1, Address2, City, State, StateAbbr, CountryCode, PostalCode,
           NationalID, OfficePhone, CEOName, CEOTitle, LineOfBusiness, SICCode, Revenue,
           StatusCode, LegalStatus, EmployeeCount, Tenant_Stage_Id, Latitude, Longitude,
           Email, WebsiteURL, ASICEntityStatus, ASICEntityType, ASICEntityClass, ABNStatus,
           ABN_StatusFromDate, GST_Status, GST_StatusFromDate, RegistrationOrIncorporationDate,
           EntityAge, EmployeeIndicator, PrimarySICDesc, PrimarySIC3Digit, PrimarySIC3DigitDesc,
           PrimarySIC2Digit, PrimarySIC2DigitDesc, PrimarySICDivision, PrimarySICDivisionDesc,
           SubHideReasonID, ANZSICCode, NAICSCode, NACECode, HQ_ID, HQ_CompanyName, NumberofMembersinHierarchy
    FROM Empirical_DataStage.Tenants_Stage
    WHERE BatchID = 20250825;
     drop temporary table if exists immediateParentRelations ;
    create temporary table immediateParentRelations
     SELECT 'ImmediateParent' AS category,
           t1.VendorID AS child, t2.VendorID AS parent,
            t1.ImmediateParentDUNS AS ImmediateParentDUNS, t2.ImmediateParentDUNS AS ImmediateParentDUNS2,
           t1.GlobalUltimateParentDUNS AS GlobalUltimateParentDUNS1, t2.GlobalUltimateParentDUNS AS GlobalUltimateParentDUNS2,
           t1.DomesticParentDUNS as DomesticParentDUNS1,t2.DomesticParentDUNS as DomesticParentDUNS2,
           t1.PropertyID as PropertyID
    FROM WATenantsStageData t1
    JOIN WATenantsStageData1 t2
      ON t1.ImmediateParentDUNS = t2.VendorID
     AND t1.PropertyID = t2.PropertyID;
     drop temporary table if exists domesticParentRelations ;
    create temporary table domesticParentRelations
        SELECT 'DomesticParent' AS category,
           t1.VendorID AS child, t2.VendorID AS parent,
            t1.ImmediateParentDUNS AS ImmediateParentDUNS1, t2.ImmediateParentDUNS AS ImmediateParentDUNS2,
           t1.GlobalUltimateParentDUNS AS GlobalUltimateParentDUNS, t2.GlobalUltimateParentDUNS AS GlobalUltimateParentDUNS2,
           t1.DomesticParentDUNS as DomesticParentDUNS,t2.DomesticParentDUNS as DomesticParentDUNS2,
           t1.PropertyID as PropertyID
    FROM WATenantsStageData t1
    JOIN WATenantsStageData1 t2
      ON t1.DomesticParentDUNS = t2.VendorID
     AND t1.PropertyID = t2.PropertyID;--  AND t1.VendorID <> t2.DomesticParentDUNS;
          drop temporary table if exists globalUltimateParentRelations ;
    create temporary table globalUltimateParentRelations

    SELECT 'GlobalUltimateParent' AS category,
           t1.VendorID AS child, t2.VendorID AS parent,
           t1.ImmediateParentDUNS AS ImmediateParentDUNS1, t2.ImmediateParentDUNS AS ImmediateParentDUNS2,
           t1.GlobalUltimateParentDUNS AS GlobalUltimateParentDUNS, t2.GlobalUltimateParentDUNS AS GlobalUltimateParentDUNS2,
           t1.DomesticParentDUNS as DomesticParentDUNS,t2.DomesticParentDUNS as DomesticParentDUNS2,
           t1.PropertyID as PropertyID
    FROM WATenantsStageData t1
    JOIN WATenantsStageData1 t2
      ON t1.GlobalUltimateParentDUNS = t2.VendorID  AND t1.VendorID <> t2.GlobalUltimateParentDUNS
     AND t1.PropertyID = t2.PropertyID
;
drop temporary table if exists tenant_hierarchy_table;
create temporary table tenant_hierarchy_table
    WITH RECURSIVE tenant_hierarchy AS (
    -- Start from each tenant
    SELECT
        t.PropertyID,
        t.VendorID AS child,
        t.ImmediateParentDUNS,
        t.DomesticParentDUNS,
        t.GlobalUltimateParentDUNS,
        'TENANT' AS relation_type,
        0 AS level,
        t.VendorID AS rootTenant
    FROM WATenantsStageData t

    UNION ALL

    -- Step 1: Immediate Parent
    SELECT
        h.PropertyID,
        r.ImmediateParentDUNS AS child,
        NULL AS ImmediateParentDUNS,
        NULL AS DomesticParentDUNS,
        NULL AS GlobalUltimateParentDUNS,
        'IMMEDIATE_PARENT' AS relation_type,
        h.level + 1,
        h.rootTenant
    FROM tenant_hierarchy h
    JOIN immediateParentRelations r
      ON h.ImmediateParentDUNS = r.ImmediateParentDUNS
     AND h.PropertyID = r.PropertyID

    UNION ALL

    -- Step 2: Domestic Parent
    SELECT
        h.PropertyID,
        r.DomesticParentDUNS AS child,
        NULL,
        NULL,
        NULL,
        'DOMESTIC_PARENT' AS relation_type,
        h.level + 1,
        h.rootTenant
    FROM tenant_hierarchy h
    JOIN domesticParentRelations r
      ON h.DomesticParentDUNS = r.DomesticParentDUNS
     AND h.PropertyID = r.PropertyID

    UNION ALL

    -- Step 3: Global Ultimate Parent
    SELECT
        h.PropertyID,
        r.GlobalUltimateParentDUNS AS child,
        NULL,
        NULL,
        NULL,
        'GLOBAL_ULTIMATE_PARENT' AS relation_type,
        h.level + 1,
        h.rootTenant
    FROM tenant_hierarchy h
    JOIN globalUltimateParentRelations r
      ON h.GlobalUltimateParentDUNS = r.GlobalUltimateParentDUNS
     AND h.PropertyID = r.PropertyID
)
SELECT distinct PropertyID,child as VendorID,ImmediateParentDUNS,DomesticParentDUNS,GlobalUltimateParentDUNS-- ,level,rootTenant
FROM tenant_hierarchy
where PropertyID is not null and (-- level>0  or
(GlobalUltimateParentDUNS is not null or DomesticParentDUNS is not null or ImmediateParentDUNS is not null))
ORDER BY PropertyID, rootTenant, level;

WITH WATenantsStageData1 AS (
    SELECT PropertyID, VendorID, ImmediateParentDUNS, ImmediateParentName, ImmediateParentCountry,
           DomesticParentDUNS, DomesticParentName, DomesticParentCountry,
           GlobalUltimateParentDUNS, GlobalUltimateParentName, GlobalUltimateParentCountry,
           TenantName, Address1, Address2, City, State, StateAbbr, CountryCode, PostalCode,
           NationalID, OfficePhone, CEOName, CEOTitle, LineOfBusiness, SICCode, Revenue,
           StatusCode, LegalStatus, EmployeeCount, Tenant_Stage_Id, Latitude, Longitude,
           Email, WebsiteURL, ASICEntityStatus, ASICEntityType, ASICEntityClass, ABNStatus,
           ABN_StatusFromDate, GST_Status, GST_StatusFromDate, RegistrationOrIncorporationDate,
           EntityAge, EmployeeIndicator, PrimarySICDesc, PrimarySIC3Digit, PrimarySIC3DigitDesc,
           PrimarySIC2Digit, PrimarySIC2DigitDesc, PrimarySICDivision, PrimarySICDivisionDesc,
           SubHideReasonID, ANZSICCode, NAICSCode, NACECode, HQ_ID, HQ_CompanyName, NumberofMembersinHierarchy
    FROM Empirical_DataStage.Tenants_Stage
    WHERE BatchID = 20250825
)
select distinct
case
	when tht.VendorID=wa.ImmediateParentDUNS and tht.VendorID=wa.DomesticParentDUNS and tht.VendorID=wa.GlobalUltimateParentDUNS then "parent"
	when tht.VendorID=wa.ImmediateParentDUNS then "IP"
    when tht.VendorID=wa.DomesticParentDUNS then "DP"
    when tht.VendorID=wa.GlobalUltimateParentDUNS then "GP"
    else "tenant"

end as category,
 tht.* ,wa.*
 from tenant_hierarchy_table tht
inner join WATenantsStageData as wa on wa.VendorID=tht.VendorID and wa.PropertyID=tht.PropertyID
order by tht.PropertyID,tht.GlobalUltimateParentDUNS,tht.DomesticParentDUNS,tht.ImmediateParentDUNS
