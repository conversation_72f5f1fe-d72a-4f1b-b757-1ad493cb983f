with WATenantsStageData as (
  select PropertyID,VendorID,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
         DomesticParentDUNS,DomesticParentName,DomesticParentCountry,
         GlobalUltimateParentDUNS,GlobalUltimateParentName,GlobalUltimateParentCountry,
         TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,NationalID,
         OfficePhone,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,StatusCode,LegalStatus,
         EmployeeCount,Tenant_Stage_Id,Latitude,Longitude,Email,WebsiteURL,ASICEntityStatus,
         ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,GST_StatusFromDate,
         RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,PrimarySICDesc,
         PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
         PrimarySICDivision,PrimarySICDivisionDesc,SubHideReasonID,ANZSICCode,NAICSCode,
         NACECode,HQ_ID,HQ_CompanyName,NumberofMembersinHierarchy
  from ar-sandbox-data-lakehouse.ar_rep_Empirical_DataStage.Tenants_Stage
  where BatchID=20250813
),

-- Subsidiaries missing their parent tenant in the same property
subsidiariesTenants as (
  select wt.GlobalUltimateParentDUNS as parentDuns, wt.*
  from WATenantsStageData as wt
  left join (
    select VendorID, PropertyID
    from WATenantsStageData
    where VendorID is not null
  ) p
  on p.VendorID = wt.GlobalUltimateParentDUNS and p.PropertyID = wt.PropertyID
  where p.VendorID is null and wt.GlobalUltimateParentDUNS is not null
),

-- Find groups where multiple subsidiaries share the same parent+property
subsidiariesTenantsGroupBy as (
  select wt.GlobalUltimateParentDUNS, wt.PropertyID
  from subsidiariesTenants wt
  group by wt.GlobalUltimateParentDUNS, wt.PropertyID
  having count(*) > 1
),

-- Rank subsidiaries to auto-select one parent, mark others as children
rankedSubsidiaries as (
  select st.*,
         row_number() over (
           partition by st.GlobalUltimateParentDUNS, st.PropertyID
           order by st.Tenant_Stage_Id
         ) as rn
  from subsidiariesTenants st
  join subsidiariesTenantsGroupBy g
    on st.GlobalUltimateParentDUNS = g.GlobalUltimateParentDUNS
   and st.PropertyID = g.PropertyID
),

-- Keep only AutoSelected tenants
autoSelected as (
  select "AutoSelectedParent" as category, * except (rn)
  from rankedSubsidiaries
  where rn = 1

  union all

  select "AutoSelectedChild" as category, * except (rn)
  from rankedSubsidiaries
  where rn > 1
)

,
totals as (
  select count(*) as total_cnt from autoSelected
)

select 'ImmediateParentDUNS' as column_name, count(ImmediateParentDUNS) as populated_cnt,
       round(count(ImmediateParentDUNS)*100.0/sum(t.total_cnt), 2) as population_rate
from autoSelected, totals t

union all
select 'ImmediateParentName', count(ImmediateParentName),
       round(count(ImmediateParentName)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'ImmediateParentCountry', count(ImmediateParentCountry),
       round(count(ImmediateParentCountry)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'DomesticParentDUNS', count(DomesticParentDUNS),
       round(count(DomesticParentDUNS)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'DomesticParentName', count(DomesticParentName),
       round(count(DomesticParentName)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'DomesticParentCountry', count(DomesticParentCountry),
       round(count(DomesticParentCountry)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'GlobalUltimateParentDUNS', count(GlobalUltimateParentDUNS),
       round(count(GlobalUltimateParentDUNS)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'GlobalUltimateParentName', count(GlobalUltimateParentName),
       round(count(GlobalUltimateParentName)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'GlobalUltimateParentCountry', count(GlobalUltimateParentCountry),
       round(count(GlobalUltimateParentCountry)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'TenantName', count(TenantName),
       round(count(TenantName)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t
union all
select
  'NationalID' as column_name,
  count(NationalID) as populated_count,
  round(count(NationalID) * 100.0 / sum(t.total_cnt), 2) as population_pct
from autoSelected, totals t

union all
select
  'OfficePhone',
  count(OfficePhone),
  round(count(OfficePhone) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'CEOName',
  count(CEOName),
  round(count(CEOName) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'CEOTitle',
  count(CEOTitle),
  round(count(CEOTitle) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'LineOfBusiness',
  count(LineOfBusiness),
  round(count(LineOfBusiness) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'SICCode',
  count(SICCode),
  round(count(SICCode) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'Revenue',
  count(Revenue),
  round(count(Revenue) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'StatusCode',
  count(StatusCode),
  round(count(StatusCode) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'LegalStatus',
  count(LegalStatus),
  round(count(LegalStatus) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'EmployeeCount',
  count(EmployeeCount),
  round(count(EmployeeCount) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'Tenant_Stage_Id',
  count(Tenant_Stage_Id),
  round(count(Tenant_Stage_Id) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'Latitude',
  count(Latitude),
  round(count(Latitude) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'Longitude',
  count(Longitude),
  round(count(Longitude) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'Email',
  count(Email),
  round(count(Email) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'WebsiteURL',
  count(WebsiteURL),
  round(count(WebsiteURL) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'ASICEntityStatus',
  count(ASICEntityStatus),
  round(count(ASICEntityStatus) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'ASICEntityType',
  count(ASICEntityType),
  round(count(ASICEntityType) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'ASICEntityClass',
  count(ASICEntityClass),
  round(count(ASICEntityClass) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'ABNStatus',
  count(ABNStatus),
  round(count(ABNStatus) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'ABN_StatusFromDate',
  count(ABN_StatusFromDate),
  round(count(ABN_StatusFromDate) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'GST_Status',
  count(GST_Status),
  round(count(GST_Status) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'GST_StatusFromDate',
  count(GST_StatusFromDate),
  round(count(GST_StatusFromDate) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'RegistrationOrIncorporationDate',
  count(RegistrationOrIncorporationDate),
  round(count(RegistrationOrIncorporationDate) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'EntityAge',
  count(EntityAge),
  round(count(EntityAge) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'EmployeeIndicator',
  count(EmployeeIndicator),
  round(count(EmployeeIndicator) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select
  'PrimarySICDesc',
  count(PrimarySICDesc),
  round(count(PrimarySICDesc) * 100.0 / sum(t.total_cnt), 2)
from autoSelected, totals t

union all

select 'PrimarySIC3Digit', count(PrimarySIC3Digit), round(count(PrimarySIC3Digit)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'PrimarySIC3DigitDesc', count(PrimarySIC3DigitDesc), round(count(PrimarySIC3DigitDesc)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'PrimarySIC2Digit', count(PrimarySIC2Digit), round(count(PrimarySIC2Digit)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'PrimarySIC2DigitDesc', count(PrimarySIC2DigitDesc), round(count(PrimarySIC2DigitDesc)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'PrimarySICDivision', count(PrimarySICDivision), round(count(PrimarySICDivision)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'PrimarySICDivisionDesc', count(PrimarySICDivisionDesc), round(count(PrimarySICDivisionDesc)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'SubHideReasonID', count(SubHideReasonID), round(count(SubHideReasonID)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'ANZSICCode', count(ANZSICCode), round(count(ANZSICCode)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'NAICSCode', count(NAICSCode), round(count(NAICSCode)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'NACECode', count(NACECode), round(count(NACECode)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'HQ_ID', count(HQ_ID), round(count(HQ_ID)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'HQ_CompanyName', count(HQ_CompanyName), round(count(HQ_CompanyName)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t

union all
select 'NumberofMembersinHierarchy', count(NumberofMembersinHierarchy), round(count(NumberofMembersinHierarchy)*100.0/sum(t.total_cnt), 2)
from autoSelected, totals t
;
