import pandas as pd

# Load CSV
df = pd.read_csv("/home/<USER>/Documents/Data/tenant/sa_tenants_hierarchy_In_Property.csv")

# List of columns to check (adjust if needed)
columns_to_check = [
    "TenantName", "NationalID", "OfficePhone", "CEOName", "CEOTitle", "LineOfBusiness",
    "SICCode", "Revenue", "StatusCode", "LegalStatus", "EmployeeCount", "Tenant_Stage_Id",
    "Latitude", "Longitude", "Email", "WebsiteURL", "ASICEntityStatus", "ASICEntityType",
    "ASICEntityClass", "ABNStatus", "ABN_StatusFromDate", "GST_Status", "GST_StatusFromDate",
    "RegistrationOrIncorporationDate", "EntityAge", "EmployeeIndicator", "PrimarySICDesc",
    "PrimarySIC3Digit", "PrimarySIC3DigitDesc", "PrimarySIC2Digit", "PrimarySIC2DigitDesc",
    "PrimarySICDivision", "PrimarySICDivisionDesc", "SubHideReasonID", "ANZSICCode",
    "NAICSCode", "NACECode", "HQ_ID", "HQ_CompanyName", "NumberofMembersinHierarchy"
]
group_keys = ["PropertyID", "GlobalUltimateParentDUNS"]

# Total rows
total_cnt = len(df)

def compute_group_stats(group):
    total_cnt = len(group)
    stats = {}
    for col in columns_to_check:
        populated_cnt = group[col].notna().sum()
        population_rate = round(populated_cnt * 100.0 / total_cnt, 2)
        stats[f"{col}_populated_cnt"] = populated_cnt
        stats[f"{col}_population_rate"] = population_rate
    return pd.Series(stats)
# Apply per group
results_df = df.groupby(group_keys).apply(compute_group_stats).reset_index()

# Put results into DataFrame
#results_df = pd.DataFrame(results, columns=["column_name", "populated_cnt", "population_rate"])

# Show results
print(results_df)

# Save to Excel
results_df.to_csv("/home/<USER>/Documents/Data/tenant/sa_tenant_data_population_stats.csv", index=False)
