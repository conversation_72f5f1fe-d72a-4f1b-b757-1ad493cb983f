import pandas as pd
import re
from rapidfuzz import fuzz

# -----------------------------
# Normalization helpers
# -----------------------------
ABBREVIATIONS = {
    r"\bTce\b": "Terrace",
    r"\bCt\b": "Court",
    r"\bSt\b": "Street",
    r"\bPl\b": "Place",
    r"\bRd\b": "Road",
    r"\bAve\b": "Avenue",
    r"\bHwy\b": "Highway",
    r"\bBlvd\b": "Boulevard",
    r"\bSq\b": "Square",
    r"\bCr\b": "Crescent",
    r"\bDr\b": "Drive",
    r"\bLn\b": "Lane",
}

def normalize_address(addr: str) -> str:
    if pd.isna(addr):
        return ""
    addr = addr.strip().title()  # Title case
    # Replace abbreviations
    for abbr, full in ABBREVIATIONS.items():
        addr = re.sub(abbr, full, addr, flags=re.IGNORECASE)
    # Remove commas and extra spaces
    addr = re.sub(r"[,\.\-]", " ", addr)
    addr = re.sub(r"\s+", " ", addr)
    return addr.strip()

def number_in_range(num: str, rng: str) -> bool:
    """
    Check if number like '58A' is inside '58-60' or '47A-47B'
    """
    # Extract digits only for range compare
    digits = re.findall(r"\d+", rng)
    if len(digits) >= 2:
        start, end = int(digits[0]), int(digits[-1])
        num_digits = re.findall(r"\d+", num)
        if num_digits:
            num_val = int(num_digits[0])
            return start <= num_val <= end
    return False

def is_match(addr1: str, addr2: str) -> bool:
    a1 = normalize_address(addr1)
    a2 = normalize_address(addr2)

    if not a1 or not a2:
        return False

    # Exact match
    if a1 == a2:
        return True

    # Check number-range equivalence
    if number_in_range(a1, a2) or number_in_range(a2, a1):
        return True

    # Fuzzy match threshold
    if fuzz.ratio(a1, a2) >= 85:
        return True

    return False

# -----------------------------
# Main comparison
# -----------------------------
df = pd.read_csv("/home/<USER>/Downloads/PropertyTenantAddressData - PropertyTenantAddressData.csv")

results = []
for _, row in df.iterrows():
    tenant = row["TenantAddress"]
    primary = row["PropertyPrimaryAddress"]
    secondary = row.get("PropertySecondaryAddress", "")

    if is_match(tenant, primary) or is_match(tenant, secondary):
        status = "Match"
    else:
        status = "No-Match"

    results.append({**row.to_dict(), "Status": status})

df_out = pd.DataFrame(results)

# Save only non-matches
df_no_match = df_out[df_out["Status"] == "No-Match"]
df_no_match.to_csv("/home/<USER>/Documents/Data/tenant/Tenant_NoMatch_List.xlsx", index=False)

print("✅ Done. Output saved to Tenant_NoMatch_List.xlsx")
