drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where IsActive=1
);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
select distinct StrataPropertyID,MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where MasterPropertyID in
(select * from tempMasterProperties) and IsActive=1
);

# Getting all Media of Strata Properties
drop temporary table if exists tempStrataPropertiesMedia;
create temporary table tempStrataPropertiesMedia(
select * from Empirical_Prod.MediaRelationship where PropertyID in
(select StrataPropertyID from tempStrataProperties)
);
#Attaching Master to above StrataPropertiesMedia
drop temporary table if exists MediaRelationshipToBeUpdatedWithMasterProperty;
create  temporary table MediaRelationshipToBeUpdatedWithMasterProperty(
select distinct MediaRelationshipID,PropertyID,MasterPropertyID,MediaTypeID from tempStrataPropertiesMedia spm 
inner join tempStrataProperties sp on spm.PropertyID=sp.StrataPropertyID
);

#Moving Tenant Roster,Listing Signs from Child to Master Strata
select mr.PropertyID,mrp.MasterPropertyID,mr.MediaRelationTypeID from Empirical_Prod.MediaRelationship mr 
inner join MediaRelationshipToBeUpdatedWithMasterProperty mrp on mr.MediaRelationshipID=mrp.MediaRelationshipID and mr.PropertyID=mrp.PropertyID
where  mr.MediaTypeID in (15,13) and mr.PropertyID!=mrp.MasterPropertyID ;#and mr.MediaRelationTypeID=1 #count = 695
/*
#Updating PropertyID,RelationID for MediaRelationTypeID = 1 
update Empirical_Prod.MediaRelationship mr 
inner join MediaRelationshipToBeUpdatedWithMasterProperty mrp on mr.MediaRelationshipID=mrp.MediaRelationshipID and mr.PropertyID=mrp.PropertyID
set mr.PropertyID=mrp.MasterPropertyID, mr.RelationID = mrp.MasterPropertyID ,mr.IsDefault = 0
where 
	mr.MediaTypeID in (15,13) and 
	mr.MediaRelationTypeID=1 and 
	mr.PropertyID!=mrp.MasterPropertyID;
	# and mr.PropertyID=260602;
 
#Updating PropertyID for MediaRelationTypeID not in 1,4 (Property,Sale) 
update Empirical_Prod.MediaRelationship mr 
inner join MediaRelationshipToBeUpdatedWithMasterProperty mrp on mr.MediaRelationshipID=mrp.MediaRelationshipID and mr.PropertyID=mrp.PropertyID
set mr.PropertyID=mrp.MasterPropertyID ,mr.IsDefault = 0
where 
	mr.MediaTypeID in (15,13) and 
	mr.MediaRelationTypeID not in (1,3,4) and mr.PropertyID!=mrp.MasterPropertyID;
	# and mr.PropertyID=260602;
*/

select * from Empirical_Prod.MediaRelationType;
# Inserting a copy of Property Media(Building Images) to Master Strata

select mr.MediaRelationTypeID ,mr.MediaTypeID,count(*)
from Empirical_Prod.MediaRelationship mr 
inner join MediaRelationshipToBeUpdatedWithMasterProperty mrp on mr.MediaRelationshipID=mrp.MediaRelationshipID and mr.PropertyID=mrp.PropertyID
where mr.MediaTypeID=3 group by mr.MediaRelationTypeID,mr.MediaTypeID;

select mr.MediaID,mr.MediaRelationTypeID,mr.MediaTypeID,mr.MediaSubTypeID,RelationID,mr.PropertyID,mrp.MasterPropertyID, 0 as IsDefault,mr.IsActive 
from Empirical_Prod.MediaRelationship mr 
inner join MediaRelationshipToBeUpdatedWithMasterProperty mrp on mr.MediaRelationshipID=mrp.MediaRelationshipID and mr.PropertyID=mrp.PropertyID
where mr.MediaTypeID=3 and mr.MediaRelationTypeID not in (1,3,4); # count =  

select mr.MediaID,mr.MediaRelationTypeID,mr.MediaTypeID,mr.MediaSubTypeID,RelationID,mr.PropertyID,mrp.MasterPropertyID, 0 as IsDefault,mr.IsActive 
from Empirical_Prod.MediaRelationship mr 
inner join MediaRelationshipToBeUpdatedWithMasterProperty mrp on mr.MediaRelationshipID=mrp.MediaRelationshipID and mr.PropertyID=mrp.PropertyID
where mr.MediaTypeID=3 and mr.MediaRelationTypeID in (1); # count= 

#checking Queries
select * from Empirical_Prod.MediaRelationship where MediaTypeID=3 and MediaRelationTypeID=1 and PropertyID=182163;
select * from Empirical_Prod.MediaRelationship where MediaTypeID=3 and MediaRelationTypeID=1 and PropertyID=172344;


/*
# Inserting New Building Images for MediaRelationTypeID=1
insert into Empirical_Prod.MediaRelationship
select null as MediaRelationshipID, mr.MediaID,mr.MediaRelationTypeID,mr.MediaTypeID,mr.MediaSubTypeID,mrp.MasterPropertyID as RelationID,mrp.MasterPropertyID as PropertyID,0 as IsDefault,mr.IsActive 
from Empirical_Prod.MediaRelationship mr 
inner join MediaRelationshipToBeUpdatedWithMasterProperty mrp on mr.MediaRelationshipID=mrp.MediaRelationshipID and mr.PropertyID=mrp.PropertyID
where  mr.MediaTypeID=3 and mr.MediaRelationTypeID=1 # and mr.PropertyID = 260602;

# Updating PropertyID for BuildingImages for MediaRelationTypeID not in 1,4 (Property,Sale) 
update Empirical_Prod.MediaRelationship mr 
inner join MediaRelationshipToBeUpdatedWithMasterProperty mrp on mr.MediaRelationshipID=mrp.MediaRelationshipID and mr.PropertyID=mrp.PropertyID
set mr.PropertyID=mrp.MasterPropertyID ,mr.IsDefault = 0
where mr.MediaTypeID =3 and mr.MediaRelationTypeID not in (1,3,4) and mr.PropertyID!=mrp.MasterPropertyID; -- and mrp.MediaRelationshipID = 142093;
*/

-- select * from Empirical_Prod.MediaRelationship where RelationID  = 97107
