drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where IsActive=1
);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
select distinct StrataPropertyID,MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where MasterPropertyID in
(select * from tempMasterProperties) and IsActive=1
);

drop temporary table if exists currentPropertyListingGroups;
create temporary table currentPropertyListingGroups(
select distinct TSP.MasterPropertyID, TSP.StrataPropertyID, gr.GroupID, lg.ListingID from Empirical_DataStage.tempStrataProperties as TSP 
inner join Empirical_Prod.GroupRelationship  as gr  on gr.PropertyID = TSP.StrataPropertyID
inner join Empirical_Prod.ListingGroup as lg on lg.GroupID = gr.GroupID
);

select * from currentPropertyListingGroups;
-- Master Property Groups and  ListingGroup
drop temporary table if exists TargetPropertiesWithoutGroupRelationship;
create temporary table TargetPropertiesWithoutGroupRelationship(
select  distinct p.PropertyName,p.PropertyID FROM Empirical_DataStage.currentPropertyListingGroups TSP
inner join Empirical_Prod.Property as p on p.PropertyID=TSP.MasterPropertyID
left JOIN Empirical_Prod.GroupRelationship GR ON TSP.MasterPropertyID = GR.PropertyID
where GR.GroupRelationshipID is null
);

select * from TargetPropertiesWithoutGroupRelationship;

INSERT INTO `Empirical_Prod`.`Group`
(`GroupID`,`GroupName`,`DateModified`)
 select null,PropertyName,now() from TargetPropertiesWithoutGroupRelationship where PropertyID=154628;
 select last_insert_id();-- 424515
-- (null,<{GroupName: }>,<{DateModified: }>);

INSERT INTO `Empirical_Prod`.`GroupRelationship`
(`GroupRelationshipID`,`GroupID`,`StructureID`,`RightID`,`PropertyID`,`ParcelID`,`LotID`)
select null,g.GroupID,null,null,p.PropertyID,null,null 
from TargetPropertiesWithoutGroupRelationship as p
inner join Empirical_Prod.`Group` as g on g.GroupName=p.PropertyName and Date(g.DateModified)= Date(now())  and p.PropertyID=154628;

select last_insert_id();
