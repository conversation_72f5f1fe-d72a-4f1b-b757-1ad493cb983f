drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where IsActive=1
);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
select distinct StrataPropertyID,MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where MasterPropertyID in
(select * from tempMasterProperties) and IsActive=1
);

-- Get Confirmed Leases which needs move from  Child PID to Master PID

select TSP.StrataPropertyID, TSP.MasterPropertyID, l.LeaseID, l.TenantName from Empirical_DataStage.
tempStrataProperties as TSP 
inner join Empirical_Prod.Lease  as l  on l.PropertyID = TSP.StrataPropertyID  where  l.PropertyID=375377; #113229


-- Updated Confirmed Leases With Master PID 
/**

UPDATE Empirical_Prod.Lease l
INNER JOIN Empirical_DataStage.tempStrataProperties TSP 
ON l.PropertyID = TSP.StrataPropertyID and l.PropertyID=375377
SET l.PropertyID = TSP.MasterPropertyID,ModifiedBy=22, ModifiedDate =now();

**/

-- Get Un Confirmed Leases/Source Confrimations Leases which needs move from  Child PID to Master PID

select TSP.StrataPropertyID, TSP.MasterPropertyID, sl.LeaseID, sl.StageID, sl.`Alt Tenant Name` from Empirical_DataStage.
tempStrataProperties as TSP 
inner join Empirical_DataStage.LeaseComp_Stage_For_Grouping  as sl  on sl.PropertyID = TSP.StrataPropertyID;  # where  sl.PropertyID=375377;


-- Updated Un Confirmed Leases/Source Confrimations Leases with Master PID

/**

UPDATE Empirical_DataStage.LeaseComp_Stage_For_Grouping sl
INNER JOIN Empirical_DataStage.tempStrataProperties TSP 
ON sl.PropertyID = TSP.StrataPropertyID and sl.PropertyID=409119
SET sl.PropertyID = TSP.MasterPropertyID, ModifiedBy=22, ModifiedDate =now();

**/
