drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where IsActive=1
);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
select distinct StrataPropertyID,MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where MasterPropertyID in
(select * from tempMasterProperties) and IsActive=1
);
-- select * from tempStrataProperties;
-- List of Confirmations of the ConfirmedTenants of above source properties
drop temporary table if exists ConfirmationsOfTier1Tenants;
create temporary table ConfirmationsOfTier1Tenants(
select  ts.*,TSP.MasterPropertyID from tempStrataProperties TSP 
inner join Empirical_Prod.SuiteTenant st on st.PropertyID=TSP.StrataPropertyID
inner join Empirical_Tenants.ConfirmedTenants ct on st.ConfirmedTenantID=ct.ConfirmedTenantID
inner join Empirical_DataStage.Tenants_Stage ts on ct.CompanyID=ts.BranchID
where TSP.StrataPropertyID=338049
);

-- select * from ConfirmationsOfTier1Tenants;

Select * from Empirical_Prod.SuiteTenant st inner join tempStrataProperties pc on st.PropertyID=pc.StrataPropertyID  where PropertyID=338049;#where SuiteID is not null;
-- Update PropertyID in Suite tenant with TargetPID
update Empirical_Prod.SuiteTenant st inner join tempStrataProperties pc on st.PropertyID=pc.StrataPropertyID 
set st.PropertyID=pc.MasterPropertyID,ModifiedBy=22,ModifiedDate=now()
where PropertyID=338049;

select * from Empirical_DataStage.Tenants_Stage ts inner join ConfirmationsOfTier1Tenants ct on ts.Tenant_Stage_Id=ct.Tenant_Stage_Id;
-- Update PropertyID in tenants stage for confirmations with TargetPID
-- update Empirical_DataStage.Tenants_Stage ts inner join ConfirmationsOfTier1Tenants ct on ts.Tenant_Stage_Id=ct.Tenant_Stage_Id  set ts.PropertyID=ct.MasterPropertyID,ts.ModifiedBy=22,ts.ModifiedDate=now();



-- Tier3 Tenants

drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship
);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
select distinct StrataPropertyID,MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where MasterPropertyID in
(select * from tempMasterProperties)
);
-- select * from tempStrataProperties
-- List of Confirmations of the ConfirmedTenants of above source properties
drop temporary table if exists ListOfTier3Tenants;
create temporary table ListOfTier3Tenants(
select  ts.*,TSP.MasterPropertyID from tempStrataProperties TSP 
inner join Empirical_DataStage.Tenants_Stage ts on ts.PropertyID=TSP.StrataPropertyID
where ts.BranchID is null and TSP.StrataPropertyID= 340105
);

select * from ListOfTier3Tenants;
select * from Empirical_DataStage.Tenants_Stage ts inner join ListOfTier3Tenants ct on ts.Tenant_Stage_Id=ct.Tenant_Stage_Id;
-- Update PropertyID in tenants stage for Tier3 tenants with TargetPID
update Empirical_DataStage.Tenants_Stage ts inner join ListOfTier3Tenants ct on ts.Tenant_Stage_Id=ct.Tenant_Stage_Id set ts.PropertyID=ct.MasterPropertyID,ts.ModifiedBy=22,ts.ModifiedDate=now();