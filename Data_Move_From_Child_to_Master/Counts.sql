-- Tenants
-- ================================================================
-- Starta T1 Counts
drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where IsActive=1
);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
SELECT  P.StrataPropertyID, P.MasterPropertyID, A.StateID
FROM Empirical_Prod.PropertyStrataRelationship P
INNER JOIN Empirical_Prod.Address A ON A.ParentID = P.StrataPropertyID
WHERE P.MasterPropertyID IN (SELECT MasterPropertyID FROM tempMasterProperties)
AND A.ParentTableID = 1 AND A.Sequence = 1 AND P.IsActive=1
);
-- select * from tempStrataProperties;
-- List of Confirmations of the ConfirmedTenants of above source properties
drop temporary table if exists ConfirmationsOfTier1Tenants;
create temporary table ConfirmationsOfTier1Tenants(
select ts.*,TSP.MasterPropertyID ,TSP.StateID from tempStrataProperties TSP 
inner join Empirical_Prod.SuiteTenant st on st.PropertyID=TSP.StrataPropertyID
inner join Empirical_Tenants.ConfirmedTenants ct on st.ConfirmedTenantID=ct.ConfirmedTenantID
inner join Empirical_DataStage.Tenants_Stage ts on ct.CompanyID=ts.BranchID
-- where TSP.StrataPropertyID=338049
);

-- select * from ConfirmationsOfTier1Tenants;
-- counts
Select pc.StateID , count(distinct SuiteTenantID) from Empirical_Prod.SuiteTenant st inner join tempStrataProperties pc on st.PropertyID=pc.StrataPropertyID group by pc.StateID;

-- Master Starta T1 Counts
drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where IsActive=1

);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
SELECT P.StrataPropertyID, P.MasterPropertyID, A.StateID
FROM Empirical_Prod.PropertyStrataRelationship P
INNER JOIN Empirical_Prod.Address A ON A.ParentID = P.StrataPropertyID
WHERE P.MasterPropertyID IN (SELECT MasterPropertyID FROM tempMasterProperties)
AND A.ParentTableID = 1 AND A.Sequence = 1 AND P.IsActive=1
);
-- select * from tempStrataProperties;
-- List of Confirmations of the ConfirmedTenants of above source properties
drop temporary table if exists ConfirmationsOfTier1Tenants;
create temporary table ConfirmationsOfTier1Tenants(
select  ts.*,TSP.MasterPropertyID ,TSP.StateID from tempStrataProperties TSP 
inner join Empirical_Prod.SuiteTenant st on st.PropertyID=TSP.MasterPropertyID
inner join Empirical_Tenants.ConfirmedTenants ct on st.ConfirmedTenantID=ct.ConfirmedTenantID
inner join Empirical_DataStage.Tenants_Stage ts on ct.CompanyID=ts.BranchID
-- where TSP.StrataPropertyID=338049
);

-- select * from ConfirmationsOfTier1Tenants;
-- counts
Select pc.StateID , count(distinct SuiteTenantID) from Empirical_Prod.SuiteTenant st inner join tempStrataProperties pc on st.PropertyID=pc.MasterPropertyID group by pc.StateID;

-- =======================================================================================
-- Starta T3 Counts
drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where IsActive=1

);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
SELECT P.StrataPropertyID, P.MasterPropertyID, A.StateID
FROM Empirical_Prod.PropertyStrataRelationship P
INNER JOIN Empirical_Prod.Address A ON A.ParentID = P.StrataPropertyID
WHERE P.MasterPropertyID IN (SELECT MasterPropertyID FROM tempMasterProperties)
AND A.ParentTableID = 1 AND A.Sequence = 1 AND P.IsActive=1
);
-- select * from tempStrataProperties
-- List of Confirmations of the ConfirmedTenants of above source properties
drop temporary table if exists ListOfTier3Tenants;
create temporary table ListOfTier3Tenants(
select  ts.*,TSP.MasterPropertyID,TSP.StateID from tempStrataProperties TSP 
inner join Empirical_DataStage.Tenants_Stage ts on ts.PropertyID=TSP.StrataPropertyID
where ts.BranchID is null #and TSP.StrataPropertyID= 340105
);

select StateID ,count(distinct Tenant_Stage_Id) from ListOfTier3Tenants group by StateID;

-- Master starta T3 Counts

drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where IsActive=1

);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
SELECT P.StrataPropertyID, P.MasterPropertyID, A.StateID
FROM Empirical_Prod.PropertyStrataRelationship P
INNER JOIN Empirical_Prod.Address A ON A.ParentID = P.StrataPropertyID
WHERE P.MasterPropertyID IN (SELECT MasterPropertyID FROM tempMasterProperties)
AND A.ParentTableID = 1 AND A.Sequence = 1 AND P.IsActive=1
);
-- select * from tempStrataProperties
-- List of Confirmations of the ConfirmedTenants of above source properties
drop temporary table if exists ListOfTier3Tenants;
create temporary table ListOfTier3Tenants(
select  ts.*,TSP.MasterPropertyID,TSP.StateID from tempStrataProperties TSP 
inner join Empirical_DataStage.Tenants_Stage ts on ts.PropertyID=TSP.MasterPropertyID
where ts.BranchID is null #and TSP.StrataPropertyID= 340105
);

select StateID ,count(distinct Tenant_Stage_Id) from ListOfTier3Tenants group by StateID;

-- ==============================================================================================================
-- Leases
drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where IsActive=1
);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
SELECT P.StrataPropertyID, P.MasterPropertyID, A.StateID
FROM Empirical_Prod.PropertyStrataRelationship P
INNER JOIN Empirical_Prod.Address A ON A.ParentID = P.StrataPropertyID
WHERE P.MasterPropertyID IN (SELECT MasterPropertyID FROM tempMasterProperties)
AND A.ParentTableID = 1 AND A.Sequence = 1 AND P.IsActive=1
);

-- Get Confirmed Leases which needs move from  Child PID to Master PID
-- counts

select TSP.StateID,count(distinct LeaseID) from Empirical_DataStage.
tempStrataProperties as TSP 
inner join Empirical_Prod.Lease  as l  on l.PropertyID = TSP.StrataPropertyID group by TSP.StateID;

select TSP.StateID,count(distinct StageID) from Empirical_DataStage.
tempStrataProperties as TSP 
inner join Empirical_DataStage.LeaseComp_Stage_For_Grouping  as sl  on sl.PropertyID = TSP.StrataPropertyID group by TSP.StateID;


select TSP.StateID,count(distinct LeaseID) from Empirical_DataStage.
tempStrataProperties as TSP 
inner join Empirical_Prod.Lease  as l  on l.PropertyID = TSP.MasterPropertyID group by TSP.StateID;

select TSP.StateID,count(distinct StageID) from Empirical_DataStage.
tempStrataProperties as TSP 
inner join Empirical_DataStage.LeaseComp_Stage_For_Grouping  as sl  on sl.PropertyID = TSP.MasterPropertyID group by TSP.StateID;

-- ===============================================================================================================================
-- Listings 

drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where IsActive=1

);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
SELECT P.StrataPropertyID, P.MasterPropertyID, A.StateID
FROM Empirical_Prod.PropertyStrataRelationship P
INNER JOIN Empirical_Prod.Address A ON A.ParentID = P.StrataPropertyID
WHERE P.MasterPropertyID IN (SELECT MasterPropertyID FROM tempMasterProperties)
AND A.ParentTableID = 1 AND A.Sequence = 1 AND P.IsActive=1
);


select  TSP.StateID , count(distinct ListingGroupID) from Empirical_DataStage.
tempStrataProperties as TSP 
inner join Empirical_Prod.GroupRelationship  as gr  on gr.PropertyID = TSP.StrataPropertyID
inner join Empirical_Prod.ListingGroup as lg on lg.GroupID = gr.GroupID
group by TSP.StateID;

select  TSP.StateID , count(distinct ListingGroupID) from Empirical_DataStage.
tempStrataProperties as TSP 
inner join Empirical_Prod.GroupRelationship  as gr  on gr.PropertyID = TSP.MasterPropertyID
inner join Empirical_Prod.ListingGroup as lg on lg.GroupID = gr.GroupID
group by TSP.StateID;

-- =============================================================================================================================
-- Media

-- Strata Counts
drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where IsActive=1

);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
SELECT P.StrataPropertyID, P.MasterPropertyID, A.StateID
FROM Empirical_Prod.PropertyStrataRelationship P
INNER JOIN Empirical_Prod.Address A ON A.ParentID = P.StrataPropertyID
WHERE P.MasterPropertyID IN (SELECT MasterPropertyID FROM tempMasterProperties)
AND A.ParentTableID = 1 AND A.Sequence = 1 AND P.IsActive=1
);

# Getting all Media of Strata Properties
drop temporary table if exists tempStrataPropertiesMedia;
create temporary table tempStrataPropertiesMedia(
select * from Empirical_Prod.MediaRelationship where PropertyID in
(select StrataPropertyID from tempStrataProperties)
);
#Attaching Master to above StrataPropertiesMedia
drop temporary table if exists MediaRelationshipToBeUpdatedWithMasterProperty;
create temporary table MediaRelationshipToBeUpdatedWithMasterProperty(
select  MediaRelationshipID,PropertyID,MasterPropertyID,MediaTypeID ,StateID from tempStrataPropertiesMedia spm 
inner join tempStrataProperties sp on spm.PropertyID=sp.StrataPropertyID
);

#Moving Tenant Roster,Listing Signs from Child to Master Strata
select StateID, count(distinct mr.MediaRelationshipID) from Empirical_Prod.MediaRelationship mr 
inner join MediaRelationshipToBeUpdatedWithMasterProperty mrp on mr.MediaRelationshipID=mrp.MediaRelationshipID and mr.PropertyID=mrp.PropertyID
where  mr.MediaTypeID in (15,13) group by StateID;

-- Building Images
select mr.MediaRelationTypeID ,StateID, count(distinct mr.MediaRelationshipID)
from Empirical_Prod.MediaRelationship mr 
inner join MediaRelationshipToBeUpdatedWithMasterProperty mrp on mr.MediaRelationshipID=mrp.MediaRelationshipID and mr.PropertyID=mrp.PropertyID
where mr.MediaTypeID=3 group by mr.MediaRelationTypeID,StateID;


-- Master Strata Counts

drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where IsActive=1

);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
SELECT P.StrataPropertyID, P.MasterPropertyID, A.StateID
FROM Empirical_Prod.PropertyStrataRelationship P
INNER JOIN Empirical_Prod.Address A ON A.ParentID = P.StrataPropertyID
WHERE P.MasterPropertyID IN (SELECT MasterPropertyID FROM tempMasterProperties)
AND A.ParentTableID = 1 AND A.Sequence = 1 AND P.IsActive=1
);

# Getting all Media of Strata Properties
drop temporary table if exists tempStrataPropertiesMedia;
create temporary table tempStrataPropertiesMedia(
select * from Empirical_Prod.MediaRelationship where PropertyID in
(select MasterPropertyID from tempStrataProperties)
);
#Attaching Master to above StrataPropertiesMedia
drop temporary table if exists MediaRelationshipToBeUpdatedWithMasterProperty;
create temporary table MediaRelationshipToBeUpdatedWithMasterProperty(
select  MediaRelationshipID,PropertyID,StrataPropertyID,MediaTypeID ,StateID from tempStrataPropertiesMedia spm 
inner join tempStrataProperties sp on spm.PropertyID=sp.MasterPropertyID
);

#Moving Tenant Roster,Listing Signs from Child to Master Strata
select StateID, count(distinct mr.MediaRelationshipID) from Empirical_Prod.MediaRelationship mr 
inner join MediaRelationshipToBeUpdatedWithMasterProperty mrp on mr.MediaRelationshipID=mrp.MediaRelationshipID and mr.PropertyID=mrp.PropertyID
where  mr.MediaTypeID in (15,13) group by StateID;

-- Building Images
select mr.MediaRelationTypeID ,StateID, count(distinct mr.MediaRelationshipID)
from Empirical_Prod.MediaRelationship mr 
inner join MediaRelationshipToBeUpdatedWithMasterProperty mrp on mr.MediaRelationshipID=mrp.MediaRelationshipID and mr.PropertyID=mrp.PropertyID
where mr.MediaTypeID=3 group by mr.MediaRelationTypeID,StateID;

