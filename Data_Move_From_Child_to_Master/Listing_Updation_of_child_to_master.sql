drop temporary table if exists tempMasterProperties;
create temporary table tempMasterProperties(
select distinct MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where IsActive=1
);

drop temporary table if exists tempStrataProperties;
create temporary table tempStrataProperties(
select distinct StrataPropertyID,MasterPropertyID from Empirical_Prod.PropertyStrataRelationship where MasterPropertyID in
(select * from tempMasterProperties) and IsActive=1
);

 -- Child Properties with Current Group and Listing Groups 


select distinct TSP.MasterPropertyID, TSP.StrataPropertyID, gr.GroupID, lg.ListingID from Empirical_DataStage.
tempStrataProperties as TSP 
inner join Empirical_Prod.GroupRelationship  as gr  on gr.PropertyID = TSP.StrataPropertyID
inner join Empirical_Prod.ListingGroup as lg on lg.GroupID = gr.GroupID;


--  for One Single Property

set @SourcePIDGroupID=null,@TargetPIDGroupID=null;

#Child PID GroupRelationship
select * from Empirical_Prod.GroupRelationship  where PropertyID in (168924);
#Master PID GroupRelationship
select * from Empirical_Prod.GroupRelationship  where PropertyID in (154628);
#Child PID ListingGroup
select distinct GroupID into @SourcePIDGroupID from Empirical_Prod.GroupRelationship  where PropertyID in (168924); 
#Master PID ListingGroup
select distinct GroupID into @TargetPIDGroupID from Empirical_Prod.GroupRelationship  where PropertyID in ( 154628); 


select * from  `Empirical_Prod`.`ListingGroup` WHERE GroupID = @SourcePIDGroupID;
select * from  `Empirical_Prod`.`ListingGroup` WHERE GroupID = @TargetPIDGroupID;

select * from Empirical_Prod.Suite  where SuiteID = 56977;

 select @TargetPIDGroupID , @SourcePIDGroupID;

/**
update  `Empirical_Prod`.`ListingGroup` 
SET `GroupID` = @TargetPIDGroupID 
WHERE GroupID = @SourcePIDGroupID;
**/

-- Child Property Groups and  ListingGroup
drop temporary table if exists currentPropertyListingGroups;
create temporary table currentPropertyListingGroups(
select  TSP.MasterPropertyID, TSP.StrataPropertyID, gr.GroupID,lg.ListingGroupID,lg.ListingID,lg.SuiteID from Empirical_DataStage.tempStrataProperties as TSP 
inner join Empirical_Prod.GroupRelationship  as gr  on gr.PropertyID = TSP.StrataPropertyID
inner join Empirical_Prod.ListingGroup as lg on lg.GroupID = gr.GroupID
);

select * from currentPropertyListingGroups;


-- Master Property Groups and  ListingGroup
drop temporary table if exists PropertyListingGroups;
create temporary table PropertyListingGroups(
select cplg.*, gr.GroupID as TargetGroupID from currentPropertyListingGroups  as cplg
inner join Empirical_Prod.GroupRelationship  as gr  on gr.PropertyID = cplg.MasterPropertyID
);


select * from PropertyListingGroups;

select  lg.* from `Empirical_Prod`.`ListingGroup`  as lg
inner join PropertyListingGroups as pg on lg.ListingGroupID = pg.ListingGroupID #where pg.GroupID=185626;

-- Update Child Properties Group ID with Master Property Group ID in ListingGroup

/**
update `Empirical_Prod`.`ListingGroup`  as lg
inner join PropertyListingGroups as pg on lg.ListingGroupID =pg.ListingGroupID and  pg.ListingGroupID=185626
SET lg.`GroupID` = pg.TargetGroupID ;
**/

