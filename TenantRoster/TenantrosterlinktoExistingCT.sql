drop temporary table if exists trmCTS;
create temporary table trmCTS(
select tr.*, ct.CompanyID from Tenant_Roster_CT_match_2024_07_04 as tr
inner join Empirical_Prod.SuiteTenant  as st on tr.ConfirmedTenantID=st.ConfirmedTenantID
inner join  Empirical_Tenants.ConfirmedTenants as ct on   ct.ConfirmedTenantID=tr.ConfirmedTenantID  
and tr.PropertyID=st.PropertyID  where st.IsActive=1 and ct.IsActive=1
);

drop temporary table if exists QLDProperties;
create temporary table QLDProperties(
select ParentID from Empirical_Prod.Address where StateID=58
 and ParentTableID=1 and IsActive=1 and Sequence=1
 );
 
 
drop temporary table if exists QLDPropertiesTRCTS; #1783
create temporary table QLDPropertiesTRCTS(
select * from Empirical_DataStage.trmCTS where PropertyID in 
(select * from QLDProperties) and PropertyID is not null
);

drop temporary table if exists QLDPropertiesTRCTSWithCompany;
create temporary table QLDPropertiesTRCTSWithCompany(
select ct.*,c.<PERSON>,c.ModifiedDate as CModifiedDate,c.Mo<PERSON>y as CModifiedBy from QLDPropertiesTRCTS ct 
inner join Empirical_Prod.Company c on ct.CompanyID=c.CompanyID where c.IsHidden=0 or c.IsHidden is null
);



drop temporary table if exists comProperties;
create temporary table comProperties(
select ParentID from Empirical_Prod.Address where ZipCode in (3000, 3002, 3003, 3004, 3006, 3008, 3031, 3051, 3052, 3053, 3054)
 and ParentTableID=1 and IsActive=1 and Sequence=1
 );
 
 drop temporary table if exists COMPropertiesTRCTS; #1478
create temporary table COMPropertiesTRCTS(
select * from Empirical_DataStage.trmCTS where PropertyID in 
(select * from comProperties) and PropertyID is not null
);



drop temporary table if exists COMPropertiesTRCTSWithCompany;
create temporary table COMPropertiesTRCTSWithCompany(
select ct.*,c.IsHidden,c.ModifiedDate as CModifiedDate,c.ModifiedBy as CModifiedBy from COMPropertiesTRCTS ct 
inner join Empirical_Prod.Company c on ct.CompanyID=c.CompanyID where c.IsHidden=0 or c.IsHidden is null
);






