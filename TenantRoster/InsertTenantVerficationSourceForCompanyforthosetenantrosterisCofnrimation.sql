
-- Insert tenant verification sources for companies with confirmed tenant rosters

drop temporary table if exists tenantRosterCompanies;
create temporary table tenantRosterCompanies(
select distinct BranchID from Empirical_DataStage.Tenants_Stage where ProviderID=40 and BranchID is not null
);

-- 3985

select count( *) from Empirical_DataStage.Tenants_Stage where ProviderID=40;

-- 37499

drop temporary table if exists tenantRosterCompaniesWitHdetails;
create temporary table tenantRosterCompaniesWitHdetails(
select c.* from Empirical_Prod.Company as c  
inner join tenantRosterCompanies as rc on rc.BranchID =c.CompanyID
);

-- 3985

drop temporary table if exists tenantRosterCTS;
create temporary table tenantRosterCTS(
select ct.* from Empirical_Tenants.ConfirmedTenants as ct
inner join tenantRosterCompaniesWitHdetails as rc on rc.CompanyID =ct.CompanyID where ct.ProviderID=1 and ct.IsActive=1
);

-- 3724

drop temporary table if exists tenantRosterCTSWithTV;
create temporary table tenantRosterCTSWithTV(
select tv.* from Empirical_Prod.TenantVerification as tv
inner join tenantRosterCTS as rt on rt.CompanyID = tv.BranchID and tv.VerificationSourceID=11 and tv.IsActive=1
);

-- 87 having tenant verfication from reasearcher 

select * from tenantRosterCTSWithTV;
select * from tenantRosterCTS where CompanyID not in (select BranchID from tenantRosterCTSWithTV);
-- 3107 , not having tv

select * from Empirical_Prod.SuiteTenant where ConfirmedTenantID in ( 
select ConfirmedTenantID from tenantRosterCTS where CompanyID not in (select BranchID from tenantRosterCTSWithTV));

drop temporary table if exists tenantRosterTVS;
create temporary table tenantRosterTVS(
    select null,
    CompanyID,
    11 as VerificationSourceID,
    "Tenant Roster Verfied" as VerificationNote,
    1 as IsActive, 
    22 as CreatedBy,
    now() as CreatedDate,
    22 as ModifiedBy,
    now() as ModifiedDate
    from tenantRosterCTS  where CompanyID not in (select BranchID from tenantRosterCTSWithTV)
   # where rtr.MediaID=976679
);

select * from  tenantRosterTVS where CompanyID =1676509;

#select * fr

/**
INSERT INTO Empirical_Prod.TenantVerification ( select * from tenantRosterTVS where CompanyID =1676509 ); #where MediaID=976679
*/



