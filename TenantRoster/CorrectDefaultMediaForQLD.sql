drop temporary table if exists tempMediaToCorrectDefault;
create temporary table tempMediaToCorrectDefault(
select * from Empirical_Prod.Media where MediaID in (select MediaID from Empirical_DataStage.CorrectDefaultMediaQLD));

drop temporary table if exists tempMediaPropertyToCorrectDefault;
create temporary table tempMediaPropertyToCorrectDefault(
select mt.PropertyID as mrProperty,mt.MediaID,ms.PropertyID as cdmProperty from Empirical_Prod.MediaRelationship mt 
    inner join Empirical_DataStage.CorrectDefaultMediaQLD ms on ms.MediaID = mt.MediaID where mt.PropertyID= ms.PropertyID);
    
select * from tempMediaPropertyToCorrectDefault;

-- setting existing media having isDefault field 1 to 0, to remove as default
-- changelog
drop temporary table if exists tempMediaToCorrectDefaultChangeLogs;
create temporary table tempMediaToCorrectDefaultChangeLogs(
    select null,
    mr.MediaID,
    517 as ChangeLogFieldID,
    mr.<PERSON> as OldValue,
    0 as NewValue, 
    22 as ChangedBy,
    now() as ChangedDate,
    1 as ApplicationID,
    2 as ActionID
    from Empirical_DataStage.CorrectDefaultMediaQLD as rtr
    inner join Empirical_Prod.MediaRelationship as mr on
    rtr.PropertyID=mr.PropertyID and mr.IsDefault=1 and mr.IsActive=1
    #where mr.PropertyID =415666
);
select * from  tempMediaToCorrectDefaultChangeLogs;

/**
INSERT INTO Empirical_Prod.ChangeLogMedia ( select * from tempMediaToCorrectDefaultChangeLogs ); #where MediaID=972812
*/



-- update query
update Empirical_Prod.MediaRelationship 
	set IsDefault = 0 -- ,ModifiedDate=now(),ModifiedBy=22
    where PropertyID in (select PropertyID from Empirical_DataStage.CorrectDefaultMediaQLD) 
    and IsActive=1
    #and PropertyID =415666
    ;

SELECT 
    mr.PropertyID,
    mr.MediaID,
    mr.IsDefault,
    rtr.PropertyID
FROM
    Empirical_Prod.MediaRelationship AS mr
        INNER JOIN
    Empirical_DataStage.CorrectDefaultMediaQLD AS rtr ON mr.MediaID = rtr.MediaID
WHERE
    rtr.PropertyID = mr.PropertyID -- and mr.PropertyID =415666
;

-- setting media isDefault field 1 give in the table CorrectDefaultMediaQLD
-- changelog
drop temporary table if exists tempMediaToCorrectDefaultChangeLogs;
create temporary table tempMediaToCorrectDefaultChangeLogs(
    select null,
    mr.MediaID,
    517 as ChangeLogFieldID,
    mr.IsDefault as OldValue,
    1 as NewValue, 
    22 as ChangedBy,
    now() as ChangedDate,
    1 as ApplicationID,
    2 as ActionID
    from Empirical_DataStage.CorrectDefaultMediaQLD as rtr
    inner join Empirical_Prod.MediaRelationship as mr on
    rtr.PropertyID=mr.PropertyID and
    rtr.MediaID=mr.MediaID  and mr.IsActive=1
    #and mr.PropertyID =415666
);
/**
INSERT INTO Empirical_Prod.ChangeLogMedia ( select * from tempMediaToCorrectDefaultChangeLogs ); #where MediaID=972812
*/

-- update
UPDATE Empirical_Prod.MediaRelationship mt
INNER JOIN Empirical_DataStage.CorrectDefaultMediaQLD ms ON mt.MediaID = ms.MediaID
SET mt.IsDefault = 1 -- ModifiedDate=now(),ModifiedBy=22
WHERE mt.PropertyID IN (
    SELECT PropertyID FROM Empirical_DataStage.CorrectDefaultMediaQLD
) and  mt.IsActive=1
#and mt.PropertyID =415666
;
 
SELECT 
    mr.PropertyID,
    mr.MediaID,
    mr.IsDefault,
    rtr.PropertyID
FROM
    Empirical_Prod.MediaRelationship AS mr
        INNER JOIN
    Empirical_DataStage.CorrectDefaultMediaQLD AS rtr ON mr.MediaID = rtr.MediaID
WHERE
    rtr.PropertyID = mr.PropertyID 
    #a nd mr.PropertyID =415666
    ;
;
