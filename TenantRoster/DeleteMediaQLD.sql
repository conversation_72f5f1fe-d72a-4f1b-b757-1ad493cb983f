SELECT  * FROM Empirical_DataStage.DeleteMediaQLD;

drop temporary table if exists tempMediaDefaultOne;
create temporary table tempMediaDefaultOne
SELECT 
    mr.PropertyID as mrPropertyID,
    mr.MediaID,
    mr.Is<PERSON>efault,
    rtr.PropertyID as rtrPropertyID
FROM
    Empirical_Prod.MediaRelationship AS mr
        INNER JOIN
    Empirical_DataStage.DeleteMediaQLD AS rtr ON mr.MediaID = rtr.MediaID
WHERE
    rtr.PropertyID = mr.PropertyID and IsDefault=1;
    
select * from tempMediaDefaultOne;
    
drop temporary table if exists tempMediaDefaultZero;
create temporary table tempMediaDefaultZero
SELECT 
    mr.PropertyID as mrPropertyID,
    mr.MediaID,
    mr.Is<PERSON>efault,
    mr.MediaRelationshipID,
    rtr.PropertyID as rtrPropertyID
FROM
    Empirical_Prod.MediaRelationship AS mr
        INNER JOIN
    Empirical_DataStage.DeleteMediaQLD AS rtr ON mr.MediaID = rtr.MediaID
WHERE
    rtr.PropertyID = mr.PropertyID and IsDefault=0;

select * from tempMediaDefaultZero;

  
/*

delete from Empirical_Prod.MediaRelationship where MediaRelationshipID in (select MediaRelationshipID from tempMediaDefaultZero);


*/


-- property having 1 media and isDefault is 1

drop temporary table if exists tempMediaHavingOne;
create temporary table tempMediaHavingOne
select * from  Empirical_Prod.MediaRelationship where IsDefault=1 and PropertyID in ( select mrPropertyID as PropertyID from tempMediaDefaultOne ) group by PropertyID having count(*) = 1;

select * from tempMediaHavingOne;

/* 
delete from Empirical_Prod.MediaRelationship where MediaRelationshipID in (select MediaRelationshipID from tempMediaHavingOne);
*/
 
 
  -- property having more than 1 media and isDefault is 1
drop temporary table if exists tempMediaHavingMoreDelete;
create temporary table tempMediaHavingMoreDelete
select * from  Empirical_Prod.MediaRelationship where IsDefault=1 and PropertyID in ( select mrPropertyID as PropertyID from tempMediaDefaultOne ) group by PropertyID having count(*) >1;

select * from tempMediaHavingMoreDelete;


delete from Empirical_Prod.MediaRelationship where MediaRelationshipID in (select MediaRelationshipID from tempMediaHavingMoreDelete);
