SELECT 
    *
FROM
    Empirical_DataStage.CorrectMediaTypeTaggingQLD;
 

SELECT 
    *
FROM
    Empirical_Prod.Media
WHERE
    MediaID IN (SELECT 
            MediaID
        FROM
            Empirical_DataStage.CorrectMediaTypeTaggingQLD);

SELECT 
    mr.PropertyID,
    mr.MediaID,
    mt.MediaTypeID,
    mt.MediaTypeName,
    rtr.PropertyID,
    kt.MediaTypeName AS currentName,
    rtr.`Correct Tagging`
FROM
    Empirical_Prod.MediaRelationship AS mr
        INNER JOIN
    Empirical_DataStage.CorrectMediaTypeTaggingQLD AS rtr ON mr.MediaID = rtr.MediaID
        LEFT JOIN
    Empirical_Prod.MediaType AS mt ON mt.MediaTypeName = rtr.`Correct Tagging`
        LEFT JOIN
    Empirical_Prod.MediaType AS kt ON kt.MediaTypeID = mr.MediaTypeID
WHERE
    rtr.PropertyID = mr.PropertyID
        AND rtr.`Correct Tagging` != kt.MediaTypeName
;


    
    
    
drop temporary table if exists tenantRosterMediaChangeLogs;
create temporary table tenantRosterMediaChangeLogs(
    select null,
    mr.MediaID,
    514 as ChangeLogFieldID,
    mt.MediaTypeName as OldValue,
    rtr.`Correct Tagging` as NewValue, 
    22 as ChangedBy,
    now() as ChangedDate,
    1 as ApplicationID,
    2 as ActionID
    from Empirical_DataStage.CorrectMediaTypeTaggingQLD as rtr
    inner join Empirical_Prod.MediaRelationship as mr on
    rtr.PropertyID=mr.PropertyID and
    rtr.MediaID=mr.MediaID
          LEFT JOIN Empirical_Prod.MediaType AS mt ON mt.MediaTypeID = mr.MediaTypeID
    #where rtr.MediaID=937710
    where rtr.`Correct Tagging` != mt.MediaTypeName
);

SELECT 
    *
FROM
    Empirical_Prod.ChangeLogFields;
SELECT 
    *
FROM
    tenantRosterMediaChangeLogs;
/**
INSERT INTO Empirical_Prod.ChangeLogMedia ( select * from tenantRosterMediaChangeLogs ); #where MediaID=972812
*/



SELECT 
    mr.PropertyID,
    mr.MediaID,
    mt.MediaTypeID,
    mt.MediaTypeName,
    rtr.PropertyID,
    rtr.`Correct Tagging`
FROM
    Empirical_Prod.MediaRelationship AS mr
        INNER JOIN
    Empirical_DataStage.CorrectMediaTypeTaggingQLD AS rtr ON mr.MediaID = rtr.MediaID
        LEFT JOIN
    Empirical_Prod.MediaType AS mt ON mt.MediaTypeName = rtr.`Correct Tagging`
WHERE
    rtr.PropertyID = mr.PropertyID 
;
      

  
  
  
UPDATE Empirical_Prod.MediaRelationship AS mr
        INNER JOIN
    Empirical_DataStage.CorrectMediaTypeTaggingQLD AS rtr ON mr.MediaID = rtr.MediaID
        LEFT JOIN
    Empirical_Prod.MediaType AS mt ON mt.MediaTypeName = rtr.`Correct Tagging` 
SET 
    mr.MediaTypeID = mt.MediaTypeID
WHERE
    rtr.PropertyID = mr.PropertyID 
;


select * from Empirical_DataStage.CorrectMediaTypeTaggingQLD;

drop temporary table if exists MediaTaggingRemoveDefault;
create temporary table MediaTaggingRemoveDefault
select mr.*,mt.MediaTypeName from Empirical_Prod.MediaRelationship mr
inner join Empirical_Prod.MediaType mt on mt.MediaTypeID=mr.MediaTypeID 
 where MediaID in (SELECT MediaID FROM Empirical_DataStage.CorrectMediaTypeTaggingQLD) and mr.IsDefault=1 and mr.MediaTypeID in (15,13);
 
 select * from MediaTaggingRemoveDefault;
 
drop temporary table if exists tempMediaToCorrectDefaultChangeLogs;
create temporary table tempMediaToCorrectDefaultChangeLogs(
    select null,
    mr.MediaID,
    517 as ChangeLogFieldID,
    mr.IsDefault as OldValue,
    0 as NewValue, 
    22 as ChangedBy,
    now() as ChangedDate,
    1 as ApplicationID,
    2 as ActionID
    from Empirical_DataStage.MediaTaggingRemoveDefault as rtr
    inner join Empirical_Prod.MediaRelationship as mr on
    rtr.MediaID=mr.MediaID 
   #where mr.PropertyID='659157'
);

select * from tempMediaToCorrectDefaultChangeLogs;

/**
INSERT INTO Empirical_Prod.ChangeLogMedia ( select * from tempMediaToCorrectDefaultChangeLogs ); #where MediaID=972812
*/

update Empirical_Prod.MediaRelationship 
	set IsDefault = 0 -- ,ModifiedDate=now(),ModifiedBy=22
    where MediaID in (select MediaID from Empirical_DataStage.MediaTaggingRemoveDefault )
    and IsActive=1;-- where PropertyID=406918) ;
    
select * from MediaTaggingRemoveDefault;    

drop temporary table if exists MediaTaggingShift;
create temporary table MediaTaggingShift
select m.*,mr.PropertyID,mr.IsDefault,mr.MediaTypeID from Empirical_Prod.Media as m
inner join Empirical_Prod.MediaRelationship mr on mr.MediaID=m.MediaID
where PropertyID in(select PropertyID from MediaTaggingRemoveDefault) and IsDefault<>1 and MediaTypeID not in (15,13) group by PropertyID
 ; 

select * from MediaTaggingShift;

drop temporary table if exists tempMediaToCorrectDefaultChangeLogs;
create temporary table tempMediaToCorrectDefaultChangeLogs(
    select null,
    mr.MediaID,
    517 as ChangeLogFieldID,
    mr.IsDefault as OldValue,
    1 as NewValue, 
    22 as ChangedBy,
    now() as ChangedDate,
    1 as ApplicationID,
    2 as ActionID
    from MediaTaggingShift as rtr
    inner join Empirical_Prod.MediaRelationship as mr on
    rtr.MediaID=mr.MediaID 
  --  where mr.PropertyID=406918 -- w
);
select * from tempMediaToCorrectDefaultChangeLogs;
/**
INSERT INTO Empirical_Prod.ChangeLogMedia ( select * from tempMediaToCorrectDefaultChangeLogs ); #where MediaID=972812
*/

UPDATE Empirical_Prod.MediaRelationship mt
INNER JOIN Empirical_DataStage.MediaTaggingShift ms ON mt.MediaID = ms.MediaID and mt.IsActive=1
SET mt.IsDefault = 1 -- ,ModifiedDate=now(),ModifiedBy=22
  --  where  mt.PropertyID=406918;
