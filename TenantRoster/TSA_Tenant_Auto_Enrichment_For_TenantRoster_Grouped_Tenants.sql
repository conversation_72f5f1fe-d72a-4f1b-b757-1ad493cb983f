CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `TSA_Tenant_Auto_Enrichment_For_TenantRoster_Grouped_Tenants`()
BEGIN

    DECLARE foreign_key_error BOOLEAN DEFAULT FALSE;
    DECLARE error_message TEXT DEFAULT '';
    DECLARE error_code CHAR(5) DEFAULT '';
    
-- Handler for foreign key errors
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION 
    BEGIN
        GET DIAGNOSTICS CONDITION 1
        error_message = MESSAGE_TEXT,
        error_code = RETURNED_SQLSTATE;
        
        IF error_code = '23000' THEN
            SET foreign_key_error = TRUE;
        END IF;
    END;
    
    drop temporary table if exists tempCompanyCT;
    create temporary table tempCompanyCT(
        select * from Empirical_DataStage.Tenant_Roster_CT_match_2024_07_04
            where (IsProcessed=0 or IsProcessed is null) #and ConfirmedTenantID=1137049
    );
    
    select min(ConfirmedTenantID) into @minConfirmedtenantID from tempCompanyCT;
    
    while @minConfirmedTenantID is not null do
        call Empirical_Prod.TSA_Tenant_Auto_Enrichment(@minConfirmedTenantID,10,22);
        -- select @minConfirmedTenantID;
        update Empirical_DataStage.Tenant_Roster_CT_match_2024_07_04 set IsProcessed=1 where ConfirmedTenantID=@minConfirmedtenantID;
        select min(ConfirmedTenantID) into @minConfirmedtenantID from tempCompanyCT where ConfirmedTenantID>@minConfirmedtenantID;
        
        IF foreign_key_error THEN
        -- Handle the foreign key constraint violation
        update Empirical_DataStage.Tenant_Roster_CT_match_2024_07_04 set IsProcessed=2 where ConfirmedTenantID=@minConfirmedtenantID;
             SELECT CONCAT('Error: ', error_message) AS Error_Detail;
        end if;
    end while;

END