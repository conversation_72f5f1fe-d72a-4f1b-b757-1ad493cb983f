drop temporary table if exists tempTenantStageALA;
create temporary table tempTenantStageALA(
 select ts.VendorID,ts.TenantName,ts.ConfirmedTenantID,ts.Tenant_Stage_Id,ts.BranchID,ts.PropertyID
 from Empirical_DataFixes.RemovePromotedTenantRosterTenants_2024_10_10 rct inner join 
 Empirical_DataStage.Tenants_Stage ts on rct.VendorID=ts.VendorID 
 where ts.VendorID="d7e203557f0142b89074854103193f90"
);

select * from tempTenantStageALA limit 1;
drop temporary table if exists tempCompanyRelationship;
create temporary table tempCompanyRelationship(
 select * from Empirical_Prod.CompanyRelationship where ChildCompanyID in (select BranchID from tempTenantStageALA)
);


drop temporary table if exists tempConfirmedTenants;
create temporary table tempConfirmedTenants(
 select * from Empirical_Tenants.ConfirmedTenants where CompanyID in (select ChildCompanyID from tempCompanyRelationship)
);

select * from tempConfirmedTenants;

select TV.* from tempTenantStageALA N 
inner join  Empirical_Prod.TenantVerification TV on N.BranchID = TV.BranchID
where TV.VerificationNote='Tenant Roster Verfied' and VerificationSourceID=11; -- 759 records;

/*
DELETE TV
FROM Empirical_Prod.TenantVerification TV
INNER JOIN tempTenantStageALA N 
    ON TV.BranchID = N.BranchID
where TV.VerificationNote='Tenant Roster Verfied' and VerificationSourceID=11;
*/
select * from Empirical_Prod.NationalIdentifiers where CompanyID in (select ChildCompanyID from tempCompanyRelationship)
or ConfirmedTenantID in (select ConfirmedTenantID from tempConfirmedTenants) or Tenant_Stage_Id in (select Tenant_Stage_Id from tempTenantStageALA); -- counts = 0
/*
delete from Empirical_Prod.NationalIdentifiers where CompanyID in (select ChildCompanyID from tempCompanyRelationship)
or ConfirmedTenantID in (select ConfirmedTenantID from tempConfirmedTenants) or Tenant_Stage_Id in (select Tenant_Stage_Id from tempTenantStageALA);
*/


select * from Empirical_Prod.CompanyContact where CompanyID in (select ChildCompanyID from tempCompanyRelationship); -- counts =0
/*
-- delete from Empirical_Prod.CompanyContact where CompanyID in (select ChildCompanyID from tempCompanyRelationship);
*/

select * from  Empirical_Prod.ChangeLogCompany where CompanyID in (select ChildCompanyID from tempCompanyRelationship); -- counts = 8
/*
delete from Empirical_Prod.ChangeLogCompany where CompanyID in (select ChildCompanyID from tempCompanyRelationship);
*/
select * from Empirical_Prod.GroupEntity where CompanyID in (select ChildCompanyID from tempCompanyRelationship); -- counts =0
/*
delete from Empirical_Prod.GroupEntity where CompanyID in (select ChildCompanyID from tempCompanyRelationship);
*/
select * from Empirical_Prod.CompanySupport where CompanyID in (select ChildCompanyID from tempCompanyRelationship); -- counts = 0
/*
delete from Empirical_Prod.CompanySupport where CompanyID in (select ChildCompanyID from tempCompanyRelationship); 
*/
select * from Empirical_Prod.CompanyRelationship where ChildCompanyID in (select ChildCompanyID from tempCompanyRelationship); -- counts = 758
/*
delete from Empirical_Prod.CompanyRelationship where ChildCompanyID in (select ChildCompanyID from tempCompanyRelationship);
*/

select * from Empirical_Tenants.ConfirmedTenantsFieldAudit where ConfirmedTenantid in(
 select ConfirmedTenantid from Empirical_Tenants.ConfirmedTenants where CompanyID in 
 (select ChildCompanyID from tempCompanyRelationship)); -- counts = 4548
 
/*
delete from Empirical_Tenants.ConfirmedTenantsFieldAudit where ConfirmedTenantid in(
 select ConfirmedTenantid from Empirical_Tenants.ConfirmedTenants where CompanyID in 
 (select ChildCompanyID from tempCompanyRelationship));
*/

select * from Empirical_DataStage.Tenants_Stage where BranchID in (select BranchID from tempTenantStageALA); -- 759

/*
delete from Empirical_DataStage.Tenants_Stage
where BranchID in (select BranchID from tempTenantStageALA); 
*/

select * from Empirical_Prod.SuiteTenant where PropertyID in (select PropertyID from tempTenantStageALA) 
and ConfirmedTenantID in (select ConfirmedTenantID from tempConfirmedTenants);
/*
delete from Empirical_Prod.SuiteTenant where PropertyID in (select PropertyID from tempTenantStageALA) 
and ConfirmedTenantID in (select ConfirmedTenantID from tempConfirmedTenants);
*/

select * from Empirical_Tenants.ConfirmedTenants where CompanyID in (select ChildCompanyID from tempCompanyRelationship); -- 1516
/*
delete from Empirical_Tenants.ConfirmedTenants where CompanyID in (select ChildCompanyID from tempCompanyRelationship);
*/





select * from Empirical_Prod.Company WHERE CompanyID in (select ChildCompanyID from tempCompanyRelationship); -- counts = 758

/*
delete from Empirical_Prod.Company WHERE CompanyID in (select ChildCompanyID from tempCompanyRelationship);
*/


-- Matched Tenant Roster Records Deletion:

select * from Empirical_DataFixes.RemoveMatchedTenantRosterTenants_2024_10_10;

drop temporary table if Exists NeedToRemoveTenants;
create temporary table NeedToRemoveTenants
(
select T.*  from Empirical_DataStage.Tenants_Stage T inner join
Empirical_DataFixes.RemoveMatchedTenantRosterTenants_2024_10_10 RM on T.Tenant_Stage_Id = RM.Tenant_Stage_Id
);

select * from NeedToRemoveTenants;
-- select * from Empirical_DataStage.Tenants_Stage where BranchID = 368017;
-- select * from Empirical_Tenants.ConfirmedTenants where CompanyID = 368017;

drop temporary table if Exists NeedToRemoveVerification;
create temporary table NeedToRemoveVerification(
select TV.* from NeedToRemoveTenants N 
inner join  Empirical_Prod.TenantVerification TV on N.BranchID = TV.BranchID
where TV.VerificationNote='Tenant Roster' and VerificationSourceID=11
);

-- DELETE TV
-- FROM Empirical_Prod.TenantVerification TV
-- INNER JOIN NeedToRemoveVerification N 
--     ON TV.BranchID = N.BranchID
-- where TV.VerificationNote='Tenant Roster' and VerificationSourceID=11;

select * from NeedToRemoveTenants N
inner join Empirical_DataStage.Tenants_Stage T on T.Tenant_Stage_Id = N.Tenant_Stage_Id;

delete from Empirical_DataStage.Tenants_Stage
where Tenant_Stage_Id in (select Tenant_Stage_Id from NeedToRemoveTenants); 



