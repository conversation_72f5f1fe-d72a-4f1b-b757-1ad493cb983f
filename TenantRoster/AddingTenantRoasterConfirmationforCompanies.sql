ALTER TABLE `Empirical_DataStage`.`Tenant_Roster_CT_match_2024_07_04` 
ADD COLUMN `CompanyID` BIGINT NULL DEFAULT NULL AFTER `fuzzy_score`;

select tr.*,ct.TenantName from Empirical_DataStage.Tenant_Roster_CT_match_2024_07_04 tr inner join Empirical_Tenants.ConfirmedTenants ct
on tr.ConfirmedTenantID=ct.ConfirmedTenantID and tr.TenantName_CT = ct.TenantName;

update Empirical_DataStage.Tenant_Roster_CT_match_2024_07_04 tr inner join Empirical_Tenants.ConfirmedTenants ct
on tr.ConfirmedTenantID=ct.ConfirmedTenantID and tr.TenantName_CT = ct.TenantName set tr.CompanyID=ct.CompanyID;

select tr.*,ct.* from Empirical_DataStage.Tenants_Stage ct inner join Empirical_DataStage.Tenant_Roster_CT_match_2024_07_04 tr
on ct.VendorID=tr.VendorID and ct.Tenant_Stage_Id=tr.Tenant_Stage_Id and ct.PropertyID = tr.PropertyID and ct.TenantName = tr.TenantName_Roster;


update Empirical_DataStage.Tenants_Stage ct inner join Empirical_DataStage.Tenant_Roster_CT_match_2024_07_04 tr
on ct.VendorID=tr.VendorID and ct.Tenant_Stage_Id=tr.Tenant_Stage_Id and ct.PropertyID = tr.PropertyID and ct.TenantName = tr.TenantName_Roster
set ct.BranchID=tr.CompanyID,ModifiedBy=22,ModifiedDate=now();

drop temporary table if exists tempTenantVerificationForTenantRoster;
create temporary table tempTenantVerificationForTenantRoster(
select null as TenantVerificationID,CompanyID as BranchID,11 as VerificationSourceID,'Tenant Roster' as VerificationNote,1 as IsActive,
22 as CreatedBy,now() as CreatedDate,22 as ModifiedBy,now() as ModifiedDate 
from Empirical_DataStage.Tenant_Roster_CT_match_2024_07_04
);

select * from tempTenantVerificationForTenantRoster;

insert into Empirical_Prod.TenantVerification
select * from tempTenantVerificationForTenantRoster;

select * from Empirical_Prod.Company 
WHERE
    CompanyID IN (SELECT CompanyID FROM Empirical_DataStage.Tenant_Roster_CT_match_2024_07_04)
    AND IsHidden = 1;

UPDATE Empirical_Prod.Company 
SET 
    ModifiedBy = 22,
    ModifiedDate = CURRENT_TIMESTAMP(),
    IsHidden = NULL,
    HidedBy = NULL,
    HidedDate = NULL,
    HideReasonID = NULL,
    SubHideReasonID = NULL,
    HideReasonComments = 'Unhidding the companies having TenantRoster as source confirmation.'
WHERE
    CompanyID IN (SELECT CompanyID FROM Empirical_DataStage.Tenant_Roster_CT_match_2024_07_04)
    AND IsHidden = 1;


ALTER TABLE `Empirical_DataStage`.`Tenant_Roster_CT_match_2024_07_04` 
ADD COLUMN `IsProcessed` TINYINT(1) NULL DEFAULT 0 AFTER `CompanyID`;

Call Empirical_DataStage.TSA_Tenant_Auto_Enrichment_For_TenantRoster_Grouped_Tenants();

SELECT count(*),IsProcessed FROM Empirical_DataStage.Tenant_Roster_CT_match_2024_07_04 group by IsProcessed;