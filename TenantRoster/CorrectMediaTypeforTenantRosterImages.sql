select * from   Empirical_DataStage.media_with_path ;
select * from    Empirical_Prod.Media where MediaID in ( select MediaID from Empirical_DataStage.media_with_path) ;

SELECT 
mr.PropertyID,
    mr.MediaID,
    mt.MediaTypeID,
    mt.MediaTypeName,
    mst.MediaSubTypeName
FROM
    Empirical_Prod.MediaRelationship AS mr
        LEFT JOIN
    Empirical_Prod.MediaType AS mt ON mt.MediaTypeID = mr.MediaTypeID
        LEFT JOIN
    Empirical_Prod.MediaSubType AS mst ON mr.MediaSubTypeID = mst.MediaSubTypeID
        INNER JOIN
    Empirical_DataStage.media_with_path AS rtr ON mr.MediaID = rtr.MediaID
      #  AND rtr.PropertyID = mr.PropertyID
    #  AND mr.MediaID IN (972812)
      ;


    
    
    
drop temporary table if exists tenantRosterMediaChangeLogs;
create temporary table tenantRosterMediaChangeLogs(
    select null,
    mr.MediaID,
    514 as ChangeLogFieldID,
    mt.MediaTypeName as OldValue,
    "Tenant Roster" as NewValue, 
    22 as ChangedBy,
    now() as ChangedDate,
    1 as ApplicationID,
    2 as ActionID
    from Empirical_DataStage.media_with_path as rtr
    inner join Empirical_Prod.MediaRelationship as mr on
    #rtr.PropertyID=mr.PropertyID
   
   # and
    rtr.MediaID=mr.MediaID
      LEFT JOIN Empirical_Prod.MediaType AS mt ON mt.MediaTypeID = mr.MediaTypeID
   # where rtr.MediaID=972812
);

select * from Empirical_Prod.ChangeLogFields;
select * from tenantRosterMediaChangeLogs;
/**
INSERT INTO Empirical_Prod.ChangeLogMedia ( select * from tenantRosterMediaChangeLogs ); #where MediaID=972812
*/



select * from Empirical_Prod.MediaRelationship AS mr
        LEFT JOIN
    Empirical_Prod.MediaType AS mt ON mt.MediaTypeID = mr.MediaTypeID
        LEFT JOIN
    Empirical_Prod.MediaSubType AS mst ON mr.MediaSubTypeID = mst.MediaSubTypeID
        INNER JOIN
    Empirical_DataStage.media_with_path AS rtr ON mr.MediaID = rtr.MediaID
       # AND rtr.PropertyID = mr.PropertyID 
#SET 
   # mr.MediaTypeID = 15
#WHERE
#   mr.MediaID IN (972812)
   ;

UPDATE Empirical_Prod.MediaRelationship AS mr
        LEFT JOIN
    Empirical_Prod.MediaType AS mt ON mt.MediaTypeID = mr.MediaTypeID
        LEFT JOIN
    Empirical_Prod.MediaSubType AS mst ON mr.MediaSubTypeID = mst.MediaSubTypeID
        INNER JOIN
    Empirical_DataStage.media_with_path AS rtr ON mr.MediaID = rtr.MediaID
       # AND rtr.PropertyID = mr.PropertyID 
SET 
    mr.MediaTypeID = 15
#WHERE
  # mr.MediaID IN (972812);