# Tenant_Ingestion


### 1 Illiion Raw data to Illion Stage

call Empirical_DataStage.CRE_Tenant_Import_ILLION_From_Raw_Data_DEC2023(9003);
call Empirical_DataStage.CRE_Tenant_Import_ILLION_From_Raw_Data_Zessta(9003);
select * from Empirical_DataStage.Tenants_Stage_ILLION_NOV_2023; 
select * from Empirical_DataStage.Tenants_Stage_ILLION_Zessta where BatchID =9003; 
## Tenants_Stage_ILLION_NOV_2023 to Tenants_Stage_ILLION_Zessta

### 2 Illion Stage to Tenant Stage
call Empirical_DataStage.CRE_Tenant_Import_ILLION_Zessta(9003);
select * from Empirical_DataStage.Tenants_Stage order by Tenant_Stage_Id desc; 
select * from Empirical_DataStage.Tenants_Stage where BatchID =9003 order by Tenant_Stage_Id; 
##Tenants_Stage_ILLION_Zessta to Empirical_DataStage.Tenants_Stage

### 3 Tenant Stage to application tables
call Empirical_DataStage.CRE_Tenant_Import_Zessta(9003); -- P_BatchID

select * from Empirical_DataStage.Tenants_Stage order by Tenant_Stage_Id desc; 
select * from Empirical_Prod.Company order by CompanyID desc; --  Yes, Parent Company yes
select * from Empirical_Prod.CompanyRelationship order by CompanyRelationshipID desc;
select * from Empirical_Prod.Location order by LocationID desc;
select * from Empirical_Prod.Address order by AddressID desc ;
select * from Empirical_Prod.CompanyContact order by CompanyContactID desc;
select * from Empirical_Prod.CompanySupport order by CompanySupportID desc ;
select * from Empirical_Tenants.ConfirmedTenants order by ConfirmedTenantID desc ;
select * from Empirical_Prod.NationalIdentifiers order by ID desc;
SELECT * FROM Empirical_Tenants.ConfirmedTenantsFieldAudit order by ConfirmedTenantFieldAuditID desc;



call Empirical_DataStage.CRE_Tenant_Do_Property_Match_Zessta( 9003, 6, '58','QLD');

select 
	TS.Tenant_Stage_ID, Address1 as AddressText, C.CityID, C.CityName, C.StateID
from
	Empirical_DataStage.Tenants_Stage TS
    JOIN Empirical_Prod.City C on C.CityName = TS.City and C.StateID=58
WHERE TS.BatchID=9003 AND  TS.IsProcessed=1 and TS.PropertyID is null

 #### 3.1 Insert the Tenants with Multiple Providers

### 4 Tenants and Property Match ----///



