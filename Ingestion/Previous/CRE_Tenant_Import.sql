CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Tenant_Import`()
BEGIN

DECLARE L_NewBranchID INT;

CREATE TEMPORARY TABLE tempTenants AS
(

	SELECT 
		Tenant_Stage_Id,
		DUNS,
		Coalesce(CASE WHEN trim(TRADESTYLE)='' THEN NULL ELSE TRADESTYLE END,`BUSINESS NAME`) as CompanyName,
		`ADDRESS 1 (PHYSICAL)` as Address1,
		`ADDRESS 2 (PHYSICAL)` as Address2,
		`CITY NAME` AS City,
		C.CityID,
		`STATE PROVINCE ABBRV`,
		S.StateID,
		14 as Country<PERSON>,
		`POSTAL CODE` as <PERSON>ipcode,
		concat(trim(LEADING '0' from `COUNTRY ACCESS CODE`),TELEPHONE) as OfficePhone,
		CASE WHEN `FAX#` ='' THEN '' ELSE concat(trim(LEADING '0' from `COUNTRY ACCESS CODE`),`FAX#`) END as Fax,
		`US 1987 SIC #1` as <PERSON><PERSON><PERSON><PERSON>,
		348 as <PERSON><PERSON>,
		1 as BranchStatus,
		0 as <PERSON><PERSON><PERSON><PERSON>,
        Latitude,
        Longitude
        
	FROM
		Tenants_Stage ST
			JOIN
		Empirical_Prod.City C ON C.CityName = TRIM(LOWER(`CITY NAME`))
			AND C.StateID = 57
			JOIN
		Empirical_Prod.State S ON S.StateAbbr= TRIM(LOWER(`STATE PROVINCE ABBRV`))
			AND S.CountryID = 14 
	WHERE
		ST.IsProcessed=0 
	);

	SELECT 
		MIN(Tenant_Stage_Id)
	INTO @Tenant_Stage_Id FROM
		tempTenants
	ORDER BY Tenant_Stage_Id;

	While(@Tenant_Stage_Id is not null )  DO	
		 Select 
         CompanyName,
         DUNS,
         Address1,
         Address2,
         CityID ,
         StateID,
         CountryID,
         Zipcode,OfficePhone,Fax,SICCode,MetroID,BranchStatus,IsMember,Latitude,Longitude,22 
         INTO @CompanyName,@DUNS,@Address1,@Address2,@CityID,@StateID,@CountryID,@Zipcode,@OfficePhone,@Fax,@SICCode,@MetroID,@BranchStatus,@IsMember,@Latitude,@Longitude,@EntityID 
         From tempTenants where Tenant_Stage_Id=@Tenant_Stage_Id;  
         
         -- Create Company
         INSERT INTO Empirical_Prod.Company
				(
					CompanyName,					
					CreatedBy,
					CreatedDate,
					ModifiedBy,
					ModifiedDate
				)
				Values
				(
					@CompanyName,
					@EntityID,
					current_timestamp(),
					@EntityID,
					current_timestamp()
				);
         
			select  LAST_INSERT_ID() INTO @ParentCompanyID;         
			
            CALL Empirical_Prod.CRE_Import_Branch_Save(
				-1, -- P_CompanyID INT, 
				@ParentCompanyID, -- P_ParentCompanyID INT,
				NULL, -- P_ConfirmedTenantID int,
				null, -- P_CompanyName Varchar(200),
				NULL, -- P_Website  varchar(225),	
				NULL, -- P_CompanyTypeID INT,
				NULL, -- P_IsNationalBrokerageCompany tinyint(1),
				NULL, -- p_IsPublicCompany tinyint(1),
				NULL, -- P_TickerSymbol varchar(20),
				@Address1, -- P_Address1 VARCHAR(200),
				@Address2, -- P_Address2 VARCHAR(50),
				@CityID, -- P_CityID INT,
				@StateID, -- P_StateID INT,
				@Zipcode, -- P_ZipCode Varchar(10),
				NULL, -- P_CountyID INT,
				@CountryID, -- P_CountryID INT,
				@MetroID, -- P_MetroID INT,
				@Latitude, -- P_Latitude DECIMAL(21,14),
				@Longitude, -- P_Longitude DECIMAL(21,14),
				@OfficePhone,-- P_OfficePhone varchar(20),
				@Fax, -- P_Fax varchar(20),
				NULL, -- P_Email varchar(255),
				1,-- P_IsActive tinyint(1),
				NULL, -- P_IsCountyHQ tinyint(1),
				NULL, -- P_IsGlobalHQ tinyint(1),
				NULL, -- P_Researcher int ,
				NULL, -- P_SalesRep int,
				NULL, -- P_SupportAgent int,
				NULL, -- P_NAICSCode Varchar(20),
				@SICCode, -- P_ISIC varchar(20),
				null, -- P_RatingTierID int,
				0, -- P_IsMember tinyint(1),
                @DUNS,-- P_ExtVendorID
                2, -- P_ProviderID,                
				@EntityID,-- P_EntityID INT-- Logged in user  
                L_NewBranchID
            );
        
        SELECT VendorTenantID INTO @VendorTenantID from Empirical_Prod.Company  WHERE CompanyID=L_NewBranchID;
        
        UPDATE Tenants_Stage SET ParentCompanyID=@ParentCompanyID, BranchID=L_NewBranchID,ConfirmedTenantID=@VendorTenantID,IsProcessed=1,ModifiedDate=current_timestamp() Where Tenant_Stage_Id=  @Tenant_Stage_Id ;
        
        SELECT 
			MIN(Tenant_Stage_Id)
		INTO @Tenant_Stage_Id FROM
			tempTenants
		WHERE Tenant_Stage_Id > @Tenant_Stage_Id
		ORDER BY Tenant_Stage_Id;  
	END While;

	DROP TEMPORARY TABLE tempTenants;
END