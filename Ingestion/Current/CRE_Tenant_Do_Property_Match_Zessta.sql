CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Tenant_Do_Property_Match_Zessta`( IN P_BatchID INT, IN P_Provider Varchar(50),IN P_StateID INT, IN P_StateAbbr Varchar(50))
BEGIN

-- Path 1 -- Using Direct Address Match
-- ----------------------------------------------------------------------------------------------------------
DROP TEMPORARY TABLE IF EXISTS `tempStagedTenants`;
CREATE TEMPORARY TABLE tempStagedTenants as
(
select 
	TS.Tenant_Stage_ID, Address1 as AddressText, C.CityID, C.CityName, C.StateID
from
	Empirical_DataStage.Tenants_Stage TS
    JOIN Empirical_Prod.City C on C.CityName = TS.City and C.StateID=P_StateID
WHERE TS.BatchID=P_BatchID AND  TS.IsProcessed=1 and TS.PropertyID is null
);

 alter table tempStagedTenants Modify Column AddressText Varchar(255);
 CREATE INDEX idx_tempStagedTenants_Address ON tempStagedTenants(AddressText,CityID,StateID);

-- get all matching properties
DROP TEMPORARY TABLE IF EXISTS `tempMatchedProperties`;
CREATE TEMPORARY TABLE tempMatchedProperties AS
(
	 SELECT DISTINCT 
		A.ParentID as PropertyID,
        TS.Tenant_Stage_ID        
	FROM
		Empirical_Prod.Address A  USE INDEX (idx_address_property_match)
			JOIN
		tempStagedTenants TS ON A.AddressText = TS.AddressText and A.CityID= TS.CityID AND A.StateID=P_StateID and A.ParentTableID=1 and A.IsActive=1 
        
);

UPDATE tempMatchedProperties F Join Empirical_DataStage.Tenants_Stage S ON S.Tenant_Stage_ID=F.Tenant_Stage_ID SET S.PropertyID=F.PropertyID , S.IsProcessed=1, S.ModifiedDate=current_timestamp(),S.ModifiedBy=22;


-- 2 - Using direct lat/long intersect against proprty location
-- ---------------------------------------------------------------------------------------------
DROP TEMPORARY TABLE IF EXISTS tempTenantStage;
CREATE TEMPORARY TABLE tempTenantStage as
(
SELECT 
    TS.Tenant_Stage_Id,   
    ST_GeomFromText(concat('POINT(',TS.Latitude,' ', TS.Longitude,')' )) as LocationPoint
    FROM	
    Tenants_Stage TS 
    
WHERE
    TS.ProviderID = P_Provider
	AND TS.PropertyID IS NULL
	AND TS.IsProcessed=1
	AND TS.Latitude IS NOT NULL
    AND TS.Longitude IS NOT NULL
    AND TS.StateAbbr=P_StateAbbr
    AND TS.BatchID=P_BatchID 
);
ALTER TABLE tempTenantStage 
CHANGE COLUMN `LocationPoint` `LocationPoint` POINT SRID 0 NOT NULL ,
ADD SPATIAL INDEX `idx_location_locationpoint` (`LocationPoint`);

DROP TEMPORARY TABLE IF EXISTS tempPropertyLocation;
CREATE TEMPORARY TABLE tempPropertyLocation as
(
SELECT 
    A.ParentID ,
	ST_GeomFromText(concat('POINT(',L.Latitude,' ', L.Longitude,')' )) as LocationPoint
FROM
	Empirical_Prod.Address A
    JOIN Empirical_Prod.Location L ON L.LocationID=A.LocationID 
    JOIN Empirical_Prod.Property P on P.PropertyID=A.ParentID
    and A.ParentTableID=1 
    and A.IsActive=1 and A.Sequence=1 and L.Latitude <> 0 and L.Longitude <>0
    AND P.IsDeleted=0 and P.Adding=0
    AND A.StateID=P_StateID
);

ALTER TABLE tempPropertyLocation 
CHANGE COLUMN `LocationPoint` `LocationPoint` POINT NOT NULL ,
ADD SPATIAL INDEX `idx_tempPropertyLocation_locationpoint` (`LocationPoint`);


DROP TEMPORARY TABLE IF EXISTS tempInsersectResult_FirstPass;
CREATE TEMPORARY TABLE tempInsersectResult_FirstPass as
(
SELECT 
    TS.Tenant_Stage_ID, PL.ParentID as PropertyID
FROM
	tempPropertyLocation PL,tempTenantStage TS
WHERE
    MBRCONTAINS(TS.LocationPoint, PL.LocationPoint)   
 ); 
 
-- Exclude Tenants which matched to multiple properties
DROP TEMPORARY TABLE IF EXISTS tempInsersectResult_SecondPass;
CREATE TEMPORARY TABLE tempInsersectResult_SecondPass as
(
 select Tenant_Stage_ID,PropertyID from Empirical_DataStage.tempInsersectResult_FirstPass Group By Tenant_Stage_ID having Count(PropertyID) =1
 );

Update tempInsersectResult_SecondPass SP 
JOIN Empirical_DataStage.Tenants_Stage TS ON TS.Tenant_Stage_Id=SP.Tenant_Stage_Id 
SET TS.PropertyID =SP.PropertyID, TS.IsProcessed=1, TS.ModifiedDate=current_timestamp(),TS.ModifiedBy=22
;

-- Path 3 - Using Lat/Long Intersect against Parcel Shape
-- ----------------------------------------------------------------------------------------------
DROP TEMPORARY TABLE IF EXISTS tempBaseList;
CREATE TEMPORARY TABLE tempBaseList
(
select Tenant_Stage_Id from Tenants_Stage TS 
Where TS.BatchID=P_BatchID and TS.PropertyID is null and IFNULL(TS.IsProcessed,0)=1
);

-- Get the matching shape files for the tenant coordinates
DROP TEMPORARY TABLE IF EXISTS tempTNShapes;
 CREATE TEMPORARY TABLE tempTNShapes as
 (
	SELECT 
		S.ShapeID, TS.Tenant_Stage_ID
	FROM
		Empirical_GIS.Parcel_Shapes S,
		Empirical_DataStage.Tenants_Stage TS
			JOIN
		tempBaseList BL ON BL.Tenant_Stage_ID = TS.Tenant_Stage_ID
	WHERE
		MBRCONTAINS(S.Shape,POINT(TS.Longitude, TS.Latitude))
			AND TS.Latitude != 0
			AND TS.Longitude != 0
			AND TS.IsProcessed = 1
             AND TS.PropertyID IS NULL
 );
CREATE INDEX idx_tempMatchedShapes_ShapeID ON tempTNShapes (ShapeID);

DROP TEMPORARY TABLE IF EXISTS tempPropertyShapes;
CREATE TEMPORARY TABLE tempPropertyShapes AS
(
	 SELECT 
		S.ShapeID,L.LocationID,L.GISShapeID,A.ParentID as PropertyID
	FROM
		Empirical_Prod.Location L 
		JOIN Empirical_GIS.Parcel_Shapes S ON S.ShapeID= L.GISShapeID
		JOIN Empirical_Prod.Address A on A.LocationID= L.LocationID and A.ParentTableID=1 and A.IsActive=1 and A.Sequence=1
	WHERE
	MBRCONTAINS(S.Shape,POINT(L.Longitude,L.Latitude))
);
CREATE INDEX idx_tempPropertyShapes_ShapeID ON tempPropertyShapes (ShapeID);

DROP TEMPORARY TABLE IF EXISTS tempSinglePropertyShapes;
CREATE TEMPORARY TABLE tempSinglePropertyShapes as
(
select ShapeID, Count(PropertyID) from tempPropertyShapes
Group By ShapeID Having Count(PropertyID)=1
);

DROP TEMPORARY TABLE IF EXISTS tempFinal;
Create TEMPORARY TABLE tempFinal as
(
Select SC.*,P.PropertyID from tempTNShapes SC 
JOIN tempSinglePropertyShapes PS ON PS.ShapeID=SC.ShapeID
JOIN tempPropertyShapes P ON P.ShapeID=PS.ShapeID
JOIN Empirical_DataStage.Tenants_Stage S on S.Tenant_Stage_Id=SC.Tenant_Stage_ID 
) ;

UPDATE tempFinal F Join Empirical_DataStage.Tenants_Stage S ON S.Tenant_Stage_ID=F.Tenant_Stage_ID SET S.PropertyID=F.PropertyID ,S.IsProcessed=1, S.ModifiedDate=current_timestamp(),S.ModifiedBy=22;



DROP TEMPORARY TABLE IF EXISTS tempPropertyExisting;
DROP TEMPORARY TABLE IF EXISTS tempBaseList;
DROP TEMPORARY TABLE IF EXISTS tempTNShapes;
DROP TEMPORARY TABLE IF EXISTS tempPropertyShapes;
DROP TEMPORARY TABLE IF EXISTS tempSinglePropertyShapes;
DROP TEMPORARY TABLE IF EXISTS `tempStagedTenants`;
DROP TEMPORARY TABLE IF EXISTS `tempMatchedProperties`;
DROP TEMPORARY TABLE IF EXISTS tempTenantStage;
DROP TEMPORARY TABLE IF EXISTS tempPropertyLocation;
DROP TEMPORARY TABLE IF EXISTS tempInsersectResult_FirstPass;
DROP TEMPORARY TABLE IF EXISTS tempInsersectResult_SecondPass;
	
END