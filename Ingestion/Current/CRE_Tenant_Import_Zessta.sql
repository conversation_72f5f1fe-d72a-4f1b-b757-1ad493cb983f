CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Tenant_Import_Zessta`(IN P_BatchID INT)
BEGIN

DECLARE L_NewBranchID INT;
DROP TABLE IF EXISTS tempTenants;
CREATE TEMPORARY TABLE tempTenants AS
(

	SELECT 
		Tenant_Stage_Id,
		#DUNS,
		`TenantNAME` as CompanyName,
		`ADDRESS1` as Address1,
		`ADDRESS2` as Address2,
		`CITY` AS City,
		C.CityID,
		#`STATEABBR`,
		S.StateID,
		14 as CountryID,
		`PostalCode` as Zipcode,
		concat(trim(LEADING '0' from `CountryCode`),OfficePhone) as OfficePhone,
		CASE WHEN `FAX` ='' THEN '' ELSE concat(trim(LEADING '0' from `CountryCode`),`FAX`) END as Fax,
		`SICCode` as SICCode,
		348 as MetroID,
		1 as BranchStatus,
		0 as IsM<PERSON><PERSON>,
        Latitude,
        Longitude
        
	FROM
		Tenants_Stage ST
			JOIN
		Empirical_Prod.City C ON C.CityName = TRIM(LOWER(ST.`CITY`))
			AND C.StateID = 58
			JOIN
		Empirical_Prod.State S ON S.StateAbbr= TRIM(LOWER(ST.`STATEABBR`))
			AND S.CountryID = 14 and C.StateID=S.StateID
	WHERE
		ST.IsProcessed=0 and ST.ProviderID=6 and ST.BatchID=P_BatchID
	);
select * from tempTenants;
	SELECT 
		MIN(Tenant_Stage_Id)
	INTO @Tenant_Stage_Id FROM
		tempTenants
	ORDER BY Tenant_Stage_Id;
select  @Tenant_Stage_Id;
	While(@Tenant_Stage_Id is not null )  DO	
		 Select 
         CompanyName,
         #DUNS,
         Address1,
         Address2,
         CityID ,
         StateID,
         CountryID,
         Zipcode,OfficePhone,Fax,SICCode,MetroID,BranchStatus,IsMember,Latitude,Longitude,22 
         INTO @CompanyName,@Address1,@Address2,@CityID,@StateID,@CountryID,@Zipcode,@OfficePhone,@Fax,@SICCode,@MetroID,@BranchStatus,@IsMember,@Latitude,@Longitude,@EntityID 
         From tempTenants where Tenant_Stage_Id=@Tenant_Stage_Id;  
         
         -- Create Company
         INSERT INTO Empirical_Prod.Company
				(
					CompanyName,					
					CreatedBy,
					CreatedDate,
					ModifiedBy,
					ModifiedDate
				)
				Values
				(
					@CompanyName,
					@EntityID,
					current_timestamp(),
					@EntityID,
					current_timestamp()
				);
         
			select  LAST_INSERT_ID() INTO @ParentCompanyID;         
			select @ParentCompanyID; 
            CALL Empirical_Prod.CRE_Import_Branch_Save_Zessta(
				-1, -- P_CompanyID INT, 
				@ParentCompanyID, -- P_ParentCompanyID INT,
				NULL, -- P_ConfirmedTenantID int,
				null, -- P_CompanyName Varchar(200),
				NULL, -- P_Website  varchar(225),	
				NULL, -- P_CompanyTypeID INT,
				NULL, -- P_IsNationalBrokerageCompany tinyint(1),
				NULL, -- p_IsPublicCompany tinyint(1),
				NULL, -- P_TickerSymbol varchar(20),
				@Address1, -- P_Address1 VARCHAR(200),
				@Address2, -- P_Address2 VARCHAR(50),
				@CityID, -- P_CityID INT,
				@StateID, -- P_StateID INT,
				@Zipcode, -- P_ZipCode Varchar(10),
				NULL, -- P_CountyID INT,
				@CountryID, -- P_CountryID INT,
				@MetroID, -- P_MetroID INT,
				@Latitude, -- P_Latitude DECIMAL(21,14),
				@Longitude, -- P_Longitude DECIMAL(21,14),
				@OfficePhone,-- P_OfficePhone varchar(20),
				@Fax, -- P_Fax varchar(20),
				NULL, -- P_Email varchar(255),
				1,-- P_IsActive tinyint(1),
				NULL, -- P_IsCountyHQ tinyint(1),
				NULL, -- P_IsGlobalHQ tinyint(1),
				NULL, -- P_Researcher int ,
				NULL, -- P_SalesRep int,
				NULL, -- P_SupportAgent int,
				NULL, -- P_NAICSCode Varchar(20),
				@SICCode, -- P_ISIC varchar(20),
				null, -- P_RatingTierID int,
				0, -- P_IsMember tinyint(1),
                0,-- @DUNS,-- P_ExtVendorID
                14, -- P_ProviderID,                
				@EntityID,-- P_EntityID INT-- Logged in user  
                                null,
                null,
                null,

                L_NewBranchID
            );
        select  L_NewBranchID;
        SELECT VendorTenantID_Del INTO @VendorTenantID from Empirical_Prod.Company  WHERE CompanyID=L_NewBranchID;
        
        UPDATE Tenants_Stage SET ParentCompanyID=@ParentCompanyID, BranchID=L_NewBranchID,ConfirmedTenantID=@VendorTenantID,IsProcessed=1,ModifiedDate=current_timestamp() Where Tenant_Stage_Id=  @Tenant_Stage_Id ;
        
        SELECT 
			MIN(Tenant_Stage_Id)
		INTO @Tenant_Stage_Id FROM
			tempTenants
		WHERE Tenant_Stage_Id > @Tenant_Stage_Id
		ORDER BY Tenant_Stage_Id;  
	END While;

	DROP TEMPORARY TABLE tempTenants;
END