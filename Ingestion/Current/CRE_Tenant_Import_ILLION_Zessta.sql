CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Tenant_Import_ILLION_Zessta`(
IN P_BatchID INT
)
BEGIN

DECLARE L_Offset INT;

SET @TotalRecords=NULL,
    @LoopCount= NULL,
    @Counter=1;
SET L_Offset= 10000;

-- SET @TotalRecords=1;
/*   
SELECT COUNT(1) INTO @TotalRecords 
FROM 
Empirical_DataStage.Tenants_Stage_ILLION 

WHERE
    BatchID = P_BatchID
AND Tenants_Stage_Id IS NULL
AND (`ASIC Entity Status` = 'REGD'
			OR `Update Recency Indicator` = 1
			OR IFNULL(TRIM(`Existence Score`), '') <> '')
			AND TRIM(AddressLine1) <> ''; 
*/

-- SET @TotalRecords=100000;

SELECT COUNT(1) INTO @TotalRecords 
FROM 
Empirical_DataStage.Tenants_Stage_ILLION_Zessta IL
-- LEFT JOIN Empirical_DataStage.Tenants_Stage TS ON TS.VendorID =IL.ID AND IL.BatchID = P_BatchID AND TS.BatchID = P_BatchID
WHERE
IL.Tenants_Stage_Id IS NULL
-- AND TS.VendorID IS NULL
AND ( IL.`ASIC Entity Status` = 'REGD'    OR   IL.ABNStatus  = 'ACT')
AND TRIM(AddressLine1) <> ''; 


SET @LoopCount = CEILING(@TotalRecords/ L_Offset);

WHILE  @Counter <=  @LoopCount DO

	DROP TEMPORARY TABLE IF EXISTS tempIllionStage ;
	CREATE TEMPORARY TABLE tempIllionStage AS
	(
		SELECT 
		IL.ID AS VendorID,
		CASE
			WHEN TRIM(IL.`Entity Name`) = '' THEN NULL
			ELSE IL.`Entity Name`
		END AS TenantName,
		IL.AddressLine1 AS Address1,
		IL.AddressLine2 AS Address2,
		IL.Suburb AS City,
		NULL AS State,
		IL.State AS StateAbbr,
		14 AS CountryCode,
		IL.Postcode AS PostalCode,
		TRIM(IL.Phone) AS OfficePhone,
		IL.Fax AS Fax,
		CONCAT(IL.`Key Decision Maker First Name`,
				' ',
				IL.`Key Decision Maker Middle Name`,
				' ',
				IL.`Key Decision Maker Last Name`) AS CEOName,
		IL.`Key Decision Maker Position` AS CEOTitle,
		IL.LineofBusiness AS LineOfBusiness,
		IL.`Primary SIC` AS SICCode,
		IL.Revenue AS Revenue,
		NULL AS EmployeesAtLocation,
		IL.`Number of Employees` AS EmployeeCount,
		IL.`ABN Entity Type` AS LegalStatus,
		NULL AS StatusCode,
		IL.`Hierarchical Level` AS SubsidiaryCode,
		0 AS IsProcessed,
		NULL AS ConfirmedTenantID,
		NULL AS PropertyID,
		NULL AS MatchingScore,
		CURRENT_TIMESTAMP() AS CreatedDate,
		CURRENT_TIMESTAMP() AS ModifiedDate,
		NULL AS Tenant_Stage_Id,
		IL.Latitude AS Latitude,
		IL.Longitude AS Longitude,
		NULL AS ParentCompanyID,
		NULL AS BranchID,
		P_BatchID AS BatchID,
		6 AS ProviderID,
		NULL AS IsDefault,
		NULL AS NAICSCode,
		NULL AS NACECode,
		IL.Email AS Email,
		IL.Website AS Website,
		IL.Tenants_Stage_Illion_ID,
        IL.`Primary SIC Desc` PrimarySICDesc,
        IL.`Primary SIC 3 Digit` PrimarySIC3Digit,
        IL.`Primary SIC 3 Digit Desc` PrimarySIC3DigitDesc,
        IL.`Primary SIC 2 Digit` PrimarySIC2Digit,
        IL.`Primary SIC 2 Digit Desc` PrimarySIC2DigitDesc,
        IL.`Primary SIC Division` PrimarySICDivision,
        IL.`Primary SIC Division Desc` PrimarySICDivisionDesc,
        IL.`ASIC Entity Status` ASICEntityStatus,
        IL.`ASIC Entity Type` ASICEntityType,
        IL.`ASIC Entity Class` ASICEntityClass,
        IL.ABNStatus,
        IL.ABN_StatusFromDate,
        IL.GST_Status,
        IL.GST_StatusFromDate,
        IL.RegistrationOrIncorporationDate,
        IL.`Entity Age(Years)` EntityAge,
        IL.`Employee Indicator` EmployeeIndicator,
        IL.`Revenue Indicator` RevenueIndicator,
        NULL as HQ_ID,
        NULL HQ_CompanyName,
        IL.`Number of Members in Hierachy` NumberofMembersinHierarchy,
        IL.`Immediate Parent DUNS` ImmediateParentDUNS,
        IL.`Immediate Parent Name` ImmediateParentName,
        IL.`Immediate Parent Country` ImmediateParentCountry,
        IL.`Domestic Parent DUNS` DomesticParentDUNS,
        IL.`Domestic Parent Name` DomesticParentName,
        IL.`Domestic Parent Country` DomesticParentCountry,
        IL.`Global Ultimate Parent DUNS` GlobalUltimateParentDUNS,
        IL.`Global Ultimate Parent Name` GlobalUltimateParentName,
        IL.`Global Ultimate Parent Country` GlobalUltimateParentCountry
	FROM
		Tenants_Stage_ILLION_Zessta IL
        -- LEFT JOIN Empirical_DataStage.Tenants_Stage TS ON TS.VendorID =IL.ID
	WHERE
		IL.BatchID = P_BatchID
		AND IL.Tenants_Stage_Id IS NULL
		-- AND TS.VendorID IS NULL
		AND ( IL.`ASIC Entity Status` = 'REGD'    OR   IL.ABNStatus  = 'ACT')
		AND TRIM(IL.AddressLine1) <> ''
		LIMIT L_Offset 
	);
select * from tempIllionStage;
	INSERT INTO Tenants_Stage 
	(
		VendorID,
		TenantName,
		Address1,
		Address2,
		City,
		State,
		StateAbbr,
		CountryCode,
		PostalCode,
		OfficePhone,
		Fax,
		CEOName,
		CEOTitle,
		LineOfBusiness,
		SICCode,
		Revenue,
		EmployeesAtLocation,
		EmployeeCount,
		LegalStatus,
		StatusCode,
		SubsidiaryCode,
		IsProcessed,
		ConfirmedTenantID,
		PropertyID,
		MatchingScore,
		CreatedDate,
		ModifiedDate,
		Tenant_Stage_Id,
		Latitude,
		Longitude,
		ParentCompanyID,
		BranchID,
		BatchID,
		ProviderID,
		IsDefault,
		NAICSCode,
		NACECode,
		Email,
		WebsiteURL,
		PrimarySICDesc,
        PrimarySIC3Digit,
        PrimarySIC3DigitDesc,
        PrimarySIC2Digit,
        PrimarySIC2DigitDesc,
        PrimarySICDivision,
        PrimarySICDivisionDesc,
        ASICEntityStatus,
        ASICEntityType,
        ASICEntityClass,
        ABNStatus,
        ABN_StatusFromDate,
        GST_Status,
        GST_StatusFromDate,
        RegistrationOrIncorporationDate,
        EntityAge,
        EmployeeIndicator,
        RevenueIndicator,
        HQ_ID,
        HQ_CompanyName,
        NumberofMembersinHierarchy,
        ImmediateParentDUNS,
        ImmediateParentName,
        ImmediateParentCountry,
        DomesticParentDUNS,
        DomesticParentName,
        DomesticParentCountry,
        GlobalUltimateParentDUNS,
        GlobalUltimateParentName,
        GlobalUltimateParentCountry
	) 
	SELECT 
		VendorID,
		TenantName,
		Address1,
		Address2,
		City,
		State,
		StateAbbr,
		CountryCode,
		PostalCode,
		OfficePhone,
		Fax,
		CEOName,
		CEOTitle,
		LineOfBusiness,
		SICCode,
		Revenue,
		EmployeesAtLocation,
		EmployeeCount,
		LegalStatus,
		StatusCode,
		SubsidiaryCode,
		IsProcessed,
		ConfirmedTenantID,
		PropertyID,
		MatchingScore,
		CreatedDate,
		ModifiedDate,
		Tenant_Stage_Id,
		Latitude,
		Longitude,
		ParentCompanyID,
		BranchID,
		BatchID,
		ProviderID,
		IsDefault,
		NAICSCode,
		NACECode,
		Email,
		Website,
        PrimarySICDesc,
        PrimarySIC3Digit,
        PrimarySIC3DigitDesc,
        PrimarySIC2Digit,
        PrimarySIC2DigitDesc,
        PrimarySICDivision,
        PrimarySICDivisionDesc,
        ASICEntityStatus,
        ASICEntityType,
        ASICEntityClass,
        ABNStatus,
        ABN_StatusFromDate,
        GST_Status,
        GST_StatusFromDate,
        RegistrationOrIncorporationDate,
        EntityAge,
        EmployeeIndicator,
        RevenueIndicator,
        HQ_ID,
        HQ_CompanyName,
        NumberofMembersinHierarchy,
        ImmediateParentDUNS,
        ImmediateParentName,
        ImmediateParentCountry,
        DomesticParentDUNS,
        DomesticParentName,
        DomesticParentCountry,
        GlobalUltimateParentDUNS,
        GlobalUltimateParentName,
        GlobalUltimateParentCountry
	FROM 
		tempIllionStage ;  
            select "WEREWR";
    /*    
	UPDATE
    tempIllionStage TT
    JOIN Tenants_Stage TS ON TS.VendorID=TT.VendorID AND TS.BatchID=TT.BatchID
     JOIN Tenants_Stage_ILLION I on I.ID=TS.VendorID AND I.BatchID=TS.BatchID
		SET I.Tenants_Stage_ID=TS.Tenant_Stage_Id
	WHERE I.BatchID=P_BatchID 
    AND I.Tenants_Stage_ID IS NULL;
	*/
    UPDATE
	Empirical_DataStage.Tenants_Stage TS
	JOIN Empirical_DataStage.Tenants_Stage_ILLION_Zessta IL ON IL.ID =TS.VendorID and IL.BatchID=P_BatchID and TS.BatchID=P_BatchID
	SET IL.Tenants_Stage_ID=TS.Tenant_Stage_Id
	WHERE IL.Tenants_Stage_ID IS NULL;

	DROP TEMPORARY TABLE tempIllionStage;

	
SET @Counter=@Counter+1;

END WHILE;


END