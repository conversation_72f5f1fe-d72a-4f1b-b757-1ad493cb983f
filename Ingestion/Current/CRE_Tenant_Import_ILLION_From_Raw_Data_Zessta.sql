CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Tenant_Import_ILLION_From_Raw_Data_Zessta`(
IN P_BatchID INT
)
BEGIN

DECLARE L_Offset INT;

SET @TotalRecords=NULL,
    @LoopCount= NULL,
    @Counter=1;
SET L_Offset= 10000;

-- SET @TotalRecords=1;

-- Find total number of rows to be processed and staged into next level

/*
SELECT COUNT(1) INTO @TotalRecords
FROM 
Empirical_DataStage.Tenants_Stage_ILLION_APR_2021 R 
LEFT JOIN Empirical_DataStage.Tenants_Stage_ILLION S ON S.BatchID=R.BatchID and S.﻿ID=R.ID
WHERE 
R.BatchID=P_BatchID and S.﻿ID IS NULL; */


SELECT COUNT(1)  INTO @TotalRecords
FROM 
Empirical_DataStage.Tenants_Stage_ILLION_NOV_2023 R 
LEFT JOIN Empirical_DataStage.Tenants_Stage_ILLION_Zessta S ON S.BatchID=R.BatchID and S.ID=R.ID
WHERE 
R.BatchID=P_BatchID 
AND ( R.`ASIC Entity Status` = 'REGD'    OR   R.ABNStatus  = 'ACT')
AND TRIM(CONCAT(R.StreetNo, ' ',R.StreetName,' ',R.StreetType)) <> '' 
and S.ID IS NULL; 

SET @LoopCount = CEILING(@TotalRecords/ L_Offset);

	-- Select @TotalRecords,@BatchSize,@LoopCount ;
WHILE  @Counter <=  @LoopCount DO

	INSERT INTO Empirical_DataStage.Tenants_Stage_ILLION_Zessta
	SELECT 
		R.ID,
	   R.Recency_Yrs as `Update Recency Indicator`,
	   null as `Existence Score`,
	   R.`ACN`,
	   R.`ABN`,
	   R.`Entity Name`,
	   CONCAT(R.StreetNo, ' ',R.StreetName,' ',R.StreetType) `AddressLine1`,
	   R.UnitNo as `AddressLine2`,
	   R.TownSuburb as `Suburb`,
	   R.`State`,
	   R.`Postcode`,
	   R.`Latitude`,
	   R.`Longitude`,
	   R.`Email`,
	   R.`Website`,
	   R.`Phone`,
	   R.`Fax`,
	   R.`LineofBusiness`,
	   R.`Primary SIC`,
	   R.`Primary SIC Desc`,
	   R.`Primary SIC 3 Digit`,
	   R.`Primary SIC 3 Digit Desc`,
	   R.`Primary SIC 2 Digit`,
	   R.`Primary SIC 2 Digit Desc`,
	   R.`Primary SIC Division`,
	   R.`Primary SIC Division Desc`,
	   R.`Import Export Flag`,
	   R.`ABNStatus`,
	   R.`ABN_StatusFromDate`,
	   R.`ABN Entity Type`,
	   R.`ASIC Entity Status`,
	   R.`ASIC Entity Type`,
	   R.`ASIC Entity Class`,
	   null as `ASIC Entity Subclass`,
	   R.`GST_Status`,
	   R.`GST_StatusFromDate`,
	   R.`Key Decision Maker Salutation`,
	   R.`Key Decision Maker First Name`,
	   R.`Key Decision Maker Middle Name`,
	   R.`Key Decision Maker Last Name`,
	   R.`Key Decision Maker Position`,
	   R.`RegistrationOrIncorporationDate`,
	   R.`Entity Age(Years)`,
	   R.`Number of Employees`,
	   R.`Employee Indicator`,
	   R.`Revenue`,
	   R.`Revenue Indicator`,
	   R.`Hierarchical Level`,
	   R.`Number of Members in Hierarchy` as `Number of Members in Hierachy`,
	   null as `Entity Hierarchy Type`,
	   R.`Immediate Parent DUNS`,
	   R.`Immediate Parent Name`,
	   R.`Immediate Parent Country`,
	   R.`Domestic Parent DUNS`,
	   R.`Domestic Parent Name`,
	   R.`Domestic Parent Country`,
	   R.`Global Ultimate Parent DUNS`,
	   R.`Global Ultimate Parent Name`,
	   R.`Global Ultimate Parent Country`,
		null as `Tenants_Stage_Illion_ID`,
	   null as `Tenants_Stage_ID`,
	   R.`BatchID`,
       null as `ANZSICCode`,
	null as `HQ_ID`,
	null as `HQ_CompanyName`,
    now() as CreatedDateTime,
    now() as UpdatedDateTime
       
	FROM 
	Empirical_DataStage.Tenants_Stage_ILLION_NOV_2023 R 
	LEFT JOIN Empirical_DataStage.Tenants_Stage_ILLION_Zessta S ON S.BatchID=R.BatchID and S.ID=R.ID
	WHERE 
		R.BatchID=P_BatchID 
		AND ( R.`ASIC Entity Status` = 'REGD'    OR   R.ABNStatus  = 'ACT')
		AND TRIM(CONCAT(R.StreetNo, ' ',R.StreetName,' ',R.StreetType)) <> ''
		AND S.ID IS NULL
		/*AND Postcode NOT IN ('3000','3004','3006', '4000', '4064') 
		AND State='VIC'  
		AND  MarketableFlag='1'
		AND (AddrCareOf = '' or 'Primary SIC' in ('8111','8721'))
		*/
	LIMIT L_Offset ;
    
    SET @Counter=@Counter+1;

END WHILE;
    
END