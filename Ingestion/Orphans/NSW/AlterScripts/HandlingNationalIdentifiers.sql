
drop temporary table if exists tempTS;
create temporary table tempTS(
	select Tenant_Stage_Id 
    from Empirical_DataStage.Tenants_Stage 
    where PropertyID is null and 
		((PostalCode>=1000 and PostalCode<=2599 ) or 
		 (PostalCode>=2619 and PostalCode<=2899) or 
         (PostalCode>=2921 and PostalCode<=2999)) 
		and StateAbbr='NSW'
	);
  

/*
  
delete from Empirical_Prod.NationalIdentifiers where Tenant_Stage_ID in (select Tenant_Stage_Id from tempTS );
delete from Empirical_DataStage.Tenants_Stage where PropertyID is null and 
		((PostalCode>=1000 and PostalCode<=2599 ) or 
		 (PostalCode>=2619 and PostalCode<=2899) or 
         (PostalCode>=2921 and PostalCode<=2999)) 
		and StateAbbr='NSW' and  Tenant_Stage_ID in (select Tenant_Stage_Id from tempTS );

*/