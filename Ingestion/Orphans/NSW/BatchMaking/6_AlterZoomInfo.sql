ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Orphan_Zoominfo_Tenants` 
ADD COLUMN `NSW_Raw_Orphan_Zoominfo_Tenants` BIGINT NOT NULL AUTO_INCREMENT,
ADD COLUMN `Tenant_Stage_ID` BIGINT NULL AFTER `NSW_Raw_Orphan_Zoominfo_Tenants`,
ADD PRIMARY KEY (`NSW_Raw_Orphan_Zoominfo_Tenants`);

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Orphan_Zoominfo_Tenants` 
ADD UNIQUE INDEX `Tenant_Stage_ID_UNIQUE` (`Tenant_Stage_ID` ASC) VISIBLE;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Orphan_Zoominfo_Tenants` 
CHANGE COLUMN `NSW_Raw_Orphan_Zoominfo_Tenants` `NSW_Raw_Orphan_Zoominfo_Tenants` BIGINT NOT NULL AUTO_INCREMENT ,
ADD PRIMARY KEY (`NSW_Raw_Orphan_Zoominfo_Tenants`);

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Orphan_Zoominfo_Tenants` 
CHANGE COLUMN `VendorID` `VendorID` VARCHAR(200) NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Orphan_Zoominfo_Tenants` 
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Orphan_Zoominfo_Tenants` 
ADD INDEX `idx_ProviderID` (`ProviderID` ASC) VISIBLE,
ADD INDEX `idx_BatchID` (`BatchID` ASC) VISIBLE;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Orphan_Zoominfo_Tenants` 
CHANGE COLUMN `StateAbbr` `StateAbbr` VARCHAR(100) NULL DEFAULT NULL ;

UPDATE Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants SET StateAbbr='NSW' WHERE State='New South Wales';

UPDATE Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants SET State =57 WHERE StateAbbr='NSW'; 