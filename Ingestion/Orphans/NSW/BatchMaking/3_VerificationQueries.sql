select count(*) from Empirical_DataStage.NSW_Raw_Orphan_Illion_Tenants as ot
  inner join Empirical_DataStage.Tenants_Stage as ts on ts.VendorID = ot.VendorID
  where ot.BatchID in (12100);
  
   select count(*) from Empirical_DataStage.NSW_Raw_Orphan_Illion_Tenants as ot
  inner join Empirical_DataStage.Tenants_Stage as ts on ts.VendorID = ot.VendorID
  where ot.BatchID in (12200) and (ts.PropertyID is not null or ts.BranchID is not null);


   select count(*) from Empirical_DataStage.NSW_Raw_Orphan_Illion_Tenants as ot
  inner join Empirical_DataStage.Tenants_Stage as ts on ts.VendorID = ot.VendorID
  where ot.BatchID in (12200) and (ts.PropertyID is not null );


     select count(*) from Empirical_DataStage.NSW_Raw_Orphan_Illion_Tenants as ot
  inner join Empirical_DataStage.Tenants_Stage as ts on ts.VendorID = ot.VendorID
  where ot.Batch<PERSON> in (12200) and (ts.BranchID is not null);