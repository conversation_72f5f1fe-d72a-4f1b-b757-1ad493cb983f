




drop  temporary table if exists  VICOrphanduplicateVendors;
create temporary table VICOrphanduplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.VIC_Raw_Orphan_Illion_Tenants  group by VendorID having count(*)>1);


update  Empirical_DataStage.VIC_Raw_Orphan_Illion_Tenants  set BatchID=13000  where Vendor<PERSON> in (
select * from VICOrphanduplicateVendors 
);




drop temporary table if exists VICOrphanuniqueVendors;
 create temporary table VICOrphanuniqueVendors(
 SELECT VendorID FROM Empirical_DataStage.VIC_Raw_Orphan_Illion_Tenants  group by VendorID having count(*)=1)
;


 update  Empirical_DataStage.VIC_Raw_Orphan_Illion_Tenants  set BatchID=13100  where VendorID in (
select * from VICOrphanuniqueVendors 
) ; 


 

  drop  temporary table if exists VICOrphanuniqueVendorsInExistingTS;
   create temporary table VICOrphanuniqueVendorsInExistingTS(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select * from VICOrphanuniqueVendors) and ProviderID=6
 );
 

  update  Empirical_DataStage.VIC_Raw_Orphan_Illion_Tenants  set BatchID=13200  where VendorID in (
select VendorID from VICOrphanuniqueVendorsInExistingTS 
);



 drop  temporary table if exists VICOrphanuniqueVendorsInExistingTSWithPropertyID;
   create temporary table VICOrphanuniqueVendorsInExistingTSWithPropertyID(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select VendorID from VICOrphanuniqueVendorsInExistingTS) and ProviderID=6 and PropertyID is not null
 );
 

  update  Empirical_DataStage.VIC_Raw_Orphan_Illion_Tenants  set BatchID=13300  where VendorID in (
select VendorID from VICOrphanuniqueVendorsInExistingTSWithPropertyID 
);



drop  temporary table if exists  VICOrphanduplicateVendors;
create temporary table VICOrphanduplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.VIC_Raw_Orphan_Illion_Tenants  group by VendorID having count(*)>1);

  drop  temporary table if exists VICOUVendorsInExTSWithPropertyIDEmptyHavingBranchID;
   create temporary table VICOUVendorsInExTSWithPropertyIDEmptyHavingBranchID(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select * from VICOrphanuniqueVendors) and ProviderID=6 and BranchID is Not null and PropertyID is null
 );
 

  update  Empirical_DataStage.VIC_Raw_Orphan_Illion_Tenants  set BatchID=13600  where VendorID in (
select VendorID from VICOUVendorsInExTSWithPropertyIDEmptyHavingBranchID 
);


drop temporary table if exists tempVICGoldRawIllionTenants;
create temporary table tempVICGoldRawIllionTenants(
select nroit.VendorID from Empirical_DataStage.VIC_Raw_Orphan_Illion_Tenants as nroit
inner join Empirical_DataStage.VIC_Raw_Confirmed_Tenants as nrct on nrct.VendorID=nroit.VendorID
);

drop temporary table if exists tempVICBronzeRawIllionTenants;
create temporary table tempVICBronzeRawIllionTenants(
select nroit.VendorID from Empirical_DataStage.VIC_Raw_Orphan_Illion_Tenants as nroit
inner join Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID as nbtwp on nbtwp.VendorID=nroit.VendorID and nbtwp.ProviderID=6
);

 update Empirical_DataStage.VIC_Raw_Orphan_Illion_Tenants  set BatchID=13400 where VendorID in (select * from tempVICGoldRawIllionTenants);
update Empirical_DataStage.VIC_Raw_Orphan_Illion_Tenants  set BatchID=13500 where VendorID in (select * from tempVICBronzeRawIllionTenants);