ALTER TABLE `Empirical_DataStage`.`Tenants_Stage_ILLION` 
DROP FOREIGN KEY `fk_Tenants_Stage_ILLION_TenantStageID`;
ALTER TABLE `Empirical_DataStage`.`Tenants_Stage_ILLION` 
DROP INDEX `fk_Tenants_Stage_ILLION_TenantStageID_idx` ;
;

drop temporary table if exists tempTS;
create temporary table tempTS(
    select Tenant_Stage_Id from Empirical_DataStage.Tenants_Stage 
    where PropertyID is null and ((PostalCode>=3000 and PostalCode<4000) or (PostalCode>=8000 and PostalCode<9000)) and StateAbbr='VIC'
);
  

/*
delete from Empirical_Prod.NationalIdentifiers where Tenant_Stage_ID in (select Tenant_Stage_Id from tempTS );
delete from Empirical_DataStage.Tenants_Stage 
    where PropertyID is null and ((PostalCode>=3000 and PostalCode<4000) or (PostalCode>=8000 and PostalCode<9000))
        and StateAbbr='VIC' and Tenant_Stage_ID in (select Tenant_Stage_Id from tempTS);
*/
