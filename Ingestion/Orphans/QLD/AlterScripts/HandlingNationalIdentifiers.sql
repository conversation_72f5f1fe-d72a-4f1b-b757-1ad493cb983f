drop temporary table if exists tempTS;
create temporary table tempTS(
    select Tenant_Stage_Id from Empirical_DataStage.Tenants_Stage 
    where PropertyID is null and ((PostalCode>=4000 and PostalCode<5000) or (PostalCode>=9000 and PostalCode<10000)) and StateAbbr='QLD'
);
  

/*
delete from Empirical_Prod.NationalIdentifiers where Tenant_Stage_ID in (select Tenant_Stage_Id from tempTS );
delete from Empirical_DataStage.Tenants_Stage 
    where PropertyID is null and ((PostalCode>=4000 and PostalCode<5000) or (PostalCode>=9000 and PostalCode<10000))
        and StateAbbr='QLD' and Tenant_Stage_ID in (select Tenant_Stage_Id from tempTS);
*/
