drop  temporary table if exists  QLDOrphanduplicateVendors;
create temporary table QLDOrphanduplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.QLD_Raw_Orphan_Zoominfo_Tenants  group by VendorID having count(*)>1);

update  Empirical_DataStage.QLD_Raw_Orphan_Zoominfo_Tenants  set BatchID=214000  where VendorID in (
select * from QLDOrphanduplicateVendors 
);

drop temporary table if exists QLDOrphanuniqueVendors;
 create temporary table QLDOrphanuniqueVendors(
 SELECT VendorID FROM Empirical_DataStage.QLD_Raw_Orphan_Zoominfo_Tenants  group by VendorID having count(*)=1)
;

 update  Empirical_DataStage.QLD_Raw_Orphan_Zoominfo_Tenants  set BatchID=214100  where VendorID in (
select * from QLDOrphanuniqueVendors 
) ; 

  drop  temporary table if exists QLDOrphanuniqueVendorsInExistingTS;
   create temporary table QLDOrphanuniqueVendorsInExistingTS(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select * from QLDOrphanuniqueVendors) and ProviderID=15
 );


update  Empirical_DataStage.QLD_Raw_Orphan_Zoominfo_Tenants  set BatchID=214200  where VendorID in (
select VendorID from QLDOrphanuniqueVendorsInExistingTS 
);



 drop  temporary table if exists QLDOrphanuniqueVendorsInExistingTSWithPropertyID;
   create temporary table QLDOrphanuniqueVendorsInExistingTSWithPropertyID(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select VendorID from QLDOrphanuniqueVendorsInExistingTS) and ProviderID=15 and PropertyID is not null
 );


  update  Empirical_DataStage.QLD_Raw_Orphan_Zoominfo_Tenants  set BatchID=214300  where VendorID in (
select VendorID from QLDOrphanuniqueVendorsInExistingTSWithPropertyID 
);



drop  temporary table if exists  QLDOrphanduplicateVendors;
create temporary table QLDOrphanduplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.QLD_Raw_Orphan_Zoominfo_Tenants  group by VendorID having count(*)>1);

  drop  temporary table if exists QLDOUVendorsInExTSWithPropertyIDEmptyHavingBranchID;
   create temporary table QLDOUVendorsInExTSWithPropertyIDEmptyHavingBranchID(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select * from QLDOrphanuniqueVendors) and ProviderID=15 and BranchID is Not null and PropertyID is null
 );


  update  Empirical_DataStage.QLD_Raw_Orphan_Zoominfo_Tenants  set BatchID=214600  where VendorID in (
select VendorID from QLDOUVendorsInExTSWithPropertyIDEmptyHavingBranchID 
);


drop temporary table if exists tempQLDGoldRawIllionTenants;
create temporary table tempQLDGoldRawIllionTenants(
select nroit.VendorID from Empirical_DataStage.QLD_Raw_Orphan_Zoominfo_Tenants as nroit
inner join Empirical_DataStage.QLD_Raw_Confirmed_Tenants as nrct on nrct.VendorID=nroit.VendorID
);

drop temporary table if exists tempQLDBronzeRawIllionTenants;
create temporary table tempQLDBronzeRawIllionTenants(
select nroit.VendorID from Empirical_DataStage.QLD_Raw_Orphan_Zoominfo_Tenants as nroit
inner join Empirical_DataStage.QLD_Bronze_Tenants as nbtwp on nbtwp.VendorID=nroit.VendorID and nbtwp.ProviderID=6
);

 update Empirical_DataStage.QLD_Raw_Orphan_Zoominfo_Tenants  set BatchID=214400 where VendorID in (select * from tempQLDGoldRawIllionTenants);
update Empirical_DataStage.QLD_Raw_Orphan_Zoominfo_Tenants  set BatchID=214500 where VendorID in (select * from tempQLDBronzeRawIllionTenants);