select count(*) from Empirical_DataStage.QLD_Raw_Orphan_Illion_Tenants as ot
  inner join Empirical_DataStage.Tenants_Stage as ts on ts.VendorID = ot.VendorID
  where ot.BatchID in (14100);
  
   select count(*) from Empirical_DataStage.QLD_Raw_Orphan_Illion_Tenants as ot
  inner join Empirical_DataStage.Tenants_Stage as ts on ts.VendorID = ot.VendorID
  where ot.BatchID in (14200) and (ts.PropertyID is not null or ts.BranchID is not null);


   select count(*) from Empirical_DataStage.QLD_Raw_Orphan_Illion_Tenants as ot
  inner join Empirical_DataStage.Tenants_Stage as ts on ts.VendorID = ot.VendorID
  where ot.BatchID in (14200) and (ts.PropertyID is not null );


     select count(*) from Empirical_DataStage.QLD_Raw_Orphan_Illion_Tenants as ot
  inner join Empirical_DataStage.Tenants_Stage as ts on ts.VendorID = ot.VendorID
  where ot.BatchID in (14200) and (ts.BranchID is not null);