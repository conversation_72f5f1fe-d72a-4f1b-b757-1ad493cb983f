ALTER TABLE `Empirical_DataStage`.`QLD_Raw_Orphan_Illion_Tenants` 
ADD COLUMN `QLD_Raw_Orphan_Illion_Tenants` BIGINT NOT NULL AUTO_INCREMENT,
ADD COLUMN `Tenant_Stage_ID` BIGINT NULL AFTER `QLD_Raw_Orphan_Illion_Tenants`,
ADD PRIMARY KEY (`QLD_Raw_Orphan_Illion_Tenants`),
ADD UNIQUE INDEX `Tenant_Stage_ID_UNIQUE` (`Tenant_Stage_ID` ASC) VISIBLE;
;

ALTER TABLE Empirical_DataStage.QLD_Raw_Orphan_Illion_Tenants 
CHANGE COLUMN VendorID VendorID VARCHAR(300) NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`QLD_Raw_Orphan_Illion_Tenants` 
CHANGE COLUMN `BatchID` `BatchID` BIGINT NULL DEFAULT NULL ;


ALTER TABLE `Empirical_DataStage`.`QLD_Raw_Orphan_Illion_Tenants` 
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;

ALTER TABLE `Empirical_DataStage`.`QLD_Raw_Orphan_Illion_Tenants` 
ADD INDEX `idx_ProviderID` (`ProviderID` ASC) VISIBLE,
ADD INDEX `idx_BatchID` (`BatchID` ASC) VISIBLE;


ALTER TABLE `Empirical_DataStage`.`QLD_Raw_Orphan_Illion_Tenants` 
CHANGE COLUMN `NationalID` `NationalID` TEXT NULL DEFAULT NULL ;
