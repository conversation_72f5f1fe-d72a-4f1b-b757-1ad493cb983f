drop temporary table if exists tempChangelog;
create temporary table tempChangelog(
SELECT
CB.PropertyID,
CLC.CompanyID,
CASE
  WHEN C.CompanyName IS NULL THEN C.AltCompanyName
  ELSE C.CompanyName
END AS `CompanyName`,
C.AltCompanyName,
CLF.FieldName,
CLC.OldValue,
CLC.NewValue,
CONCAT(PE.FirstName, " ",PE.LastName) AS ModifiedBy,
CLC.ChangedDate,
A.ApplicationName,
AC.ActionName
FROM
  `Empirical_Prod`.`ChangeLogCompany` CLC
LEFT JOIN
 `Empirical_Prod`.`ChangeLogFields`CLF
  ON CLC.ChangeLogFieldID = CLF.ChangeLogFieldID
LEFT JOIN
  `Empirical_Prod`.`Entity` E
  ON E.EntityID = CLC.ChangedBy
LEFT JOIN
  `Empirical_Prod`.`Person` PE
  ON PE.PersonID = E.PersonID
LEFT JOIN
  `Empirical_Prod`.`Application` A
  ON CLC.ApplicationID = A.ApplicationID
LEFT JOIN
  `Empirical_Prod`.`Action` AC
  ON CLC.ActionID = AC.ActionID
LEFT JOIN
  `Empirical_Prod`.`Company` C
  ON CLC.CompanyID = C.CompanyID
LEFT JOIN
  `Empirical_Prod`.`vw_CompanyBranch` CB
  ON CLC.CompanyID = CB.CompanyID
WHERE CLC.ChangedDate >= DATE '2023-10-01' AND CLC.ChangedDate < DATE '2024-04-01'
and CONCAT(PE.FirstName, " ",PE.LastName) != 'import user'
-- and A.ApplicationName != 'TSA'
ORDER BY CLC.ChangedDate DESC
);

drop temporary table if exists comProperties;
create temporary table comProperties(
select ParentID from Empirical_Prod.Address where ZipCode in (3000, 3002, 3003, 3004, 3006, 3008, 3031, 3051, 3052, 3053, 3054)
 and ParentTableID=1 and IsActive=1 and Sequence=1
 );

select count(distinct CompanyID) from tempChangelog; -- 14936
select count(*), HideReasonComments,HidedDate from Empirical_Prod.Company where CompanyID in (select CompanyID from tempChangelog) and IsHidden=1 group by HideReasonComments,HidedDate;
select count(*),IsHidden from Empirical_Prod.Company where CompanyID in (select CompanyID from tempChangelog) group by IsHidden;

drop temporary table if exists companiesUpdatedByRstInCOMFromVw;
create temporary table companiesUpdatedByRstInCOMFromVw(
select cl.* from tempChangelog cl inner join comProperties cp on cl.PropertyID=cp.ParentID -- 1439
);

drop temporary table if exists comPropertiesCTs;
create temporary table comPropertiesCTs(
select  PropertyID, ConfirmedTenantID from Empirical_Prod.SuiteTenant 
where PropertyID in (select * from comProperties) and IsActive=1
);

drop temporary table if exists comPropertiesCTsRecords;
create temporary table comPropertiesCTsRecords(
select ctp.PropertyID, ct.CompanyID,ct.ConfirmedTenantID,ct.TenantName from Empirical_Tenants.ConfirmedTenants as ct
inner join comPropertiesCTs as ctp on ctp.ConfirmedTenantID =ct.ConfirmedTenantID
);

drop temporary table if exists companiesUpdatedByRstInCOMFromST;
create temporary table companiesUpdatedByRstInCOMFromST(
select cl.* from tempChangelog cl inner join comPropertiesCTsRecords cp on cl.CompanyID=cp.CompanyID -- 1439
);

select count(distinct CompanyID) from companiesUpdatedByRstInCOMFromST; -- 1441


select count(*), HideReasonComments,HidedBy from Empirical_Prod.Company where CompanyID in (select CompanyID from companiesUpdatedByRstInCOMFromST) and IsHidden=1 group by HideReasonComments,HidedBy;
select count(*),IsHidden from Empirical_Prod.Company where CompanyID in (select CompanyID from companiesUpdatedByRstInCOMFromST) group by IsHidden;

select count(*) from Empirical_Prod.Company 
WHERE
    CompanyID IN (select CompanyID from companiesUpdatedByRstInCOMFromST)
    and HidedBy=22 and IsHidden=1;


/*
UPDATE Empirical_Prod.Company 
SET 
   ModifiedBy=22,
   ModifiedDate= CURRENT_TIMESTAMP(),
    IsHidden = NULL,
    HidedBy = NULL,
    HidedDate = NULL,
    HideReasonID = NULL,
    SubHideReasonID = NULL,
    HideReasonComments = 'Unhiding companies updated by researcher.'
WHERE
    CompanyID IN (select CompanyID from companiesUpdatedByRstInCOMFromST)
    and HidedBy=22 and IsHidden=1;
*/