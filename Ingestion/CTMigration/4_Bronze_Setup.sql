Update Empirical_DataStage.Bronze_Tenants_2024_06_21 set BatchID=null,IsProcessed=0,Tenant_Stage_ID=null;

drop  temporary table if exists  BronzeVendors;
create temporary table BronzeVendors(
SELECT  VendorID FROM Empirical_DataStage.Bronze_Tenants_2024_06_21 where Batch<PERSON> is null);

drop  temporary table if exists  BronzeduplicateVendors;
create temporary table BronzeduplicateVendors(
SELECT  VendorID FROM BronzeVendors group by VendorID having count(*)>1);

select * from Empirical_DataStage.Bronze_Tenants_2024_06_21 where Vendor<PERSON> in (select * from BronzeduplicateVendors);
delete from Empirical_DataStage.Bronze_Tenants_2024_06_21 where VendorID='abc14fa2940e0fd1b052d022f73015d7' limit 1;

select count(*),BatchID from Empirical_DataStage.Bronze_Tenants_2024_06_21 group by Batch<PERSON>;


update Empirical_DataStage.Bronze_Tenants_2024_06_21 set State=59 where StateAbbr='VIC';

UPDATE Empirical_DataStage.Bronze_Tenants_2024_06_21 as a
SET a.NationalID = 
    CASE
        WHEN a.ACN IS NOT NULL AND a.ABN IS NOT NULL THEN CONCAT('ACN: ', a.ACN, ' ABN: ', a.ABN)
        WHEN a.ACN IS NOT NULL THEN CONCAT('ACN: ', a.ACN)
        WHEN a.ABN IS NOT NULL THEN CONCAT('ABN: ', a.ABN)
        ELSE NULL
    END;


update Empirical_DataStage.Bronze_Tenants_2024_06_21 set BatchID=24621200 where BatchID is null;


select count(*),BatchID,IsProcessed from Empirical_DataStage.Bronze_Tenants_2024_06_21 group by BatchID,IsProcessed;


drop temporary table if exists tempBronze;
create temporary table tempBronze(
SELECT ts.Tenant_Stage_Id,bt.VendorID,ts.IsHidden,ts.State as TSState,bt.State as BTState FROM Empirical_DataStage.Bronze_Tenants_2024_06_21 bt inner join Empirical_DataStage.Tenants_Stage ts 
on bt.VendorID=ts.VendorID
);

update Empirical_DataStage.Bronze_Tenants_2024_06_21 set BatchID=24621201 where VendorID in (select VendorID from tempBronze);

select count(*),ProviderID from Empirical_DataStage.Bronze_Tenants_2024_06_21 group by ProviderID; -- 14 shouldn't be there


DROP TEMPORARY TABLE IF EXISTS `Empirical_DataStage`.`TenantSourceVendor`;
create temporary table Empirical_DataStage.TenantSourceVendor (
	select Tenant_Stage_Id,VendorID,PropertyID,ProviderID from Empirical_DataStage.Tenants_Stage where VendorID in (select VendorID from Empirical_DataStage.Bronze_Tenants_2024_06_21)
);
    
update Empirical_DataStage.Bronze_Tenants_2024_06_21 as a
	inner join Empirical_DataStage.TenantSourceVendor as b on a.VendorID=b.VendorID and a.ProviderID=b.ProviderID
	set a.Tenant_Stage_ID=b.Tenant_Stage_Id;


update Empirical_DataStage.Bronze_Tenants_2024_06_21 set BatchID=24621202 where Tenant_Stage_ID is not null limit 1;
update Empirical_DataStage.Bronze_Tenants_2024_06_21 set BatchID=24621203 where Tenant_Stage_ID is null limit 1;

select * from Empirical_DataStage.Bronze_Tenants_2024_06_21 where BatchID=24621202;
select * from Empirical_DataStage.Bronze_Tenants_2024_06_21 where BatchID=24621203;

call Empirical_DataStage.CRE_Tenant_Stage_Import_Bronze_DataSet(24621200);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Bronze_DataSet(24621201);
