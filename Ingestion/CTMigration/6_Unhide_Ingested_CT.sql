drop temporary table if exists IngestedCT;
 create temporary table IngestedCT(
select
ts.Tenant_Stage_Id,
 ts.PropertyID, ts.Tenant<PERSON>ame, ts.Branch<PERSON>, c.<PERSON>, c.<PERSON>ded<PERSON>y, c.HidedDate
from Empirical_DataStage.Tenants_Stage as  ts
inner   join Empirical_Prod.Company  as c on  ts.BranchID=c.CompanyID
where ts.BatchID in(
24621300,24621301,24621302,24621303,24621304,24621305,24621306,24621307,24621308
) and ts.IsProcessed=1);
 

 select * from IngestedCT where IsHidden=1;


-- select * from Empirical_Prod.Company;
/**
UPDATE Empirical_Prod.Company 
SET 
   ModifiedBy=22,
   ModifiedDate= CURRENT_TIMESTAMP(),
    IsHidden = NULL,
    HidedBy = NULL,
    HidedDate = NULL,
    HideReasonID = NULL,
    SubHideReasonID = NULL,
    HideReasonComments = 'Unhiding companies which are stacked in beta.'
WHERE
    CompanyID IN (select BranchID from IngestedCT where IsHidden=1 );

**/