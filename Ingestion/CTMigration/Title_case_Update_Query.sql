select ts.ABNStatus as TS_ABNStatus,bt.ABNStatus as BT_ABNStatus,ts.Address1 as TS_Address1,bt.Address1 as BT_Address1,ts.Address2 as TS_Address2,
bt.Address2 as BT_Address2,ts.<PERSON><PERSON><PERSON> as TS_CEO<PERSON><PERSON>,bt.<PERSON><PERSON><PERSON> as <PERSON>_CEO<PERSON><PERSON>,ts.<PERSON><PERSON><PERSON><PERSON> as TS_CEO<PERSON><PERSON><PERSON>,bt.<PERSON><PERSON><PERSON><PERSON> as <PERSON>_CEO<PERSON><PERSON><PERSON>,
ts.City as TS_City,bt.City as BT_City,ts.EmployeeIndicator as TS_EmployeeIndicator,bt.EmployeeIndicator as BT_EmployeeIndicator,
ts.LineOfBusiness as TS_LineOfBusiness,bt.LineOfBusiness as BT_LineOfBusiness,ts.LegalStatus as TS_LegalStatus,bt.LegalStatus as BT_LegalStatus,
ts.PrimarySIC2DigitDesc as TS_PrimarySIC2DigitDesc,bt.PrimarySIC2DigitDesc as BT_PrimarySIC2DigitDesc,ts.PrimarySIC3DigitDesc as TS_PrimarySIC3DigitDesc,
bt.PrimarySIC3DigitDesc as BT_PrimarySIC3DigitDesc,ts.PrimarySICDesc as TS_PrimarySICDesc,bt.PrimarySICDesc as BT_PrimarySICDesc,
ts.PrimarySICDivisionDesc as TS_PrimarySICDivisionDesc,bt.PrimarySICDivisionDesc as BT_PrimarySICDivisionDesc,ts.RevenueIndicator as TS_RevenueIndicator,bt.RevenueIndicator as BT_RevenueIndicator,
ts.TenantName as TS_TenantName,bt.TenantName as BT_TenantName,ts.ASICEntityClass as TS_ASICEntityClass,bt.ASICEntityClass as BT_ASICEntityClass,
ts.ASICEntityStatus as TS_ASICEntityStatus,bt.ASICEntityStatus as BT_ASICEntityStatus,ts.ASICEntityType as TS_ASICEntityType,bt.ASICEntityType as BT_ASICEntityType,
ts.Email as TS_Email,bt.Email as BT_email,ts.GlobalUltimateParentName as TS_GlobalUltimateParentName,bt.GlobalUltimateParentName as BT_GlobalUltimateParentName,
ts.GlobalUltimateParentCountry as TS_GlobalUltimateParentCountry,bt.GlobalUltimateParentCountry as BT_GlobalUltimateParentCountry,
ts.ImmediateParentName as TS_ImmediateParentName,bt.ImmediateParentName as BT_ImmediateParentName,ts.ImmediateParentCountry as TS_ImmediateParentCountry,
bt.ImmediateParentCountry as BT_ImmediateParentCountry,ts.WebsiteURL as TS_WebsiteURL,bt.WebsiteURL as BT_WebsiteURL,ts.PropertyID
from Empirical_DataStage.Tenants_Stage ts inner join Empirical_DataStage.VIC_Tenants_Title_Cased_2024_06_07 bt 
on ts.VendorID=bt.VendorID where ts.PropertyID is not null;

select count(*)
from Empirical_DataStage.Tenants_Stage ts inner join Empirical_DataStage.VIC_Tenants_Title_Cased_2024_06_07 bt 
on ts.Tenant_Stage_Id=bt.Tenant_Stage_Id;

update Empirical_DataStage.Tenants_Stage ts inner join Empirical_DataStage.VIC_Tenants_Title_Cased_2024_06_07 bt 
on ts.VendorID=bt.VendorID and ts.Tenant_Stage_Id=bt.Tenant_Stage_Id set 
ts.ABNStatus=bt.ABNStatus,ts.Address1 =bt.Address1,ts.Address2=bt.Address2 ,ts.CEOName =bt.CEOName ,ts.CEOTitle =bt.CEOTitle,
ts.City =bt.City ,ts.EmployeeIndicator=bt.EmployeeIndicator,ts.LineOfBusiness=bt.LineOfBusiness,ts.LegalStatus=bt.LegalStatus,
ts.PrimarySIC2DigitDesc=bt.PrimarySIC2DigitDesc,ts.PrimarySIC3DigitDesc=bt.PrimarySIC3DigitDesc,ts.PrimarySICDesc =bt.PrimarySICDesc,
ts.PrimarySICDivisionDesc=bt.PrimarySICDivisionDesc ,ts.RevenueIndicator =bt.RevenueIndicator,ts.TenantName =bt.TenantName,
ts.ASICEntityClass=bt.ASICEntityClass,ts.ASICEntityStatus=bt.ASICEntityStatus,ts.ASICEntityType=bt.ASICEntityType,ts.Email=bt.Email,
ts.GlobalUltimateParentName=bt.GlobalUltimateParentName,ts.GlobalUltimateParentCountry=bt.GlobalUltimateParentCountry,
ts.ImmediateParentName=bt.ImmediateParentName,ts.ImmediateParentCountry=bt.ImmediateParentCountry,ts.WebsiteURL=bt.WebsiteURL;