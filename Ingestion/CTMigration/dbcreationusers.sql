

-- Username:  dbuser-<PERSON>
-- Password: DBUserJuan#621

CREATE USER 'dbuser-<PERSON>'@'%' IDENTIFIED BY 'DBUserJuan#621';
GRANT SELECT ON *.* TO 'dbuser-<PERSON>'@'%';
GRANT UPDATE ON *.* TO 'dbuser-<PERSON>'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-<PERSON>'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-<PERSON>'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-<PERSON>'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Juan'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-<PERSON>'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-<PERSON>'@'%';
FLUSH privileges;	
	
-- 3 Mekarl Sanichur 

-- Username:  dbuser-Mekarl
-- Password:  DBUserMekarl#612

CREATE USER 'dbuser-Mekarl'@'%' IDENTIFIED BY 'DBUserMekarl#612';
GRANT SELECT ON *.* TO 'dbuser-Mekarl'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Mekarl'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Mekarl'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Mekarl'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Mekarl'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Mekarl'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Mekarl'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Mekarl'@'%';
FLUSH privileges;
	
	
-- 1 Matthew Salisbury 


-- Username:  dbuser-Matthew
-- Password:  DBUserMat#610

 CREATE USER 'dbuser-Matthew'@'%' IDENTIFIED BY 'DBUserMat#610';
 GRANT SELECT ON *.* TO 'dbuser-Matthew'@'%';
-- GRANT UPDATE ON *.* TO 'dbuser-Matthew'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Matthew'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Matthew'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Matthew'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Matthew'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Matthew'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Matthew'@'%';
FLUSH privileges;	
	
-- 2 Francois Lategan 

-- Username:  dbuser-Francois
-- Password:  DBUserFr#611

CREATE USER 'dbuser-Francois'@'%' IDENTIFIED BY 'DBUserFr#611';
GRANT SELECT ON *.* TO 'dbuser-Francois'@'%';
 GRANT UPDATE ON *.* TO 'dbuser-Francois'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Francois'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Francois'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Francois'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Francois'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Francois'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Francois'@'%';
FLUSH privileges;	
	-- 4 Rick O'Connor

-- Username:  dbuser-Rick
-- Password:  DBUserRick#613

CREATE USER 'dbuser-Rick'@'%' IDENTIFIED BY 'DBUserRick#613';
GRANT SELECT ON *.* TO 'dbuser-Rick'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Rick'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Rick'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Rick'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Rick'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Rick'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Rick'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Rick'@'%';
FLUSH privileges;	
	-- Username:  dbuser-Bernard
-- Password:  DBUserBernard#614

CREATE USER 'dbuser-Bernard'@'%' IDENTIFIED BY 'DBUserBernard#614';
GRANT SELECT ON *.* TO 'dbuser-Bernard'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Bernard'@'%';
FLUSH privileges;-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Bernard'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Bernard'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Bernard'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Bernard'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Bernard'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Bernard'@'%';
FLUSH privileges;	
		-- 6 Gerard Walsh

-- Username:  gerard.walsh-db-user
-- Password: gerardwalsh2023dbU#510

CREATE USER 'gerard.walsh-db-user'@'%' IDENTIFIED BY 'gerardwalsh2023dbU#510';
GRANT SELECT ON *.* TO 'gerard.walsh-db-user'@'%';
 GRANT UPDATE ON *.* TO 'gerard.walsh-db-user'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'gerard.walsh-db-user'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'gerard.walsh-db-user'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'gerard.walsh-db-user'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'gerard.walsh-db-user'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'gerard.walsh-db-user'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'gerard.walsh-db-user'@'%';
FLUSH privileges;	Chandu, 13 Nov 2023 
		-- 7 Arnold Vadivellu

-- Username:  dbuser-Arnold
-- Password: DBUserArnold#615

CREATE USER 'dbuser-Arnold'@'%' IDENTIFIED BY 'DBUserArnold#615';
GRANT SELECT ON *.* TO 'dbuser-Arnold'@'%';
 GRANT UPDATE ON *.* TO 'dbuser-Arnold'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Arnold'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Arnold'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Arnold'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Arnold'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Arnold'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Arnold'@'%';
FLUSH privileges;	
		-- 8 Rudi Du Toit

-- Username:  rudi.dutoit-db-user
-- Password: rudidutoit2023dbU#509

CREATE USER 'rudi.dutoit-db-user'@'%' IDENTIFIED BY 'rudidutoit2023dbU#509';
GRANT SELECT ON *.* TO 'rudi.dutoit-db-user'@'%';
GRANT UPDATE ON *.* TO 'rudi.dutoit-db-user'@'%';
FLUSH privileges; 
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'rudi.dutoit-db-user'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'rudi.dutoit-db-user'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'rudi.dutoit-db-user'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'rudi.dutoit-db-user'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'rudi.dutoit-db-user'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'rudi.dutoit-db-user'@'%';
FLUSH privileges;	Chandu, 13 Nov 2023 
		-- 10 Phango

-- Username:  dbuser-Phango
-- Password: DBUserPhango#616

CREATE USER 'dbuser-Phango'@'%' IDENTIFIED BY 'DBUserPhango#616';
GRANT SELECT ON *.* TO 'dbuser-Phango'@'%';
 GRANT UPDATE ON *.* TO 'dbuser-Phango'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Phango'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Phango'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Phango'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Phango'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Phango'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Phango'@'%';
FLUSH privileges;	
		-- 11 Nigel

-- Username:  dbuser-Nigel
-- Password: DBUserNigel#617

CREATE USER 'dbuser-Nigel'@'%' IDENTIFIED BY 'DBUserNigel#617';
GRANT SELECT ON *.* TO 'dbuser-Nigel'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Nigel'@'%';
FLUSH privileges;

-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Nigel'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Nigel'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Nigel'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Nigel'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Nigel'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Nigel'@'%';
FLUSH privileges;	
	-- 12 Tafadzwa Mutero

-- Username:  dbuser-Tafadzwa
-- Password: DBUserTafadzwa#618

CREATE USER 'dbuser-Tafadzwa'@'%' IDENTIFIED BY 'DBUserTafadzwa#618';
GRANT SELECT ON *.* TO 'dbuser-Tafadzwa'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Tafadzwa'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Tafadzwa'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Tafadzwa'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Tafadzwa'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Tafadzwa'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Tafadzwa'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Tafadzwa'@'%';
FLUSH privileges;	
	--  13 Phokgedi Maja

-- Username:  dbuser-Phokgedi
-- Password: DBUserPhokgedi#619

CREATE USER 'dbuser-Phokgedi'@'%' IDENTIFIED BY 'DBUserPhokgedi#619';
GRANT SELECT ON *.* TO 'dbuser-Phokgedi'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Phokgedi'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Phokgedi'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Phokgedi'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Phokgedi'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Phokgedi'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Phokgedi'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Phokgedi'@'%';
FLUSH privileges;	
	-- 14 Joash Kisten

-- Username:  dbuser-Joash-- Password: DBUserJoash#620


CREATE USER 'dbuser-Joash'@'%' IDENTIFIED BY 'DBUserJoash#620';
GRANT SELECT ON *.* TO 'dbuser-Joash'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Joash'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Joash'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Joash'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Joash'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Joash'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Joash'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Joash'@'%';
FLUSH privileges;	
	-- 9 thoughtspot.db-user

-- Username: thoughtspot.db-user
-- Password: tsDBuser2023#516

CREATE USER 'thoughtspot.db-user'@'%' IDENTIFIED BY 'tsDBuser2023#516';
GRANT SELECT ON *.* TO 'thoughtspot.db-user'@'%';
-- GRANT UPDATE ON *.* TO 'arealytics-dev-db-user'@'%';
-- REVOKE UPDATE ON *.* FROM 'arealytics-dev-db-user'@'%';
FLUSH privileges;	
	-- 16 arealytics-app

-- Username:  dbuser-arealytics-app
-- Password: DBUserArealyticsAapp#622

CREATE USER 'dbuser-arealytics-app'@'%' IDENTIFIED BY 'DBUserArealyticsAapp#622';
GRANT SELECT ON *.* TO 'dbuser-arealytics-app'@'%';
GRANT UPDATE ON *.* TO 'dbuser-arealytics-app'@'%';
FLUSH privileges;	

GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-arealytics-app'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-arealytics-app'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-arealytics-app'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-arealytics-app'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-arealytics-app'@'%';
FLUSH privileges;



GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-data-feed'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-data-feed'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-data-feed'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-data-feed'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-data-feed'@'%';
FLUSH privileges;

GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-commau-app'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-commau-app'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-commau-app'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-commau-app'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-commau-app'@'%';
FLUSH privileges;
	-- 17 commau-app

-- Username:  dbuser-commau-app
-- Password: DBUserCommauApp#623

CREATE USER 'dbuser-commau-app'@'%' IDENTIFIED BY 'DBUserCommauApp#623';
GRANT SELECT ON *.* TO 'dbuser-commau-app'@'%';
GRANT UPDATE ON *.* TO 'dbuser-commau-app'@'%';
FLUSH privileges;	
	
-- 18 data-feed

-- Username:  dbuser-data-feed
-- Password: DBUserDataFeed#624

CREATE USER 'dbuser-data-feed'@'%' IDENTIFIED BY 'DBUserDataFeed#624';
GRANT SELECT ON *.* TO 'dbuser-data-feed'@'%';
GRANT UPDATE ON *.* TO 'dbuser-data-feed'@'%';
FLUSH privileges;	
YES	YES	NO	NO	No		CREATE USER 'dbuser-databricks'@'%' IDENTIFIED BY 'DBUserDatabricks#633';
GRANT SELECT ON *.* TO 'dbuser-databricks'@'%';
GRANT UPDATE ON *.* TO 'dbuser-databricks'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-databricks'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-databricks'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-databricks'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-databricks'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-databricks'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-databricks'@'%';
FLUSH privileges;	
YES	YES	NO	NO	No		-- 19 Nischala Tummala

-- Username:  dbuser-Nischala
-- Password: DBUserNischala#624

CREATE USER 'dbuser-Nischala'@'%' IDENTIFIED BY 'DBUserNischala#624';
GRANT SELECT ON *.* TO 'dbuser-Nischala'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Nischala'@'%';
FLUSH privileges;
--For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Nischala'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Nischala'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Nischala'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Nischala'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Nischala'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Nischala'@'%';
FLUSH privileges;	
	-- 20 Srilakshmi

-- Username:  dbuser-Srilakshmi
-- Password: DBUserSrilakshmi#625

CREATE USER 'dbuser-Srilakshmi'@'%' IDENTIFIED BY 'DBUserSrilakshmi#625';
GRANT SELECT ON *.* TO 'dbuser-Srilakshmi'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Srilakshmi'@'%';
FLUSH privileges;
--For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Srilakshmi'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Srilakshmi'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Srilakshmi'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Srilakshmi'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Srilakshmi'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Srilakshmi'@'%';
FLUSH privileges;
	
	-- 21 Chandrashekar

-- Username:  dbuser-Chandrashekar
-- Password: DBUserChandrashekar#626

CREATE USER 'dbuser-Chandrashekar'@'%' IDENTIFIED BY 'DBUserChandrashekar#626';
GRANT SELECT ON *.* TO 'dbuser-Chandrashekar'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Chandrashekar'@'%';
FLUSH privileges;
--For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Chandrashekar'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Chandrashekar'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Chandrashekar'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Chandrashekar'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Chandrashekar'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Chandrashekar'@'%';
FLUSH privileges;	
YES	YES	NO	NO	No		-- 22 Sai Ram

-- Username:  dbuser-SaiRam
-- Password: DBUserSaiRam#629

CREATE USER 'dbuser-SaiRam'@'%' IDENTIFIED BY 'DBUserSaiRam#629';
GRANT SELECT ON *.* TO 'dbuser-SaiRam'@'%';
GRANT UPDATE ON *.* TO 'dbuser-SaiRam'@'%';
FLUSH privileges;
--For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-SaiRam'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-SaiRam'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-SaiRam'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-SaiRam'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-SaiRam'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-SaiRam'@'%';
FLUSH privileges;	
YES	YES	NO	NO	No		-- 23 Sai Priya

-- Username:  dbuser-SaiPriya
-- Password: DBUserSaiPriya#627

CREATE USER 'dbuser-SaiPriya'@'%' IDENTIFIED BY 'DBUserSaiPriya#627;
GRANT SELECT ON *.* TO 'dbuser-SaiPriya'@'%';
GRANT UPDATE ON *.* TO 'dbuser-SaiPriya'@'%';
FLUSH privileges;
--For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-SaiPriya'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-SaiPriya'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-SaiPriya'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-SaiPriya'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-SaiPriya'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-SaiPriya'@'%';
FLUSH privileges;	
YES	YES	NO	NO	No		-- 24 Sourav

-- Username:  dbuser-Sourav
-- Password: DBUserSourav#628

CREATE USER 'dbuser-Sourav'@'%' IDENTIFIED BY 'DBUserSourav#628';
GRANT SELECT ON *.* TO 'dbuser-Sourav'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Sourav'@'%';
FLUSH privileges;
--For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Sourav'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Sourav'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Sourav'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Sourav'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Sourav'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Sourav'@'%';
FLUSH privileges;	
YES	YES	NO	NO	No		-- 25 Prasanna

-- Username:  dbuser-Prasanna
-- Password: DBUserPrasanna#630

CREATE USER 'dbuser-Prasanna'@'%' IDENTIFIED BY 'DBUserPrasanna#630';
GRANT SELECT ON *.* TO 'dbuser-Prasanna'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Prasanna'@'%';
FLUSH privileges;
--For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Prasanna'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Prasanna'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Prasanna'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Prasanna'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Prasanna'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Prasanna'@'%';
FLUSH privileges;	
	-- 25 Jeeva James

-- Username:  dbuser-Jeeva
-- Password: DBUserJeeva#631

CREATE USER 'dbuser-Jeeva'@'%' IDENTIFIED BY 'DBUserJeeva#631';
GRANT SELECT ON *.* TO 'dbuser-Jeeva'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Jeeva'@'%';
FLUSH privileges;
--For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Jeeva'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Jeeva'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Jeeva'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Jeeva'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Jeeva'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Jeeva'@'%';
FLUSH privileges;	
	-- 25 Sujeeth

-- Username:  dbuser-Sujeeth
-- Password: DBUserSujeeth#632

CREATE USER 'dbuser-Sujeeth'@'%' IDENTIFIED BY 'DBUserSujeeth#632';
GRANT SELECT ON *.* TO 'dbuser-Sujeeth'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Sujeeth'@'%';
FLUSH privileges;
--For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Sujeeth'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Sujeeth'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Sujeeth'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Sujeeth'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Sujeeth'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Sujeeth'@'%';
FLUSH privileges;	
							
		
	-- Username:  dbuser-Mouhamad
-- Password: DBUserMouhamad#633
CREATE USER 'dbuser-Mouhamad'@'%' IDENTIFIED BY 'DBUserMouhamad#633';
GRANT SELECT ON *.* TO 'dbuser-Mouhamad'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Mouhamad'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Mouhamad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Mouhamad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Mouhamad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Mouhamad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Mouhamad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Mouhamad'@'%';
FLUSH privileges;	
	-- Username:  dbuser-Brad
-- Password: DBUserBrad#633
CREATE USER 'dbuser-Brad'@'%' IDENTIFIED BY 'DBUserBrad#633';
GRANT SELECT ON *.* TO 'dbuser-Brad'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Brad'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Brad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Brad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Brad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Brad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Brad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Brad'@'%';
FLUSH privileges;	
							
						-- Username:  dbuser-Ounayssi
-- Password: DBUserBrad#633
CREATE USER 'dbuser-Brad'@'%' IDENTIFIED BY 'DBUserBrad#633';
GRANT SELECT ON *.* TO 'dbuser-Brad'@'%';
GRANT UPDATE ON *.* TO 'dbuser-Brad'@'%';
FLUSH privileges;
-- For DEV and UAT
GRANT ALL PRIVILEGES ON Empirical_GIS.* TO 'dbuser-Brad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Prod.* TO 'dbuser-Brad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Snapshot.* TO 'dbuser-Brad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Tenants.* TO 'dbuser-Brad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_Public.* TO 'dbuser-Brad'@'%';
FLUSH privileges;
GRANT ALL PRIVILEGES ON Empirical_DataStage.* TO 'dbuser-Brad'@'%';
FLUSH privileges;	