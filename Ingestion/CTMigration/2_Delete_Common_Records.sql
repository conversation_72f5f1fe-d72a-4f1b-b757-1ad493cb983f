drop temporary table if exists tempDeleteOrphan;
create temporary table tempD<PERSON>te<PERSON><PERSON>han(
SELECT ts.Tenant_Stage_Id FROM Empirical_DataStage.Orphan_Tenants_2024_06_21 bt inner join Empirical_DataStage.Tenants_Stage ts 
on bt.VendorID=ts.VendorID  #where ts.BranchID is not null or ts.ConfirmedTenantID is not null
where ts.IsHidden=1
);

select count(*) from Empirical_DataStage.Tenants_Stage where Tenant_Stage_Id in (select * from tempDeleteOrphan) and (IsHidden=0 or IsHidden is null);


delete from Empirical_Prod.NationalIdentifiers where Tenant_Stage_ID in (select * from tempDeleteOrphan);
delete from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select * from tempDeleteOrphan);

