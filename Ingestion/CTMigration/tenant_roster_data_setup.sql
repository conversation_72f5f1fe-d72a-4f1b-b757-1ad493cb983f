select * from Empirical_DataStage.Tenant_Roster_2024_06_14;
desc Empirical_DataStage.Tenant_Roster_2024_06_14;

call Empirical_DataStage.Tenant_Roster_Data_Import_Bronze(240614100);

select * from Empirical_DataStage.Tenant_Roster_2024_06_14 where BatchID=240614101;
select * from Empirical_DataStage.Tenants_Stage where BatchID=240614101;

select count(*),BatchID,IsProcessed from Empirical_DataStage.Tenant_Roster_2024_06_14 group by Batch<PERSON>,IsProcessed;


select count(*),BatchID from Empirical_DataStage.Tenants_Stage where BatchID in (240614101,240614100) group by Batch<PERSON>;

select c.City<PERSON>ame,ts.* from Empirical_DataStage.Tenants_Stage ts 
inner join Empirical_Prod.Address a on ts.PropertyID=a.ParentID
inner join Empirical_Prod.City c on a.CityID=c.CityID
where a.ParentTableID=1 and a.Sequence=1 and a.IsActive=1 and ts.BatchID in (240614101,240614100);


update Empirical_DataStage.Tenants_Stage ts 
inner join Empirical_Prod.Address a on ts.PropertyID=a.ParentID
inner join Empirical_Prod.City c on a.CityID=c.CityID
set ts.City=c.CityName
where a.ParentTableID=1 and a.Sequence=1 and a.IsActive=1 and ts.BatchID in (240614101,240614100);


update Empirical_DataStage.Tenant_Roster_2024_06_14 ts 
inner join Empirical_Prod.Address a on ts.PropertyID=a.ParentID
inner join Empirical_Prod.City c on a.CityID=c.CityID
set ts.City=c.CityName
where a.ParentTableID=1 and a.Sequence=1 and a.IsActive=1;

UPDATE `Empirical_Prod`.`Providers` SET `DisplayText` = 'P6', `LabelColor` = '#C80036' WHERE (`ProviderID` = '40');


select * from Empirical_Prod.Providers;