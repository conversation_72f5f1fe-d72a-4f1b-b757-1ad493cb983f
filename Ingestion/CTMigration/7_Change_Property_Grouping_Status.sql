SELECT DISTINCT
    rct.PropertyID
FROM
    Empirical_DataStage.Raw_Matched_Tenants_2024_06_21 AS mt
        INNER JOIN
    Empirical_DataStage.Raw_Confirmed_Tenants_2024_06_21 AS rct ON rct.VendorID = mt.main_ID
WHERE
    mt.ProviderID = 40;
  
  
SELECT DISTINCT
    PropertyID
FROM
    Empirical_DataStage.Raw_Confirmed_Tenants_2024_06_21;
     
SELECT 
    *
FROM
    Empirical_Prod.Property
WHERE
    PropertyID IN (SELECT DISTINCT
            PropertyID
        FROM
            Empirical_DataStage.Raw_Confirmed_Tenants_2024_06_21);
     
     'TSATenantResearchStatus', 'enum(\'Grouping Completed\',\'Grouping InProgress\')', 'YES', '', NULL, ''

    desc Empirical_Prod.Property
    
    /**
   update  Empirical_Prod.Property
   set TSATenantResearchStatus='Grouping Completed'
   where PropertyID in (     select distinct PropertyID from  Empirical_DataStage.Raw_Confirmed_Tenants_2024_06_21 where IsProcessed=1) ;
   **/
     
     
      /**
   update  Empirical_Prod.Property
   set TSATenantResearchStatus='Grouping InProgress'
   where PropertyID in (   
   
   SELECT DISTINCT
    rct.PropertyID
FROM
    Empirical_DataStage.Raw_Matched_Tenants_2024_06_21 AS mt
        INNER JOIN
    Empirical_DataStage.Raw_Confirmed_Tenants_2024_06_21 AS rct ON rct.VendorID = mt.main_ID
WHERE
    mt.ProviderID = 40 and rct.IsProcessed=1
  
   )
   ;
   **/