drop temporary table if exists tempTS;
create temporary table tempTS(
select * from Empirical_DataStage.Tenants_Stage where (PropertyID in (
select ParentID from Empirical_Prod.Address where ZipCode in (3000, 3002, 3003, 3004, 3006, 3008, 3031, 3051, 3052, 3053, 3054)
 and ParentTableID=1 and IsActive=1 and Sequence=1) or PostalCode in (3000, 3002, 3003, 3004, 3006, 3008, 3031, 3051, 3052, 3053, 3054))
and (<PERSON><PERSON><PERSON><PERSON> is null or IsH<PERSON>den=0)
);
drop temporary table if exists migrateReocrds;
create temporary table migrateReocrds(
select * from tempTS where PropertyID is not null and Branch<PERSON> is not null
);

select count(*) , BranchID from migrateReocrds group by BranchID, ParentCompanyID;

select BranchID,ParentCompanyID, VendorID, TenantName, PropertyID, ModifiedBy, ModifiedDate 
 from migrateReocrds where BranchID is not null and  ParentCompanyID is not null;
 
 
 select ProviderID, BranchID,ParentCompanyID, VendorID, TenantName, PropertyID, ModifiedBy, ModifiedDate, CreatedDate 
 from migrateReocrds where BranchID is not null and  ParentCompanyID is not null group by VendorID;
 
 drop temporary table if exists migrateReocrdsDuplicatesBranch;
create temporary table migrateReocrdsDuplicatesBranch(
  select BranchID
 from migrateReocrds where BranchID is not null and 
 ParentCompanyID is not null group by BranchID  having count(*)>1
 );
 
 
 select ConfirmedTenantID, ProviderID, BranchID,ParentCompanyID, VendorID, TenantName, PropertyID, ModifiedBy, ModifiedDate, CreatedDate 
 from migrateReocrds where BranchID is not null and  ParentCompanyID is not null  and BranchID
 
 in ( select * from migrateReocrdsDuplicatesBranch);
 
 