Update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set BatchID=null,IsProcessed=0,Tenant_Stage_ID=null,ProviderID=14;

ALTER TABLE `Empirical_Tenants`.`ConfirmedTenants` 
<PERSON>ANGE COLUMN `ExtVendorID` `ExtVendorID` VARCHAR(200) NULL DEFAULT NULL ;
ALTER TABLE `Empirical_DataStage`.`Tenants_Stage` 
CHANGE COLUMN `VendorID` `VendorID` VARCHAR(200) NULL DEFAULT NULL ;
ALTER TABLE Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 
MODIFY COLUMN NationalID VARCHAR(500) NULL;



drop  temporary table if exists  Vendors;
create temporary table Vendors(
SELECT  VendorID FROM Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 where BatchID is null);

drop  temporary table if exists  duplicateVendors;
create temporary table duplicateVendors(
SELECT  VendorID FROM Vendors group by Vendor<PERSON> having count(*)>1);


select count(*),BatchID from Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 group by <PERSON>ch<PERSON>;


update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set State=59 where StateAbbr='VIC';

UPDATE Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 as a
SET a.NationalID = 
    CASE
        WHEN a.ACN IS NOT NULL AND a.ABN IS NOT NULL THEN CONCAT('ACN: ', a.ACN, ' ABN: ', a.ABN)
        WHEN a.ACN IS NOT NULL THEN CONCAT('ACN: ', a.ACN)
        WHEN a.ABN IS NOT NULL THEN CONCAT('ABN: ', a.ABN)
        ELSE NULL
    END;


update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set BatchID=24621300 where BatchID is null;


select count(*),BatchID,IsProcessed from Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 group by BatchID,IsProcessed;

drop temporary table if exists tempGold;
create temporary table tempGold(
SELECT ts.Tenant_Stage_Id,bt.VendorID,ts.IsHidden,ts.State as TSState,bt.State as BTState,ts.BranchID FROM 
Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 bt inner join Empirical_DataStage.Tenants_Stage ts 
on bt.VendorID=ts.VendorID
);

drop temporary table if exists tempCompany;
create temporary table tempCompany(
select tg.VendorID from Empirical_Prod.Company c inner join tempGold tg on c.CompanyID=tg.BranchID
where (c.IsHidden=0 or c.IsHidden is null)
);

select * from Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 where VendorID in (select VendorID from tempCompany);
update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set BatchID=20240621 where VendorID in (select VendorID from tempCompany);

drop temporary table if exists CTWithLeaseOrUpdatedbyrstDoesntHaveTSRecord;
create temporary table CTWithLeaseOrUpdatedbyrstDoesntHaveTSRecord(
Select st.PropertyID,ct.TenantName,ct.CompanyID,ct.ConfirmedTenantID from Empirical_Prod.SuiteTenant st 
inner join Empirical_Tenants.ConfirmedTenants ct on ct.ConfirmedTenantID=st.ConfirmedTenantID
);

drop temporary table if exists companyWithLeaseOrUpdatedbyrstDoesntHaveTSRecord;
create temporary table companyWithLeaseOrUpdatedbyrstDoesntHaveTSRecord(
select ct.* from Empirical_Prod.Company c inner join CTWithLeaseOrUpdatedbyrstDoesntHaveTSRecord ct on c.CompanyID=ct.CompanyID
where (c.IsHidden=0 or c.IsHidden is null)
);

drop temporary table if exists excludeTheseFromGold;
create temporary table excludeTheseFromGold(
select distinct rct.VendorID from Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 rct 
inner join companyWithLeaseOrUpdatedbyrstDoesntHaveTSRecord ct on rct.PropertyID=ct.PropertyID and rct.TenantName=ct.TenantName
);

select * from Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 where VendorID in (select VendorID from excludeTheseFromGold);
update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set BatchID=20240621 where VendorID in (select VendorID from excludeTheseFromGold);


update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set Tenant_Stage_ID=null;
update Empirical_DataIngestion.Raw_Matched_Tenants_2024_09_25 set Tenant_Stage_ID=null;

select * from Empirical_DataStage.Tenants_Stage where ProviderID=14;

select count(*),BatchID,IsProcessed from Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 group by BatchID,IsProcessed;

-- checking queries
drop  temporary table if exists  Vendors;
create temporary table Vendors(
SELECT  VendorID FROM Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 where BatchID=24621300);

drop  temporary table if exists  MatchedVendors;
create temporary table MatchedVendors(
SELECT  VendorID FROM Empirical_DataIngestion.Raw_Matched_Tenants_2024_09_25 where main_ID in (select * from Vendors));


drop  temporary table if exists  TSVendors;
create temporary table TSVendors(
SELECT  VendorID FROM Empirical_DataStage.Tenants_Stage where VendorID in (select * from Vendors));

drop  temporary table if exists  TSMatchedVendors;
create temporary table TSMatchedVendors(
SELECT  VendorID FROM Empirical_DataStage.Tenants_Stage where VendorID in (select * from MatchedVendors));

select * from MatchedVendors mv left join TSMatchedVendors tsmv on mv.VendorID=tsmv.VendorID where tsmv.VendorID is null;

drop  temporary table if exists  TSDuplicateVendors;
create temporary table TSDuplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.Tenants_Stage where VendorID in (select * from Vendors) group by VendorID having count(*)>1);

drop  temporary table if exists  TSMatchedDuplicateVendors;
create temporary table TSMatchedDuplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.Tenants_Stage where VendorID in (select * from MatchedVendors) group by VendorID having count(*)>1);

select count(*) from Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 where VendorID in (select VendorID from TSMatchedDuplicateVendors);
select count(*) from Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 where VendorID in (select VendorID from TSDuplicateVendors);

drop  temporary table if exists  TSDuplicateVendorsTobeDeleted;
create temporary table TSDuplicateVendorsTobeDeleted(
select * from Empirical_DataStage.Tenants_Stage where VendorID in (select * from TSDuplicateVendors)
);

drop  temporary table if exists  TSDuplicateVendorsTobeDeletedProvider14;
create temporary table TSDuplicateVendorsTobeDeletedProvider14(
select VendorID from TSDuplicateVendorsTobeDeleted group by BranchID,VendorID having count(*)>1
);

drop  temporary table if exists  VendorsTobeDeleted;
create temporary table VendorsTobeDeleted(
select VendorID,Tenant_Stage_Id from Empirical_DataStage.Tenants_Stage where VendorID in (select * from TSDuplicateVendorsTobeDeletedProvider14) and ProviderID=14
);

select * from Empirical_DataStage.Tenants_Stage where VendorID in (select * from TSDuplicateVendorsTobeDeletedProvider14) and ProviderID=14; 
delete from Empirical_Prod.NationalIdentifiers where Tenant_Stage_Id in (select Tenant_Stage_Id from VendorsTobeDeleted); 
delete from Empirical_DataStage.Tenants_Stage where Tenant_Stage_Id in (select Tenant_Stage_Id from VendorsTobeDeleted) and ProviderID=14; 

update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set BatchID=24621310 where VendorID in (select VendorID from TSDuplicateVendors);



DROP TEMPORARY TABLE IF EXISTS `Empirical_DataStage`.`TenantSourceVendor`;
create temporary table Empirical_DataStage.TenantSourceVendor (
	select Tenant_Stage_Id,VendorID,PropertyID,ProviderID from Empirical_DataStage.Tenants_Stage where VendorID in (select VendorID from Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25)
);
    
update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 as a
	inner join Empirical_DataStage.TenantSourceVendor as b on a.VendorID=b.VendorID and a.ProviderID=b.ProviderID
	set a.Tenant_Stage_ID=b.Tenant_Stage_Id;
    
DROP TEMPORARY TABLE IF EXISTS `Empirical_DataStage`.`TenantSourceVendor`;
create temporary table Empirical_DataStage.TenantSourceVendor (
	select Tenant_Stage_Id,VendorID,PropertyID,ProviderID from Empirical_DataStage.Tenants_Stage where VendorID in (select VendorID from Empirical_DataIngestion.Raw_Matched_Tenants_2024_09_25)
);
    
update Empirical_DataIngestion.Raw_Matched_Tenants_2024_09_25 as a
	inner join Empirical_DataStage.TenantSourceVendor as b on a.VendorID=b.VendorID and a.ProviderID=b.ProviderID
	set a.Tenant_Stage_ID=b.Tenant_Stage_Id;


update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set BatchID=24621301 where BatchID=24621300 limit 1000;
update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set BatchID=24621302 where BatchID=24621300 limit 1000;
update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set BatchID=24621303 where BatchID=24621300 limit 1000;
update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set BatchID=24621304 where BatchID=24621300 limit 1000;
update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set BatchID=24621305 where BatchID=24621300 limit 1000;
update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set BatchID=24621306 where BatchID=24621300 limit 1000;
update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set BatchID=24621307 where BatchID=24621300 limit 1000;


update Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 set BatchID=24621308 where BatchID=24621300 limit 1;
select * from Empirical_DataIngestion.Raw_Matched_Tenants_2024_09_25 where main_ID=758421663;

select count(*),BatchID,IsProcessed from Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 group by BatchID,IsProcessed;

call Empirical_DataStage.CRE_Tenant_Stage_Import_DataSet_V1('24621302','VIC','Gold');
SET SESSION group_concat_max_len = 1000000;
call Empirical_DataStage.CRE_Verified_Tenant_Import_V1(24621302,59,14,14,349,'VIC','Gold');

call Empirical_DataStage.CRE_Tenant_Stage_Import_DataSet_V1('24621303','VIC','Gold');
SET SESSION group_concat_max_len = 1000000;
call Empirical_DataStage.CRE_Verified_Tenant_Import_V1(24621303,59,14,14,349,'VIC','Gold');

call Empirical_DataStage.CRE_Tenant_Stage_Import_DataSet_V1('24621304','VIC','Gold');
SET SESSION group_concat_max_len = 1000000;
call Empirical_DataStage.CRE_Verified_Tenant_Import_V1(24621304,59,14,14,349,'VIC','Gold');

call Empirical_DataStage.CRE_Tenant_Stage_Import_DataSet_V1('24621305','VIC','Gold');
SET SESSION group_concat_max_len = 1000000;
call Empirical_DataStage.CRE_Verified_Tenant_Import_V1(24621305,59,14,14,349,'VIC','Gold');

call Empirical_DataStage.CRE_Tenant_Stage_Import_DataSet_V1('24621306','VIC','Gold');
SET SESSION group_concat_max_len = 1000000;
call Empirical_DataStage.CRE_Verified_Tenant_Import_V1(24621306,59,14,14,349,'VIC','Gold');

call Empirical_DataStage.CRE_Tenant_Stage_Import_DataSet_V1('24621307','VIC','Gold');
SET SESSION group_concat_max_len = 1000000;
call Empirical_DataStage.CRE_Verified_Tenant_Import_V1(24621307,59,14,14,349,'VIC','Gold');


select ProviderID,VendorID,TenantName,PropertyID from Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 where BatchID=24621300;
select ProviderID,VendorID,TenantName,PropertyID from Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25 where BatchID=24621300;

select * from Empirical_DataIngestion.Raw_Matched_Tenants_2024_09_25 where main_ID in (select VendorID from Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_06_21 where BatchID=24619301);

