Update Empirical_DataStage.Orphan_Tenants_2024_06_21 set BatchID=null,IsProcessed=0,Tenant_Stage_ID=null;

drop  temporary table if exists  OrphanVendors;
create temporary table OrphanVendors(
SELECT  VendorID FROM Empirical_DataStage.Orphan_Tenants_2024_06_21 where Batch<PERSON> is null);

drop  temporary table if exists  OrphanduplicateVendors;
create temporary table OrphanduplicateVendors(
SELECT  VendorID FROM OrphanVendors group by VendorID having count(*)>1);


select count(*),BatchID from Empirical_DataStage.Orphan_Tenants_2024_06_21 group by BatchID;


update Empirical_DataStage.Orphan_Tenants_2024_06_21 set State=59 where StateAbbr='VIC';

UPDATE Empirical_DataStage.Orphan_Tenants_2024_06_21 as a
SET a.NationalID = 
    CASE
        WHEN a.ACN IS NOT NULL AND a.ABN IS NOT NULL THEN CONCAT('ACN: ', a.ACN, ' ABN: ', a.ABN)
        WHEN a.ACN IS NOT NULL THEN CONCAT('ACN: ', a.ACN)
        WHEN a.ABN IS NOT NULL THEN CONCAT('ABN: ', a.ABN)
        ELSE NULL
    END;


update Empirical_DataStage.Orphan_Tenants_2024_06_21 set BatchID=24621100 where BatchID is null;


select count(*),BatchID,IsProcessed from Empirical_DataStage.Orphan_Tenants_2024_06_21 group by BatchID,IsProcessed;


drop temporary table if exists tempOrphan;
create temporary table tempOrphan(
SELECT ts.Tenant_Stage_Id,bt.VendorID,ts.IsHidden,ts.State as TSState,bt.State as BTState FROM Empirical_DataStage.Orphan_Tenants_2024_06_21 bt inner join Empirical_DataStage.Tenants_Stage ts 
on bt.VendorID=ts.VendorID
);

update Empirical_DataStage.Orphan_Tenants_2024_06_21 set BatchID=20240621 where VendorID in (select VendorID from tempOrphan);

call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(24621100);

select count(*),PostalCode,IsProcessed from Empirical_DataStage.Orphan_Tenants_2024_06_21 group by PostalCode,IsProcessed;