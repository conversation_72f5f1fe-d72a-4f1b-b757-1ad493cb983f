drop temporary table if exists comProperties;
create temporary table comProperties(
select ParentID from Empirical_Prod.Address where ZipCode in (3000, 3002, 3003, 3004, 3006, 3008, 3031, 3051, 3052, 3053, 3054)
 and ParentTableID=1 and IsActive=1 and Sequence=1
 );
 
drop temporary table if exists comPropertiesTSRecords;
create temporary table comPropertiesTSRecords(
select * from Empirical_DataStage.Tenants_Stage where PropertyID in 
(select * from comProperties)
);

select count(*) from comPropertiesTSRecords where BranchID is not null;

select CPT.*,NI.ACN,NI.ABN from comPropertiesTSRecords as CPT left join Empirical_Prod.NationalIdentifiers as NI on CPT.Tenant_Stage_Id = NI.Tenant_Stage_ID  where BranchID is not null;

drop temporary table if exists comPropertiesCTs;
create temporary table comPropertiesCTs(
select  PropertyID, ConfirmedTenantID from Empirical_Prod.SuiteTenant 
where PropertyID in (select * from comProperties) and IsActive=1
);

drop temporary table if exists comPropertiesCTsRecords;
create temporary table comPropertiesCTsRecords(
select ctp.PropertyID, ct.* from Empirical_Tenants.ConfirmedTenants as ct
inner join comPropertiesCTs as ctp on ctp.ConfirmedTenantID =ct.ConfirmedTenantID
where ct.ProviderID=1
);


select ct.*,ni.ABN,ni.ACN from  comPropertiesCTsRecords ct 
inner join Empirical_Prod.Company  as c on c.CompanyID= ct.CompanyID 
left join Empirical_Prod.NationalIdentifiers ni on ct.ConfirmedTenantID=ni.ConfirmedTenantID
where( c.IsHidden is NULL or c.IsHidden=0);