
drop temporary table if exists comProperties;
create temporary table comProperties(
select ParentID from Empirical_Prod.Address where ZipCode in (3000, 3002, 3003, 3004, 3006, 3008, 3031, 3051, 3052, 3053, 3054)
 and ParentTableID=1 and IsActive=1 and Sequence=1
 );
 
drop temporary table if exists comPropertiesTSRecords;
create temporary table comPropertiesTSRecords(
select * from Empirical_DataStage.Tenants_Stage where PropertyID in 
(select * from comProperties) and PropertyID is not null
);

select count(*),IsHidden from comPropertiesTSRecords group by IsHidden;

drop temporary table if exists comOrphanTSRecords;
create temporary table comOrphanTSRecords(
select * from Empirical_DataStage.Tenants_Stage where PostalCode in (3000, 3002, 3003, 3004, 3006, 3008, 3031, 3051, 3052, 3053, 3054) and PropertyID is null
);

select count(*),IsHidden from comOrphanTSRecords  group by IsHidden;

drop temporary table if exists tempFinalTS;
create temporary table tempFinalTS(
select * from comPropertiesTSRecords
);

drop temporary table if exists comPropertiesCTs;
create temporary table comPropertiesCTs(
select  PropertyID, ConfirmedTenantID from Empirical_Prod.SuiteTenant 
where PropertyID in (select * from comProperties) and IsActive=1
);

drop temporary table if exists comPropertiesCTsRecords;
create temporary table comPropertiesCTsRecords(
select ctp.PropertyID, ct.CompanyID,ct.ConfirmedTenantID,ct.TenantName from Empirical_Tenants.ConfirmedTenants as ct
inner join comPropertiesCTs as ctp on ctp.ConfirmedTenantID =ct.ConfirmedTenantID
#where ct.ProviderID=1
);

drop temporary table if exists comPropertiesCTsRecordsWithCompany;
create temporary table comPropertiesCTsRecordsWithCompany(
select ct.*,c.IsHidden,c.ModifiedDate as CModifiedDate,c.ModifiedBy as CModifiedBy from comPropertiesCTsRecords ct 
inner join Empirical_Prod.Company c on ct.CompanyID=c.CompanyID where c.IsHidden=0 or c.IsHidden is null
);


/*select count(*),IsHidden from Empirical_Prod.Company where CompanyID in 
(select CompanyID from Empirical_Tenants.ConfirmedTenants where ConfirmedTenantID in 
(select ConfirmedTenantID from tempLeaseCT)) group by IsHidden;*/

drop temporary table if exists tempFinalCTExcludeLease;
create temporary table tempFinalCTExcludeLease(
select a.* from comPropertiesCTsRecordsWithCompany a 
left join tempLeaseCT c on c.ConfirmedTenantID=a.ConfirmedTenantID 
where c.ConfirmedTenantID is null
);

drop temporary table if exists tempMergedAndECREConfirmedTenantsCOM;
create temporary table tempMergedAndECREConfirmedTenantsCOM(
select * from Empirical_Tenants.ConfirmedTenants where CompanyID in (select CompanyID from comPropertiesCTsRecordsWithCompany)
);

drop temporary table if exists ConfirmedTenantsUpdatedByRST;
create temporary table ConfirmedTenantsUpdatedByRST(
select distinct CompanyID from tempMergedAndECREConfirmedTenantsCOM where ((ModifiedBy!=22 and ModifiedBy is not null) and 
(ModifiedDate>'2023-10-01' or CreatedDate>'2023-10-01'))
);

select count(distinct rst.CompanyID) from ConfirmedTenantsUpdatedByRST rst inner join tempLeaseCompany lc on lc.CompanyID=rst.CompanyID;

drop temporary table if exists tempFinalCT;
create temporary table tempFinalCT(
select distinct a.CompanyID from tempFinalCTExcludeLease a 
left join ConfirmedTenantsUpdatedByRST b on a.CompanyID=b.CompanyID
where b.CompanyID is null
);

-- delete from Empirical_Prod.NationalIdentifiers where Tenant_Stage_ID in (select Tenant_Stage_ID from comOrphanTSRecords);
-- delete from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select Tenant_Stage_ID from comOrphanTSRecords) and PropertyID is null;
select * from Empirical_Prod.HideReasons;

select count(*) from Empirical_Prod.Company 
WHERE
    CompanyID in (SELECT CompanyID FROM tempFinalCT);
    
    
    
select count(*) from Empirical_DataStage.Tenants_Stage 
WHERE
    Tenant_Stage_Id in (SELECT Tenant_Stage_Id FROM tempFinalTS);
     
   /**
 UPDATE Empirical_Prod.Company 
SET 
    HidedBy = 22,
    HidedDate = CURRENT_TIMESTAMP(),
    HideReasonID = 9,
    HideReasonComments = 'Hiding old companies in city of Melbourne while migrating',
    ModifiedBy = 22,
    IsHidden = 1,
    ModifiedDate = CURRENT_TIMESTAMP()
WHERE
    CompanyID in (SELECT CompanyID FROM tempFinalCT);
    
    
    
    UPDATE Empirical_DataStage.Tenants_Stage 
SET 
    HidedBy = 22,
    HidedDate = CURRENT_TIMESTAMP(),
    HideReasonID = 9,
    HideReasonComments = 'Hiding old companies in city of Melbourne while migrating',
    ModifiedBy = 22,
    IsHidden = 1,
    ModifiedDate = CURRENT_TIMESTAMP()
WHERE
    Tenant_Stage_Id in (SELECT Tenant_Stage_Id FROM tempFinalTS);
    **/

drop temporary table if exists CompaniesForLeaseAndTenantsUpdatedbyResearcher;
create temporary table CompaniesForLeaseAndTenantsUpdatedbyResearcher(
select * from tempLeaseCompany 
union 
select * from ConfirmedTenantsUpdatedByRST
);

drop temporary table if exists ConfirmationsForLeaseAndTenantsUpdatedbyResearcher;
create temporary table ConfirmationsForLeaseAndTenantsUpdatedbyResearcher(
select * from Empirical_DataStage.Tenants_Stage where BranchID in (select * from CompaniesForLeaseAndTenantsUpdatedbyResearcher)
);
/*
UPDATE Empirical_DataStage.Tenants_Stage 
SET 
    HidedBy = NULL,
    HidedDate = NULL,
    HideReasonID = NULL,
    HideReasonComments = 'Unhiding confirmations for active lease and tenants updated by reasearchers',
    ModifiedBy = 22,
    IsHidden = NULL,
    ModifiedDate = CURRENT_TIMESTAMP()
WHERE
    Tenant_Stage_Id in (SELECT Tenant_Stage_Id FROM ConfirmationsForLeaseAndTenantsUpdatedbyResearcher);
*/