CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Tenant_Stage_Import_Bronze_DataSet`(IN P_BatchID int)
BEGIN

	-- illion records existed in the tenant ingested data
		drop temporary table if exists temp_Raw_Orphan_Tenants;
		create temporary table temp_Raw_Orphan_Tenants(
			select * from Empirical_DataStage.Bronze_Tenants_2024_06_21 
            where BatchID=P_BatchID  and (IsProcessed is null or IsProcessed =0)
		);
		-- Create an index on the 'VendorID' column
       CREATE INDEX idx_VendorID ON temp_Raw_Orphan_Tenants (VendorID);
-- select * from temp_Raw_Orphan_Tenants;
		select count(*) into @ExistingCount from temp_Raw_Orphan_Tenants where Tenant_Stage_Id is not null;
        
        if @ExistingCount >0 then
           -- Update Tenant Stage records with Tenant Stage Illion NSW (Only Al-Tenant Automation Providers records)
		update Empirical_DataStage.Tenants_Stage as TS
			inner join temp_Raw_Orphan_Tenants as t on TS.VendorID=t.VendorID and TS.ProviderID=t.ProviderID and TS.Tenant_Stage_Id=t.Tenant_Stage_Id
			set TS.TenantName=COALESCE(t.TenantName,TS.TenantName),TS.Address1=COALESCE(t.Address1,TS.Address1),TS.Address2=COALESCE(t.Address2,TS.Address2),
            TS.City=COALESCE(t.City,TS.City),TS.State=COALESCE(t.State,TS.State),TS.StateAbbr=COALESCE(t.StateAbbr,TS.StateAbbr),
			TS.CountryCode=COALESCE(t.CountryCode,TS.CountryCode),TS.PostalCode=COALESCE(t.PostalCode,TS.PostalCode),TS.NationalID=COALESCE(t.NationalID,TS.NationalID),TS.OfficePhone=COALESCE(t.OfficePhone,TS.OfficePhone),TS.Fax=COALESCE(t.Fax,TS.Fax),
			TS.CEOName=COALESCE(t.CEOName,TS.CEOName),TS.CEOTitle=COALESCE(t.CEOTitle,TS.CEOTitle),TS.LineOfBusiness=COALESCE(t.LineOfBusiness,TS.LineOfBusiness),TS.SICCode=COALESCE(t.SICCode,TS.SICCode),TS.Revenue=COALESCE(t.Revenue,TS.Revenue),
			TS.EmployeesAtLocation=COALESCE(t.EmployeesAtLocation,TS.EmployeesAtLocation),TS.EmployeeCount=COALESCE(t.EmployeeCount,TS.EmployeeCount),TS.LegalStatus=COALESCE(t.LegalStatus,TS.LegalStatus),-- TS.StatusCode=COALESCE(t.StatusCode,TS.StatusCode),
			TS.SubsidiaryCode=COALESCE(t.SubsidiaryCode,TS.SubsidiaryCode),TS.Latitude=COALESCE(t.Latitude,TS.Latitude),TS.Longitude=COALESCE(t.Longitude,TS.Longitude),TS.NAICSCode=COALESCE(t.NAICSCode,TS.NAICSCode),TS.NACECode=COALESCE(t.NACECode,TS.NACECode),
			TS.Email=COALESCE(t.Email,TS.Email),TS.WebsiteURL=COALESCE(t.WebsiteURL,TS.WebsiteURL),TS.PropertyID=COALESCE(t.PropertyID,TS.PropertyID),
			TS.ASICEntityStatus=COALESCE(t.ASICEntityStatus,TS.ASICEntityStatus),TS.ASICEntityType=COALESCE(t.ASICEntityType,TS.ASICEntityType),TS.ASICEntityClass=COALESCE(t.ASICEntityClass,TS.ASICEntityClass),
			TS.ABNStatus=COALESCE(t.ABNStatus,TS.ABNStatus),TS.ABN_StatusFromDate=COALESCE(t.ABN_StatusFromDate,TS.ABN_StatusFromDate),TS.GST_Status=COALESCE(t.GST_Status,TS.GST_Status),TS.GST_StatusFromDate=COALESCE(t.GST_StatusFromDate,TS.GST_StatusFromDate)
			,TS.RegistrationOrIncorporationDate=COALESCE(t.RegistrationOrIncorporationDate,TS.RegistrationOrIncorporationDate),TS.EntityAge=COALESCE(t.EntityAge,TS.EntityAge),
			TS.EmployeeIndicator=COALESCE(t.EmployeeIndicator,TS.EmployeeIndicator),TS.RevenueIndicator=COALESCE(t.RevenueIndicator,TS.RevenueIndicator),TS.HQ_ID=COALESCE(t.HQ_ID,TS.HQ_ID),TS.HQ_CompanyName=COALESCE(t.HQ_CompanyName,TS.HQ_CompanyName),
			TS.NumberofMembersinHierarchy=COALESCE(t.NumberofMembersinHierarchy,TS.NumberofMembersinHierarchy),TS.ImmediateParentDUNS=COALESCE(t.ImmediateParentDUNS,TS.ImmediateParentDUNS),
			TS.ImmediateParentName=COALESCE(t.ImmediateParentName,TS.ImmediateParentName),TS.ImmediateParentCountry=COALESCE(t.ImmediateParentCountry,TS.ImmediateParentCountry),
			TS.DomesticParentDUNS=COALESCE(t.DomesticParentDUNS,TS.DomesticParentDUNS),TS.DomesticParentName=COALESCE(t.DomesticParentName,TS.DomesticParentName),TS.DomesticParentCountry=COALESCE(t.DomesticParentCountry,TS.DomesticParentCountry),
			TS.GlobalUltimateParentDUNS=COALESCE(t.GlobalUltimateParentDUNS,TS.GlobalUltimateParentDUNS),	TS.GlobalUltimateParentName=COALESCE(t.GlobalUltimateParentName,TS.GlobalUltimateParentName),
			TS.GlobalUltimateParentCountry=COALESCE(t.GlobalUltimateParentCountry,TS.GlobalUltimateParentCountry),TS.PrimarySICDesc=COALESCE(t.PrimarySICDesc,TS.PrimarySICDesc),TS.PrimarySIC3Digit=COALESCE(t.PrimarySIC3Digit,TS.PrimarySIC3Digit),
			TS.PrimarySIC3DigitDesc=COALESCE(t.PrimarySIC3DigitDesc,TS.PrimarySIC3DigitDesc),TS.PrimarySIC2Digit=COALESCE(t.PrimarySIC2Digit,TS.PrimarySIC2Digit),TS.PrimarySIC2DigitDesc=COALESCE(t.PrimarySIC2DigitDesc,TS.PrimarySIC2DigitDesc),
			TS.PrimarySICDivision=COALESCE(t.PrimarySICDivision,TS.PrimarySICDivision),TS.PrimarySICDivisionDesc=COALESCE(t.PrimarySICDivisionDesc,TS.PrimarySICDivisionDesc),#TS.SubHideReasonID=COALESCE(t.SubHideReasonID,TS.SubHideReasonID),
			TS.IsHidden=NULL,TS.HidedBy=NULL,TS.HidedDate=NULL,TS.HideReasonID=NULL,TS.HideReasonComments=NULL,
			TS.ANZSICCode=COALESCE(t.ANZSICCode,TS.ANZSICCode),TS.ModifiedDate=current_time(),TS.ModifiedBy=22;
		end if;
        
        select count(*) into @NewCount from temp_Raw_Orphan_Tenants where Tenant_Stage_Id is null;
        
        if  @NewCount>0 then
        -- Insert New records into Tenants_Stage 
		drop temporary table if exists tempTenantStage2;
		create temporary table tempTenantStage2 (
			select * from temp_Raw_Orphan_Tenants a where Tenant_Stage_ID is null
		);              

		insert into Empirical_DataStage.Tenants_Stage (
			VendorID,TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,
            #StatusCode,
            SubsidiaryCode,PropertyID,Latitude,Longitude,#ParentCompanyID,BranchID,BatchID,
            BatchID,ProviderID,
            NAICSCode,NACECode,
            Email,WebsiteURL,#ModifiedBy,IsHidden,IsDeleted,HidedBy,HidedDate,HideReasonID,HideReasonComments,
			ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,
            #SubHideReasonID,
            ANZSICCode,CreatedDate
		)
        select VendorID,TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,
          #  StatusCode,
            SubsidiaryCode,PropertyID,Latitude,Longitude,#ParentCompanyID,BranchID,BatchID,
            BatchID,ProviderID,
            NAICSCode,NACECode,
            Email,WebsiteURL,#ModifiedBy,IsHidden,IsDeleted,HidedBy,HidedDate,HideReasonID,HideReasonComments,
			ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,
            #SubHideReasonID,
            ANZSICCode,now() as CreatedDate from tempTenantStage2;
            drop temporary table if exists tempTenantStage2;
		end if;
        /*
		select * from Empirical_DataStage.Bronze_Tenants_2024_06_21 as a
		inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=a.ProviderID and a.StateAbbr=b.StateAbbr
		where a.BatchID=P_BatchID ;*/
		update Empirical_DataStage.Bronze_Tenants_2024_06_21 as a
		inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=a.ProviderID# and a.StateAbbr=b.StateAbbr
		set a.Tenant_Stage_ID=b.Tenant_Stage_ID,a.IsProcessed=1 where a.BatchID=P_BatchID ;
        
       /*  update Empirical_Prod.NationalIdentifiers as ni
         inner join  Empirical_DataStage.Orphan_Tenants_2024_04_23 as t on ni.Tenant_Stage_ID=t.Tenant_Stage_ID
         set ni.ABN=t.ABN,ni.ACN=t.ACN,ni.Tenant_Stage_ID=t.Tenant_Stage_ID       
        where t.BatchID in (select * from BatchIDs_T) and t.IsProcessed=1 and t.Tenant_Stage_ID is not null;
		*/	

		drop temporary table if exists NationalIdentifiersExists;
        create temporary table NationalIdentifiersExists(
        select ABN,ACN,Tenant_Stage_ID from Empirical_DataStage.Bronze_Tenants_2024_06_21 
        where BatchID=P_BatchID and IsProcessed=1 and Tenant_Stage_ID is not null and (ABN is not null or ACN is not null)
        and Tenant_Stage_ID in (Select Tenant_Stage_ID from Empirical_Prod.NationalIdentifiers)
        );
        -- select * from NationalIdentifiersExists;
        
        update Empirical_Prod.NationalIdentifiers ni inner join NationalIdentifiersExists nie on ni.Tenant_Stage_ID=nie.Tenant_Stage_ID
        set ni.ABN=nie.ABN,ni.ACN=nie.ACN;
        
        /*select ABN,ACN,Tenant_Stage_ID from Empirical_DataStage.Bronze_Tenants_2024_06_21 
        where BatchID=P_BatchID and IsProcessed=1 and Tenant_Stage_ID is not null and (ABN is not null or ACN is not null)
        and Tenant_Stage_ID not in (select Tenant_Stage_ID from NationalIdentifiersExists);*/

       insert into Empirical_Prod.NationalIdentifiers (ABN,ACN,Tenant_Stage_ID) 
        select ABN,ACN,Tenant_Stage_ID from Empirical_DataStage.Bronze_Tenants_2024_06_21 
        where BatchID=P_BatchID and IsProcessed=1 and Tenant_Stage_ID is not null and (ABN is not null or ACN is not null)
        and Tenant_Stage_ID not in (select Tenant_Stage_ID from NationalIdentifiersExists);
			

			
END