CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `Update_Merged_Records_1`(
IN P_BatchID INT,IN P_StateID INT,IN P_CountryID INT,IN P_ProviderID INT,IN P_MetroID INT, IN P_Market Varchar(100), IN P_Bucket varchar(100))
BEGIN
	declare L_NewConfirmedTenantID int;
   # DECLARE foreign_key_error BOOLEAN DEFAULT FALSE;
   # DECLARE CONTINUE HANDLER FOR SQLSTATE '23000' SET foreign_key_error = TRUE;
   
	DECLARE foreign_key_error BOOLEAN DEFAULT FALSE;
    DECLARE error_message TEXT DEFAULT '';
    DECLARE error_code CHAR(5) DEFAULT '';
    
    -- Handler for foreign key errors
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION 
    BEGIN
        GET DIAGNOSTICS CONDITION 1
        error_message = MESSAGE_TEXT,
        error_code = RETURNED_SQLSTATE;
        select error_message,error_code;
        IF error_code THEN
            SET foreign_key_error = TRUE;
        END IF;
    END;

    set L_NewConfirmedTenantID=null;

		DROP TEMPORARY TABLE IF EXISTS tempTenantStageALA;
        CREATE TEMPORARY TABLE tempTenantStageALA
        (
				SELECT 
		`Tenant_Stage_Id`,
		#DUNS,
		`TenantNAME` as CompanyName,
		`ADDRESS1` as Address1, 
		`ADDRESS2` as Address2,
        14 as CountryID,
		/*`CITY` AS City,
		C.CityID,
		#`STATEABBR`,
		S.StateID,
		
		`PostalCode` as Zipcode,*/
		#concat(trim(LEADING '0' from `CountryCode`),OfficePhone) as OfficePhone,
        CASE 
			WHEN OfficePhone LIKE '1%' THEN OfficePhone
			WHEN OfficePhone LIKE '0%' THEN CONCAT('61', SUBSTRING(OfficePhone, 2))
			WHEN OfficePhone REGEXP '^[2-3847-8]' THEN CONCAT('61', OfficePhone)
            else OfficePhone
		END as OfficePhone,
		CASE WHEN `FAX` ='' THEN '' ELSE FAX END as Fax,
		`SICCode` as SICCode,
		P_MetroID as MetroID,
		1 as BranchStatus,
		0 as IsMember,
        Email,
        Latitude,
        Longitude,
        PropertyID,
        VendorID,
        CEOName,
        CEOTitle, 
        LineOfBusiness,
        EmployeesAtLocation,
        null as NAICSCode,
        null as NACECode,
        ANZSICCode,
        SubsidiaryCode,
        null as StatusCode,
        Revenue,
        RevenueIndicator,
        RegistrationOrIncorporationDate,
        PrimarySIC2DigitDesc,
        PrimarySIC3DigitDesc,
        PrimarySICDivisionDesc,
        WebsiteURL,
		EmployeeCount,
        NationalID
	FROM
		Raw_Confirmed_Tenants_2024_06_10 ST
	WHERE
		BatchID=P_BatchID
        -- and (IsProcessed is null or IsProcessed =0)
		-- Tenant_Stage_Id in (select Tenant_Stage_Id from tempVendors)
        );
        
        CREATE INDEX idx_Tenant_Stage_Id_ALA ON tempTenantStageALA (Tenant_Stage_Id);

		drop temporary table if exists tempTenantStage;
		create temporary table tempTenantStage(
			select * from Empirical_DataStage.Tenants_Stage where Tenant_Stage_Id in (select Tenant_Stage_Id from tempTenantStageALA)
		);
		
       SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED; -- Set isolation level
        select min(Tenant_Stage_Id) into @minTenantStageID from tempTenantStageALA;
		SET autocommit = 0;
        while @minTenantStageID is not null
        do
			-- Start the outer transaction
			START TRANSACTION;

			set @CRE_BranchID=-1, @CRE_BranchID1=null, @ExistingPropertyID=null, @MovingToOtherProperty=0, @Source_Tenant_Stage_ID=null;

			select distinct VendorID,BranchID,PropertyID,ConfirmedTenantID into @ALA_VendorID,@CRE_BranchID, @Incoming_PropertyID,@ConfirmedTenantID  from tempTenantStage where Tenant_Stage_ID=@minTenantStageID;

            Select 
				 CompanyName,
				 #DUNS,
				 Address1,
				 Address2,
				 #CityID ,StateID,CountryID,Zipcode,
                 OfficePhone,Fax,SICCode,MetroID,BranchStatus,IsMember,Latitude,Longitude,22,  PropertyID,Tenant_Stage_Id,Email,
                 CEOName,
				CEOTitle, 
				LineOfBusiness,
				EmployeesAtLocation,
				NAICSCode,
				NACECode,
				ANZSICCode,
				SubsidiaryCode,
				StatusCode,
                Revenue,
				RevenueIndicator,
				RegistrationOrIncorporationDate,
				PrimarySIC2DigitDesc,
				PrimarySIC3DigitDesc,
				PrimarySICDivisionDesc,
				WebsiteURL,
				EmployeeCount  ,
                NationalID
				 INTO @CompanyName,@Address1,@Address2,
                 #@CityID,@StateID,@CountryID,@Zipcode,
                 @OfficePhone,@Fax,
				 @SICCode,@MetroID,@BranchStatus,@IsMember,@Latitude,@Longitude,@EntityID ,@PropertyID,@Tenant_Stage_Id,@Email,
                 @CEOName,
				@CEOTitle, 
				@LineOfBusiness,
				@EmployeesAtLocation,
				@NAICSCode,
				@NACECode,
				@ANZSICCode,
				@SubsidiaryCode,
				@StatusCode,
                @Revenue,
				@RevenueIndicator,
				@RegistrationOrIncorporationDate,
				@PrimarySIC2DigitDesc,
				@PrimarySIC3DigitDesc,
				@PrimarySICDivisionDesc,
				@WebsiteURL,
				@EmployeeCount ,
                @NationalID
				 From tempTenantStageALA where Tenant_Stage_Id=@minTenantStageID;  
				select AddressText,CityID,StateID,ZipCode,CountyID,CountryID into @Address1,@CityID,@StateID,@ZipCode,@CountyID,@CountryID from Empirical_Prod.Address where ParentID=@PropertyID and ParentTableID=1 and Sequence=1;

                if @CRE_BranchID !=-1 then
                select Address2 into @Address2 from Empirical_Prod.Address where ParentID=@CRE_BranchID and ParentTableID=6 and Sequence=1;
				end if;
              		CALL `Update_Merged_Records_2`(
						@ConfirmedTenantID , -- NA
						@CompanyName, -- NA
						@CompanyName,  -- NA
						null, -- Alternate Company Name
						@Address1, 
						@Address2, 
						null,  -- Address Street Number
						null,-- Address Street Name
						@CityID,
						@StateID, 
						@ZipCode, 
						@CountyID, 
						@CountryID, 
						null, -- Address Text
						@ALA_VendorID, -- External Vendor ID
					   1,  -- Providier ID
                       @MetroID,
					   @EntityID, -- Login User ID
                       @OfficePhone,
                       @CRE_BranchID,
                       @Email,
                       @WebsiteURL,
                       @Fax,
                       null, -- FloorNumber
                       @NationalID,
                       @EmployeeCount,
                       @Revenue,
                        @ABN,
                        @ACN ,
                       @ANZSICCode,-- P_ANZSIC
                       @CEOName,
						@CEOTitle,
						@LineOfBusiness,
						@SICCode,
						@EmployeesAtLocation,
						@LegalStatus,
						@StatusCode,
						@SubsidiaryCode,
						@NAICSCode,
						@NACECode,
						@PrimarySICDivisionDesc,
						@PrimarySIC2DigitDesc,
						@PrimarySIC3DigitDesc,
						@RegistrationOrIncorporationDate,
						@RevenueIndicator,
						@minTenantStageID,
                        @PropertyID,
                        @EntityID,
						@MovingToOtherProperty,
						P_Market,
                        P_Bucket,
					   L_NewConfirmedTenantID 
                   );

			UPDATE Raw_Confirmed_Tenants_2024_06_10 SET IsProcessed=1 where VendorID=@ALA_VendorID;

			select min(Tenant_Stage_Id) into @minTenantStageID from tempTenantStageALA
			WHERE Tenant_Stage_Id > @minTenantStageID
			ORDER BY Tenant_Stage_Id;  
			-- Enable automatic commit (optional, depending on your requirements)
			 IF foreign_key_error THEN
        -- Handle the foreign key constraint violation
			ROLLBACK;
             SELECT CONCAT('Error: ', error_message) AS Error_Detail;
			#SELECT 'Error 1452: Cannot add or update a child row. Foreign key constraint fails.' AS Result;
		ELSE
			-- Commit changes if no error
			COMMIT;
		-- 	SELECT 'Changes committed successfully.' AS Result;
		END IF;
        end while;
        	SET autocommit = 1;
END