CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `Tenant_Roster_Data_Import_Bronze`(IN P_BatchID int)
BEGIN

	-- illion records existed in the tenant ingested data
		drop temporary table if exists temp_Raw_Bronze_Tenants;
		create temporary table temp_Raw_Bronze_Tenants(
			select * from Empirical_DataStage.Tenant_Roster_2024_06_14 
            where BatchID=P_BatchID  and (IsProcessed is null or IsProcessed =0)
		);
		-- Create an index on the 'VendorID' column
       CREATE INDEX idx_VendorID ON temp_Raw_Bronze_Tenants (VendorID);
        
        select count(*) into @NewCount from temp_Raw_Bronze_Tenants where Tenant_Stage_Id is null;
        if  @NewCount>0 then
        -- Insert New records into Tenants_Stage 
		drop temporary table if exists tempTenantStage2;
		create temporary table tempTenantStage2 (
			select * from temp_Raw_Bronze_Tenants a where Tenant_Stage_ID is null
		);              

		insert into Empirical_DataStage.Tenants_Stage (
			VendorID,TenantName,State,StateAbbr,CountryCode,PropertyID,
            BatchID,ProviderID,CreatedDate
		)
        select VendorID,TenantName,State,StateAbbr,CountryID,PropertyID,
            BatchID,ProviderID,now() as CreatedDate from tempTenantStage2;
            drop temporary table if exists tempTenantStage2;
		end if;
        
		update Empirical_DataStage.Tenant_Roster_2024_06_14 as a
		inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=a.ProviderID
		set a.Tenant_Stage_ID=b.Tenant_Stage_ID,a.IsProcessed=1 where a.BatchID=P_BatchID ;

			
END