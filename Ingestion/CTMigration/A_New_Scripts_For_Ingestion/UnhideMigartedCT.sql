drop temporary table if exists IngestedCT;
 drop temporary table if exists IngestedCT;
 create temporary table IngestedCT(
select
ts.Tenant_Stage_Id,
 ts.PropertyID, ts.TenantName, ts.<PERSON><PERSON>, c.<PERSON>, c.<PERSON>ded<PERSON><PERSON>, c.HidedDate
from Empirical_DataStage.Tenants_Stage as  ts
inner   join Empirical_Prod.Company  as c on  ts.BranchID=c.CompanyID
where ts.BatchID in(
2467901,24679012, 2467903,2467904,2467905,2467906,2467907,2467908,2467909,
 2467910, 2467911, 2467912, 2467913,2467914,2467915) and ts.IsProcessed=1);
 

 select * from IngestedCT where IsHidden=1;


-- select * from Empirical_Prod.Company;
/**
UPDATE Empirical_Prod.Company 
SET 
   ModifiedBy=22,
   ModifiedDate= CURRENT_TIMESTAMP(),
    IsHidden = NULL,
    HidedBy = NULL,
    HidedDate = NULL,
    HideReasonID = NULL,
    SubHideReasonID = NULL,
    HideReasonComments = 'Beta to UAT Stacking data migrated.'
WHERE
    CompanyID IN (select BranchID from IngestedCT where IsHidden=1 );

**/



