CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Import_Branch_Save_Zessta_V1`(
IN P_CompanyID int(11),
IN P_ParentCompanyID int(11),
IN P_ConfirmedTenantID int(11),
IN P_CompanyName varchar(200),
IN P_Website varchar(225),
IN P_CompanyTypeID int(11),
IN P_IsNationalBrokerageCompany tinyint(1),
IN p_IsPublicCompany tinyint(1),
IN P_TickerSymbol varchar(20),
IN P_Address1 varchar(200),
IN P_Address2 varchar(50),
IN P_CityID int(11),
IN P_StateID int(11),
IN P_ZipCode varchar(10),
IN P_CountyID int(11),
IN P_CountryID int(11),
IN P_MetroID int(11),
IN P_Latitude decimal(21,14),
IN P_Longitude decimal(21,14),
IN P_OfficePhone varchar(20),
IN P_Fax varchar(20),
IN P_Email varchar(255),
IN P_IsActive tinyint(1),
IN P_IsCountyHQ tinyint(1),
IN P_IsGlobalHQ tinyint(1),
IN P_Researcher int(11),
IN P_SalesRep int(11),
IN P_SupportAgent int(11),
IN P_NAICSCode varchar(20),
IN P_ISIC varchar(20),
IN P_RatingTierID int(11),
-- IN P_IsMember tinyint(1),
IN P_ExtVendorID varchar(200),
IN P_ProviderID int(11),
IN P_EntityID int(11),
IN P_SaleComp_StageID int(11),
IN P_FloorNumber varchar(25),
IN P_NationalID varchar(45),
IN P_PropertyID int(40),
IN P_EmployeeCount int(40),
IN P_ANZSIC int(11),
IN P_CEOName varchar(100),
IN P_CEOTitle varchar(100), 
IN P_LineOfBusiness varchar(100),
IN P_EmployeesAtLocation varchar(100),
IN P_LegalStatus varchar(100),
IN P_StatusCode varchar(100),
IN P_SubsidiaryCode varchar(100),
IN P_NACECode varchar(100),
IN P_PrimarySICDivisionDesc varchar(100),
IN P_PrimarySIC2DigitDesc varchar(100),
IN P_PrimarySIC3DigitDesc varchar(100),
IN P_RegistrationOrIncorporationDate varchar(100),
IN P_Revenue varchar(100),
IN P_RevenueIndicator varchar(100),
IN P_Tenant_Stage_ID INT,
IN P_MovingToOtherProperty tinyint(1),
IN P_Market varchar(100),
IN P_Bucket varchar(100),
OUT P_NewBranchID int(11)
)
BEGIN
	Declare L_NewBranchID int;
    DECLARE L_LocationID integer; 
    DECLARE L_AddressID integer;
    Declare P_RetAddressID int;
    Declare P_NewConfirmedTenantID int;
    Set L_NewBranchID=null,L_LocationID=null,L_AddressID=null,P_RetAddressID=null,P_NewConfirmedTenantID= null;
    Set @ParentCompanyName = (Select CompanyName From Company Where CompanyID=P_ParentCompanyID);
    -- Check if branch already exists
	IF P_CompanyID > 0 and P_ParentCompanyID > 0 THEN
		BEGIN
			-- update branch details
            SET @jsonArray =  JSON_ARRAY();
            
			select
				CompanyName,Website,NAICSCode,ISIC,MetroID,NationalID,ANZSICCode,CompanyTypeID
				into @CompanyName,@Website,@NAICSCode,@ISIC,@MetroID,@NationalID,@ANZSICCode,@CompanyTypeID
			from Company
			WHERE
				CompanyID=P_CompanyID;
			
			select CompanyTypeName into @CompanyTypeName from Empirical_Prod.CompanyType where CompanyTypeID=@CompanyTypeID;
            select CompanyTypeName into @P_CompanyTypeName from Empirical_Prod.CompanyType where CompanyTypeID=P_CompanyTypeID;
			SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','CompanyName','CurrentValue',P_CompanyName,'PreviousValue',@CompanyName,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
			SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','Website','CurrentValue',P_Website,'PreviousValue',@Website,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
			SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','NAICSCode','CurrentValue',P_NAICSCode,'PreviousValue',@NAICSCode,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
			SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','ISIC','CurrentValue',P_ISIC,'PreviousValue',@ISIC,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
			SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','MetroID','CurrentValue',P_MetroID,'PreviousValue',@MetroID,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
			SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','NationalID','CurrentValue',P_NationalID,'PreviousValue',@NationalID,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
			SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','ANZSICCode','CurrentValue',P_ANZSIC,'PreviousValue',@ANZSICCode,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
			SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','CompanyTypeID','CurrentValue',@P_CompanyTypeName,'PreviousValue',@CompanyTypeName,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
        
            UPDATE Company 
            SET
				CompanyName =P_CompanyName,
                Website = P_Website,
                IsActive=P_IsActive,
                CompanyTypeID = P_CompanyTypeID,
                IsNationalCompany = P_IsNationalBrokerageCompany,
                IsPublic = p_IsPublicCompany,
				NAICSCode = P_NAICSCode,
                ISIC = P_ISIC,
                RatingTierID = P_RatingTierID,
                MetroID = P_MetroID,
				VendorTenantID_Del =P_ConfirmedTenantID,
            --    IsMember=P_IsMember,
                IsCountryHeadQuarter=P_IsCountyHQ,
				IsGlobalHeadQuarter=P_IsGlobalHQ,
                ModifiedBy = P_EntityID,
                ModifiedDate = current_timestamp(),                
                NationalID = P_NationalID,
                AltCompanyName= CASE WHEN LENGTH(TRIM(AltCompanyName)) = 0 THEN @ParentCompanyName ELSE AltCompanyName END,
                ANZSICCode=P_ANZSIC
			WHERE 
				CompanyID = P_CompanyID;               
			-- Check if LocationID already exists
            IF EXISTS (SELECT LocationID FROM `Address` WHERE `ParentID` = P_CompanyID AND Sequence=1 AND `ParentTableID` = 6) THEN
				-- If exists, get LocationID and addressID
                SELECT 
				`LocationID`,`AddressID` 
				INTO 
					L_LocationID, L_AddressID 
				FROM `Address` 
				WHERE `ParentID` = P_CompanyID 
						AND Sequence=1 
						AND `ParentTableID` = 6;
			ELSE
				-- IF not, get only addressID
				SELECT 
					`AddressID` 
				INTO 
					 L_AddressID 
				FROM `Address` 
				WHERE `ParentID` = P_CompanyID 
						AND Sequence=1 
						AND `ParentTableID` = 6;
            END IF;
            
			IF L_LocationID IS NOT NULL AND  P_Latitude IS NOT NULL and P_Longitude IS NOT NULL THEN
				select `Latitude`,`Longitude` into  @Latitude,@Longitude
                from Location WHERE `LocationID` = L_LocationID;
				-- Update Location Info
				UPDATE `Location` 
				SET 
					`Latitude` = P_Latitude,
					`Longitude` = P_Longitude,
                     LocationPoint = POINT(P_Longitude,P_Latitude)
				WHERE
				`LocationID` = L_LocationID;
   			SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','Latitude','CurrentValue',P_Latitude,'PreviousValue',@Latitude,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
			SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','Longitude','CurrentValue',P_Longitude,'PreviousValue',@Longitude,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));

			ELSE
				IF L_LocationID IS NULL AND  P_Latitude IS NOT NULL and P_Longitude IS NOT NULL THEN
					
                    -- Insert Location 
					INSERT INTO `Location`
					(
					`Latitude`,
					`Longitude`,
					`ZCoordinate`,
					`RoooftopSourceID`,
					`GISShapeID`,
                    LocationPoint
					)
					VALUES
					(
					P_Latitude ,
					P_Longitude ,
					null ,
					null,
					null,
                    POINT(P_Longitude,P_Latitude)
					);
					
					SET L_LocationID =  LAST_INSERT_ID();
                END IF;
			END IF;
             
             if P_CountyID is null then
				select CountyID into P_CountyID from Empirical_Prod.Address where AddressID=L_AddressID and ParentID=P_CompanyID and ParentTableID=6 and Sequence=1;
            end if;
            
			CALL CRE_Property_Address_Save(
				L_AddressID,
				2,
				P_CompanyID,
				6,
				L_LocationID,
				1,
				P_Address1,
				P_Address2,
				P_StateID,
				P_CityID,
				P_ZipCode,
				null,
				P_Address1,
				null,
				null,
				P_CountyID,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				P_CountryID,
				P_EntityID,
				1,
				null,
				P_RetAddressID    
				);

            
			-- Update Contact Info
				-- Office Phone
                if P_OfficePhone is not null and trim(IFNULL(P_OfficePhone,'')) <> '' then
                Select ContactValue into @ContactValue from Empirical_Prod.CompanyContact Where CompanyID=P_CompanyID and ContactTypeID=2 and Sequence=1 and IsActive=1;
				SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','OfficePhone','CurrentValue',P_OfficePhone,'PreviousValue',@ContactValue,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
				CALL CRE_Company_Contact_Save (P_CompanyID,2,1,P_OfficePhone,1);
                end if;
				  -- Fax 
                if P_Fax is not null and trim(IFNULL(P_Fax,'')) <> '' then
                Select ContactValue into @ContactValue from Empirical_Prod.CompanyContact Where CompanyID=P_CompanyID and ContactTypeID=5 and Sequence=1 and IsActive=1;
				SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','Fax','CurrentValue',P_Fax,'PreviousValue',@ContactValue,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
				CALL CRE_Company_Contact_Save (P_CompanyID,5,1,P_Fax,1);
				end if;
                -- Email 
				if P_Email is not null and trim(IFNULL(P_Email,'')) <> '' then
                Select ContactValue into @ContactValue from Empirical_Prod.CompanyContact Where CompanyID=P_CompanyID and ContactTypeID=8 and Sequence=1 and IsActive=1;
				SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','Email','CurrentValue',P_Email,'PreviousValue',@ContactValue,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
				CALL CRE_Company_Contact_Save (P_CompanyID,8,1,P_Email,1);
				end if;
           /* 
            --As Matt mention 
            -- Update Company Support
				-- Researcher
                if P_Researcher is not null and trim(IFNULL(P_Researcher,'')) <> '' then
                Select ContactValue into @ContactValue from Empirical_Prod.CompanySupport Where CompanyID=P_CompanyID and SupportRoleTypeID=1 and Sequence=1 and IsActive=1;
				SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','Researcher','CurrentValue',@ContactValue,'PreviousValue',P_Researcher,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
                CALL CRE_Company_Suppport_Save (P_CompanyID,1,P_Researcher,1,1);
                end if;
                -- Sale Rep
                if P_SalesRep is not null and trim(IFNULL(P_SalesRep,'')) <> '' then
                Select ContactValue into @ContactValue from Empirical_Prod.CompanySupport Where CompanyID=P_CompanyID and SupportRoleTypeID=2 and Sequence=1 and IsActive=1;
				SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','SalesRep','CurrentValue',@ContactValue,'PreviousValue',P_SalesRep,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
                CALL CRE_Company_Suppport_Save (P_CompanyID,2,P_SalesRep,1,1);
                end if;
                -- Support Agent
                if P_SupportAgent is not null and trim(IFNULL(P_SupportAgent,'')) <> '' then
                Select ContactValue into @ContactValue from Empirical_Prod.CompanySupport Where CompanyID=P_CompanyID and SupportRoleTypeID=3 and Sequence=1 and IsActive=1;
				SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('Field','SupportAgent','CurrentValue',@ContactValue,'PreviousValue',P_SupportAgent,'LoginEntityID',P_EntityID,'DateTime',current_timestamp()));
                CALL CRE_Company_Suppport_Save (P_CompanyID,3,P_SupportAgent,1,1);          
				end if;
                */
                -- Save change log for Branch Address
				CALL CRE_Changelog_Company_Save(P_CompanyID, @jsonArray,P_EntityID,1,2);
    
				select ConfirmedTenantID into @ConfirmedTenantID 
                from Empirical_Tenants.ConfirmedTenants 
                where CompanyID=P_CompanyID and ProviderID=1;

				select ABN,ACN into @ABN,@ACN 
                from  Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25
                where Tenant_Stage_ID=P_Tenant_Stage_ID ;
                
							CALL `Tenant_Confirmed_Tenant_Save_Zessta_V1`(
						@ConfirmedTenantID , 
						Concat(@ParentCompanyName, case When trim(IFNULL(P_CompanyName,''))="" THEN "" ELSE " - " END,IFNULL(P_CompanyName,'')), 
						Concat(@ParentCompanyName, case When trim(IFNULL(P_CompanyName,''))="" THEN "" ELSE " - " END,IFNULL(P_CompanyName,'')),  
						null, -- Alternate Company Name
						P_Address1, 
						P_Address2, 
						null,  -- Address Street Number
						null,-- Address Street Name
						P_CityID,
						P_StateID, 
						P_ZipCode, 
						P_CountyID, 
						P_CountryID, 
						null, -- Address Text
						P_ExtVendorID, -- External Vendor ID
					   1,  -- Providier ID
                       P_MetroID,
					   P_EntityID, -- Login User ID
                       P_OfficePhone,
                       P_CompanyID,
                       P_Email,
                       P_Website,
                       P_Fax,
                       P_FloorNumber, -- FloorNumber
                       P_NationalID,
                       P_EmployeeCount,
                       P_Revenue,
                        @ABN,
                        @ACN ,
                       P_ANZSIC,-- P_ANZSIC
                       P_CEOName,
						P_CEOTitle,
						P_LineOfBusiness,
						P_ISIC,
						P_EmployeesAtLocation,
						P_LegalStatus,
						P_StatusCode,
						P_SubsidiaryCode,
						P_NAICSCode,
						P_NACECode,
						P_PrimarySICDivisionDesc,
						P_PrimarySIC2DigitDesc,
						P_PrimarySIC3DigitDesc,
						P_RegistrationOrIncorporationDate,
						P_RevenueIndicator,
						P_Tenant_Stage_ID,
                        P_PropertyID,
                        P_EntityID,
						P_MovingToOtherProperty,
						P_Market,
                        P_Bucket,
					   @ConfirmedTenantID 
                   );
                   
            
            -- need to be more clear about suite
            -- select SuiteID into @SuiteID from Empirical_Prod.Suite where PropertyID=P_PropertyID limit 1;
			-- Sending Suite ID null
			CALL CRE_SuiteTenant_Save_Zessta_V1( @ConfirmedTenantID,P_PropertyID,P_EntityID,null -- SuiteID
            ,P_ExtVendorID,P_Tenant_Stage_ID,P_Market,P_Bucket);
				select P_CompanyID into P_NewBranchID;
        END;
        
	END IF;
	 -- if Branch does not exists
	IF P_CompanyID <=0 and P_ParentCompanyID > 0 THEN
			BEGIN         
                 -- Create Branch Entry
				INSERT INTO Company
				(
					CompanyName,
					Website,
                    IsActive,
					CompanyTypeID,
					IsNationalCompany,
					IsPublic,
					NAICSCode,
                    ISIC,
                    RatingTierID,
                    MetroID,
					VendorTenantID_Del,
                 --   IsMember,
                    IsCountryHeadQuarter,
                    IsGlobalHeadQuarter,
					CreatedBy,
					CreatedDate,
					ModifiedBy,
					ModifiedDate,
                    CountryID,
                    NationalID,
                    AltCompanyName,
					ANZSICCode
				)
			Values
				(
					P_CompanyName,
					P_Website,
                    P_IsActive,
					P_CompanyTypeID,
					P_IsNationalBrokerageCompany,
					p_IsPublicCompany,
                    P_NAICSCode,
                    P_ISIC,
                    P_RatingTierID,
                    P_MetroID,
                    P_ConfirmedTenantID,
              --      P_IsMember,
                    P_IsCountyHQ,
					P_IsGlobalHQ,
					P_EntityID,
					current_timestamp(),
					P_EntityID,
					current_timestamp(),
                    P_CountryID,
                    P_NationalID,
                    @ParentCompanyName,
                    P_ANZSIC
				);
                
			set L_NewBranchID=  LAST_INSERT_ID();            
	 	-- select L_NewBranchID as Branch;
            -- Link Branch to Company
            INSERT INTO CompanyRelationship
            (
				ParentCompanyID,
                ChildCompanyID,
                UltimateCompanyID,
                CreatedBy,
                ModifiedBy,
                ModifiedDate
			)
            Values
            (
				P_ParentCompanyID,
                L_NewBranchID,
                GETPARENTCOMPANY(P_ParentCompanyID),
                P_EntityID,
                P_EntityID,
                current_timestamp()
            );
			set @ParentCompany=  LAST_INSERT_ID();            
		-- select @ParentCompany as ParentBranch;
        	
            IF P_Latitude IS NOT NULL and P_Longitude IS NOT NULL THEN
				INSERT INTO `Location`
				(
				`Latitude`,
				`Longitude`,
				`ZCoordinate`,
				`RoooftopSourceID`,
				`GISShapeID`,
                LocationPoint
				)
				VALUES
				(
				P_Latitude ,
				P_Longitude ,
				null ,
				null,
				null,
                POINT(P_Longitude,P_Latitude)
				);
				
				SET L_LocationID =  LAST_INSERT_ID();
       --         select L_LocationID as Location;
			END IF;
            
            CALL CRE_Property_Address_Save (
							-1,
							2,
							L_NewBranchID,
							6,
							L_LocationID,
							1,
							P_Address1,
							P_Address2,
							P_StateID,
							P_CityID,
							P_ZipCode,
							null,
							P_Address1,
							null,
							null,
							P_CountyID,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							null,
							P_CountryID,
							P_EntityID,
							1,
							null,
							P_RetAddressID    
							);
		-- 					select P_RetAddressID as Address;				
             -- Update Contact Info
             				-- Office Phone
                if P_OfficePhone is not null and trim(IFNULL(P_OfficePhone,'')) <> '' then
				CALL CRE_Company_Contact_Save (L_NewBranchID,2,1,P_OfficePhone,1);
                end if;
				  -- Fax 
                if P_Fax is not null and trim(IFNULL(P_Fax,'')) <> '' then
				CALL CRE_Company_Contact_Save (L_NewBranchID,5,1,P_Fax,1);
				end if;
                -- Email 
				if P_Email is not null and trim(IFNULL(P_Email,'')) <> '' then
				CALL CRE_Company_Contact_Save (L_NewBranchID,8,1,P_Email,1);
				end if;

/* --As Matt mention 
			
            -- Update Company Support
				-- Researcher
                if P_Researcher is not null and trim(IFNULL(P_Researcher,'')) <> '' then
                CALL CRE_Company_Suppport_Save (L_NewBranchID,1,P_Researcher,1,1);
                end if;
                -- Sale Rep
                if P_SalesRep is not null and trim(IFNULL(P_SalesRep,'')) <> '' then
                CALL CRE_Company_Suppport_Save (L_NewBranchID,2,P_SalesRep,1,1);
                end if;
                -- Support Agent
                if P_SupportAgent is not null and trim(IFNULL(P_SupportAgent,'')) <> '' then
                CALL CRE_Company_Suppport_Save (L_NewBranchID,3,P_SupportAgent,1,1);          
				end if;
    
                
		*/	   
				select ABN,ACN into @ABN,@ACN 
                from  Empirical_DataIngestion.Raw_Confirmed_Tenants_2024_09_25
                where Tenant_Stage_ID=P_Tenant_Stage_ID ;
                
            -- check if Tenant ID exists. if not, create tenant and associate with the branch.
				IF P_ConfirmedTenantID IS NULL or P_ConfirmedTenantID <=0 THEN
                BEGIN

					CALL `Tenant_Confirmed_Tenant_Save_Zessta_V1`(
						-1, 
						Concat(@ParentCompanyName, case When trim(IFNULL(P_CompanyName,''))="" THEN "" ELSE " - " END,IFNULL(P_CompanyName,'')), 
						Concat(@ParentCompanyName, case When trim(IFNULL(P_CompanyName,''))="" THEN "" ELSE " - " END,IFNULL(P_CompanyName,'')),  
						null, -- Alternate Company Name
						P_Address1, 
						P_Address2, 
						null,  -- Address Street Number
						null,-- Address Street Name
						P_CityID,
						P_StateID, 
						P_ZipCode, 
						P_CountyID, 
						P_CountryID, 
						null, -- Address Text
						P_ExtVendorID, -- External Vendor ID
					   P_ProviderID,  -- Providier ID
                       P_MetroID,
					   P_EntityID, -- Login User ID
                       P_OfficePhone,
                       L_NewBranchID,
                       P_Email,
                       P_Website,
                       P_Fax,
                       P_FloorNumber, -- FloorNumber
                       P_NationalID,
                       P_EmployeeCount,
                       P_Revenue,
						 @ABN,@ACN,
						 P_ANZSIC,
                       P_CEOName,
						P_CEOTitle,
						P_LineOfBusiness,
						P_ISIC,
						P_EmployeesAtLocation,
						P_LegalStatus,
						P_StatusCode,
						P_SubsidiaryCode,
						P_NAICSCode,
						P_NACECode,
						P_PrimarySICDivisionDesc,
						P_PrimarySIC2DigitDesc,
						P_PrimarySIC3DigitDesc,
						P_RegistrationOrIncorporationDate,
						P_RevenueIndicator,
                        P_Tenant_Stage_ID,
                        P_PropertyID,
                        P_EntityID,
						P_MovingToOtherProperty,
                        P_Market,
                        P_Bucket,
					   P_NewConfirmedTenantID
                   );
                     
                   -- Associate Tenant to Branch
                  -- Update Company Set VendorTenantID_Del=P_NewConfirmedTenantID Where CompanyID=L_NewBranchID;
                   UPDATE Empirical_Tenants.ConfirmedTenants Set SaleComp_Stage_ID= P_SaleComp_StageID
                   Where ConfirmedTenantID =P_NewConfirmedTenantID;
                   
                END;
                          
                END IF;
               SET P_NewBranchID=  L_NewBranchID;  
               
			END;
	END IF;	
END