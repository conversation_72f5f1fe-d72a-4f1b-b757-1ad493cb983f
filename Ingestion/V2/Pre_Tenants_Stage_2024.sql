-- MySQL dump 10.13  Distrib 8.0.32, for Linux (x86_64)
--
-- Host: 127.0.0.1    Database: Empirical_DataStage
-- ------------------------------------------------------
-- Server version	8.0.34

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `Pre_Tenants_Stage_2024`
--

DROP TABLE IF EXISTS `Pre_Tenants_Stage_2024`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Pre_Tenants_Stage_2024` (
  `Arealytics_Tenant_Id` int NOT NULL AUTO_INCREMENT,
  `VendorID` varchar(50) DEFAULT NULL,
  `TenantName` varchar(225) DEFAULT NULL,
  `Address1` varchar(255) DEFAULT NULL,
  `Address2` varchar(255) DEFAULT NULL,
  `City` varchar(255) DEFAULT NULL,
  `State` varchar(50) DEFAULT NULL,
  `StateAbbr` varchar(50) DEFAULT NULL,
  `CountryCode` varchar(50) DEFAULT NULL,
  `PostalCode` varchar(50) DEFAULT NULL,
  `NationalID` varchar(255) DEFAULT NULL,
  `OfficePhone` varchar(50) DEFAULT NULL,
  `Fax` varchar(50) DEFAULT NULL,
  `CEOName` varchar(255) DEFAULT NULL,
  `CEOTitle` varchar(50) DEFAULT NULL,
  `LineOfBusiness` varchar(50) DEFAULT NULL,
  `SICCode` varchar(50) DEFAULT NULL,
  `Revenue` decimal(14,2) DEFAULT NULL,
  `EmployeesAtLocation` int DEFAULT NULL,
  `EmployeeCount` int DEFAULT NULL,
  `LegalStatus` varchar(50) DEFAULT NULL,
  `StatusCode` varchar(50) DEFAULT NULL,
  `SubsidiaryCode` varchar(50) DEFAULT NULL,
  `IsProcessed` tinyint DEFAULT NULL,
  `ConfirmedTenantID` int DEFAULT NULL,
  `PropertyID` int DEFAULT NULL,
  `MatchingScore` int DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `ModifiedDate` datetime DEFAULT NULL,
  `Latitude` decimal(21,14) DEFAULT NULL,
  `Longitude` decimal(21,14) DEFAULT NULL,
  `ParentCompanyID` int DEFAULT NULL,
  `BranchID` int DEFAULT NULL,
  `BatchID` int DEFAULT NULL,
  `ProviderID` int DEFAULT NULL,
  `IsDefault` tinyint(1) DEFAULT NULL,
  `NAICSCode` varchar(255) DEFAULT NULL,
  `NACECode` varchar(255) DEFAULT NULL,
  `Email` varchar(255) DEFAULT NULL,
  `WebsiteURL` varchar(255) DEFAULT NULL,
  `ModifiedBy` int DEFAULT NULL,
  `IsHidden` tinyint(1) DEFAULT NULL,
  `IsDeleted` tinyint(1) DEFAULT NULL,
  `HidedBy` int DEFAULT NULL,
  `HidedDate` datetime DEFAULT NULL,
  `HideReasonID` int DEFAULT NULL,
  `HideReasonComments` varchar(1000) DEFAULT NULL,
  `ASICEntityStatus` varchar(45) DEFAULT NULL,
  `ASICEntityType` varchar(45) DEFAULT NULL,
  `ASICEntityClass` varchar(45) DEFAULT NULL,
  `ABNStatus` varchar(45) DEFAULT NULL,
  `ABN_StatusFromDate` varchar(45) DEFAULT NULL,
  `GST_Status` varchar(45) DEFAULT NULL,
  `GST_StatusFromDate` varchar(45) DEFAULT NULL,
  `RegistrationOrIncorporationDate` varchar(45) DEFAULT NULL,
  `EntityAge` varchar(10) DEFAULT NULL,
  `EmployeeIndicator` varchar(45) DEFAULT NULL,
  `RevenueIndicator` varchar(45) DEFAULT NULL,
  `HQ_ID` varchar(45) DEFAULT NULL,
  `HQ_CompanyName` varchar(100) DEFAULT NULL,
  `NumberofMembersinHierarchy` varchar(45) DEFAULT NULL,
  `ImmediateParentDUNS` varchar(45) DEFAULT NULL,
  `ImmediateParentName` varchar(100) DEFAULT NULL,
  `ImmediateParentCountry` varchar(45) DEFAULT NULL,
  `DomesticParentDUNS` varchar(45) DEFAULT NULL,
  `DomesticParentName` varchar(100) DEFAULT NULL,
  `DomesticParentCountry` varchar(45) DEFAULT NULL,
  `GlobalUltimateParentDUNS` varchar(45) DEFAULT NULL,
  `GlobalUltimateParentName` varchar(100) DEFAULT NULL,
  `GlobalUltimateParentCountry` varchar(45) DEFAULT NULL,
  `PrimarySICDesc` varchar(45) DEFAULT NULL,
  `PrimarySIC3Digit` varchar(45) CHARACTER SET big5 COLLATE big5_chinese_ci DEFAULT NULL,
  `PrimarySIC3DigitDesc` varchar(45) DEFAULT NULL,
  `PrimarySIC2Digit` varchar(45) DEFAULT NULL,
  `PrimarySIC2DigitDesc` varchar(45) DEFAULT NULL,
  `PrimarySICDivision` varchar(45) DEFAULT NULL,
  `PrimarySICDivisionDesc` varchar(45) DEFAULT NULL,
  `SubHideReasonID` int DEFAULT NULL,
  `ANZSICCode` int DEFAULT NULL,
  `ConfidenceScore` int DEFAULT NULL,
  `IsPrimaryProvider` int DEFAULT '0',
  `PrimaryProviderArealyticsTenantID` int DEFAULT NULL,
  `Provider` varchar(45) DEFAULT NULL,
  `Tenant_Stage_ID` int DEFAULT NULL,
  PRIMARY KEY (`Arealytics_Tenant_Id`)
) ENGINE=InnoDB AUTO_INCREMENT=7028661 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;
