CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Tenant_Stage_Import_V2`(IN P_BatchID INT,IN  P_Limit INT)
BEGIN
	DROP TEMPORARY TABLE IF EXISTS tempTenantStage;
	CREATE TEMPORARY TABLE tempTenantStage(
     select * from Empirical_DataStage.Pre_Tenants_Stage_2024 
     where BatchID=P_BatchID and Tenant_Stage_ID is null limit P_Limit
    );
    select * from tempTenantStage;
	DROP TEMPORARY TABLE IF EXISTS tempPrimaryDataSource;
    CREATE TEMPORARY TABLE tempPrimaryDataSource(
		Tenant_Stage_ID INT,
        Arealytics_Tenant_Id INT,
        PrimaryProviderArealyticsTenantID INT,
		Primary_Tenant_Stage_ID INT
    );
    select min(Arealytics_Tenant_Id) into @min_Arealytics_Tenant_Id from tempTenantStage;
    
    while @min_Arealytics_Tenant_Id is not null
    do
		insert into Empirical_DataStage.Tenants_Stage (
			VendorID,TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,StatusCode,SubsidiaryCode,IsProcessed,ConfirmedTenantID,PropertyID,MatchingScore,
			CreatedDate,ModifiedDate,Latitude,Longitude,ParentCompanyID,BranchID,BatchID,ProviderID,
			IsDefault,NAICSCode,NACECode,Email,WebsiteURL,ModifiedBy,IsHidden,IsDeleted,HidedBy,HidedDate,HideReasonID,
			HideReasonComments,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,SubHideReasonID,ANZSICCode
        )
        select VendorID,TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,StatusCode,SubsidiaryCode,IsProcessed,ConfirmedTenantID,PropertyID,MatchingScore,
			CreatedDate,ModifiedDate,Latitude,Longitude,ParentCompanyID,BranchID,BatchID,ProviderID,
			IsDefault,NAICSCode,NACECode,Email,WebsiteURL,ModifiedBy,IsHidden,IsDeleted,HidedBy,HidedDate,HideReasonID,
			HideReasonComments,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,SubHideReasonID,ANZSICCode from tempTenantStage
		where Arealytics_Tenant_Id= @min_Arealytics_Tenant_Id;
    
		select last_insert_id() into @lastInsertTenantStageID;

        update Pre_Tenants_Stage_2024 set Tenant_Stage_ID= @lastInsertTenantStageID where Arealytics_Tenant_Id= @min_Arealytics_Tenant_Id;

		insert into Tenant_Stage_DataSource (ConfidenceScore,
				IsPrimaryProvider,
				PrimaryProviderArealyticsTenantID,
				TenantStageID) 
			select ConfidenceScore,
				IsPrimaryProvider,
				PrimaryProviderArealyticsTenantID, @lastInsertTenantStageID 
			from tempTenantStage
			where Arealytics_Tenant_Id= @min_Arealytics_Tenant_Id;
        
        select min(Arealytics_Tenant_Id) into @min_Arealytics_Tenant_Id from tempTenantStage 
			where Arealytics_Tenant_Id>@min_Arealytics_Tenant_Id;
		
    end while;
    
	update Tenant_Stage_DataSource as a
    join Pre_Tenants_Stage_2024 as b on a.PrimaryProviderArealyticsTenantID=b.Arealytics_Tenant_Id 
    set a.PrimaryTenantStageID=b.Tenant_Stage_ID where a.PrimaryProviderArealyticsTenantID is not null and b.BatchID=P_BatchID;
    
END