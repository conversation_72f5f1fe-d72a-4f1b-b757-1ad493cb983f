const sql = require('mysql');

// Database configuration
const config = {
  user: 'imperium_admin',
  password: 'CoVayKfSgNgq6n8HJU4O',
  server: 'localhost',
  database: 'Empirical_Prod',
  port: 2339,
  options: {
    encrypt: true, // For SQL Server
  },
};
const args = process.argv.slice(2);

// Stored procedure name
const tenantStageImportStoredProcedureName = 'CRE_Suite_And_Floor_Import_Bronze_Refresh';
const tenantIngestion = async (BatchID) => {
  return new Promise(function (resolve, reject) {
    try {

      console.log(BatchID, "Started");

      const pool = sql.createPool(config);
      let group_concat_max_len_query = `SET SESSION group_concat_max_len = 1000000000;`;
      let spcall1 = `CALL Empirical_DataStage.${tenantStageImportStoredProcedureName}(${BatchID});`;

      pool.getConnection(async function (error, connection) {
        if (connection) {
          connection.query(group_concat_max_len_query, async (error, results) => {
            if (error) {
              console.log(group_concat_max_len_query, 'Error', error);
              resolve("reject");
            }
            if (results) {
              console.log(group_concat_max_len_query, 'Success', new Date());
              connection.query(spcall1, (error, results) => {
                if (error) {
                  console.log(spcall1, 'Error', error);
                  resolve(error);
                }
                if (results) {
                  console.log(spcall1, 'Success', new Date());
                  resolve(results);
                }
              });
            }
          });
          connection.release();
        } else if (error) {
          resolve(error);
        }
      });
    } catch (error) {
      resolve(error);
    }
  });
}


const processBatchObjects = async () => {
  try {
    const response = await tenantIngestion(args[1]);
    console.log(response);
  } catch (error) {
    console.error('Error processing objects:', error);
  }
};

processBatchObjects();


