
drop temporary table if exists tempProperties;
create temporary table tempProperties(
select ParentID from Empirical_Prod.Address where StateID=59 and Sequence=1 and IsActive=1 and ParentTableID=1
);
select * from tempProperties;
drop temporary table if exists tempSuiteTenantConfirmedTenants;
create temporary table tempSuiteTenantConfirmedTenants(
select distinct ConfirmedTenantID from Empirical_Prod.SuiteTenant where PropertyID in (select * from tempProperties) and IsActive=1
);

drop temporary table if exists tempTenantStageConfirmedTenant;
create temporary table tempTenantStageConfirmedTenant(
select ConfirmedTenantID from Empirical_DataStage.Tenants_Stage where ProviderID=14 and PropertyID in (select * from tempProperties)
);

drop temporary table if exists temp5;
create temporary table temp5(
select a.ConfirmedTenantID as ST_CT,b.ConfirmedTenantID as TS_CT from tempSuiteTenantConfirmedTenants as a
left outer join tempTenantStageConfirmedTenant as b on a.ConfirmedTenantID=b.ConfirmedTenantID
);


drop temporary table if exists tempLeaseConfirmedTenant;
create temporary table tempLeaseConfirmedTenant(
select ConfirmedTenantID from Empirical_Prod.Lease 
where ConfirmedTenantID in  (select ST_CT from temp5 where TS_CT is null) and PropertyID in (select * from tempProperties) and IsActive=1 #and TransactionOriginationTypeID=1
);


drop temporary table if exists temp6;
create temporary table temp6(
select a.ST_CT as existingCT,b.ConfirmedTenantID as leaseConfirmedTenant  from temp5 as a
left outer join tempLeaseConfirmedTenant  as b on a.ST_CT=b.ConfirmedTenantID 
where a.TS_CT is null
);
select count(*) as NewConfirmedTenant from temp5 ;
select count(*) as NewConfirmedTenant from temp5 where TS_CT is not null;
select count(*) as OldConfirmedTenant from temp5 where TS_CT is null;

select count(distinct existingCT) as NonLeaseConfirmedTenant from temp6 where leaseConfirmedTenant is null;
select count(distinct existingCT) as LeaseConfirmedTenant from temp6 where leaseConfirmedTenant is not null;

drop temporary table if exists tempCompany;
create temporary table tempCompany(
select distinct CompanyID from Empirical_Tenants.ConfirmedTenants where ConfirmedTenantID in (select distinct existingCT as NonLeaseConfirmedTenant from temp6 where leaseConfirmedTenant is null)
);

#select * from Empirical_Prod.Company where CompanyID in (select * from tempCompany) limit 1;
/*
UPDATE Empirical_Prod.Company 
SET 
   ModifiedBy=22,
   ModifiedDate= CURRENT_TIMESTAMP(),
    IsHidden = 1,
    HidedBy = 22,
    HidedDate = CURRENT_TIMESTAMP(),
    HideReasonID = 3,-- #  HideReasonName- 'Other'
    SubHideReasonID = 10,-- SubHideReasonName- Not present on Tenant Roster
    HideReasonComments = 'Not updating the record via the AL tenant automation gold bucket data.'
WHERE
CompanyID =358803;
    #CompanyID IN (select * from Empirical_Prod.Company where CompanyID in (select * from tempCompany) );
    
    */