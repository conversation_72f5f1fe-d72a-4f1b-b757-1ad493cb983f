drop temporary table if exists temp1;
create temporary table temp1(
select ParentID from Empirical_Prod.Address where ParentTableID=6 and IsActive=1 and sequence=1 and StateID=58
);

drop temporary table if exists temp2;
create temporary table temp2(
select CompanyID from Empirical_Prod.Company where CompanyID in (select * from temp1) and IsHidden=1
);

drop temporary table if exists temp3;
create temporary table temp3(
select b.BranchID from Empirical_DataStage.QLD_Raw_Confirmed_Tenants a inner join Empirical_DataStage.Tenants_Stage b on a.VendorID=b.VendorID
where a.BatchID>=29000 and a.BatchID<29300
);

drop temporary table if exists temp4;
create temporary table temp4(
select CompanyID from temp2 where CompanyID in (select BranchID from temp3)
);

UPDATE Empirical_Prod.Company 
SET 
   ModifiedBy=22,
   ModifiedDate= CURRENT_TIMESTAMP(),
    IsHidden = NULL,
    HidedBy = NULL,
    HidedDate = NULL,
    HideReasonID = NULL,
    SubHideReasonID = NULL,
    HideReasonComments = 'Updated the company record with gold data.'
WHERE
    CompanyID IN (select CompanyID from temp4 where IsHidden=1 );
