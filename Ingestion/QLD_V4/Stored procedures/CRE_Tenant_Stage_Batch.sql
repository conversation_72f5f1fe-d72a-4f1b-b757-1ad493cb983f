CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Tenant_Stage_Batch`(IN P_BatchID INT,IN P_Limit INT)
BEGIN
set @BatchID=P_BatchID;
SET @LoopCount=1;
SET @TotalCount=0;
select count(*) into @TotalCount from Empirical_DataStage.QLD_Raw_Confirmed_Tenants
where BatchID=P_BatchID;

if P_Limit>0 then
    set @LoopTotalCount=FLOOR(@TotalCount/P_Limit);

    while @LoopCount<=@LoopTotalCount do
        set @BatchID=@BatchID+1;
        -- select @BatchID;
        UPDATE Empirical_DataStage.QLD_Raw_Confirmed_Tenants SET BatchID =@BatchID
        WHERE (QLD_Raw_Confirmed_TenantsID is not null and BatchID= P_BatchID) order by QLD_Raw_Confirmed_TenantsID desc limit P_Limit;
        set @LoopCount=@LoopCount+1;
    end while;
end if;
END