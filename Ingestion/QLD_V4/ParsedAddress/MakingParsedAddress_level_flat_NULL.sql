drop temporary table if exists QLDTenantsMovingFromOnePropertyToOtherPropertyWithBatchID;
create temporary table QLDTenantsMovingFromOnePropertyToOtherPropertyWithBatchID(
select a.QLD_Raw_Confirmed_TenantsID, a.BatchID ,a.Vendor<PERSON>,b.Tenant_Stage_ID,b.TenantName as TenantStageName,a.TenantName as ConfirmedTenantName,a.PropertyID as NewProperty,b.PropertyID as OldProperty
 from Empirical_DataStage.QLD_Raw_Confirmed_Tenants a inner join Empirical_DataStage.Tenants_Stage b on a.VendorID=b.VendorID
where a.PropertyID!=b.PropertyID  and b.ProviderID=6
);

create table Empirical_DataStage.QLDTenantsMovingFromOnePropertyToOtherPropertyWithBatchID as 
select * from QLDTenantsMovingFromOnePropertyToOtherPropertyWithBatchID;


select * from Empirical_DataStage.QLDTenantsMovingFromOnePropertyToOtherPropertyWithBatchID ;

select * from  Empirical_DataStage.Tenants_Stage_ParsedAddress where Tenant_Stage_ID in (
select Tenant_Stage_ID from Empirical_DataStage.QLDTenantsMovingFromOnePropertyToOtherPropertyWithBatchID 
);

drop temporary table if exists tempforMakingAddressNULL;
create temporary table tempforMakingAddressNULL(
select distinct VendorID from Empirical_DataStage.QLD_Raw_Confirmed_Tenants 
where BatchID in (
29000,
29100,
29200
)
);

drop temporary table if exists tempforMakingAddressNULLTSID;
create temporary table tempforMakingAddressNULLTSID(
select Tenant_Stage_Id from Tenants_Stage where VendorID in ( select * from tempforMakingAddressNULL) and ProviderID =6
);

drop temporary table if exists tempforMakingAddressNULLTSIDIngestedOne;
create temporary table tempforMakingAddressNULLTSIDIngestedOne(
select Tenant_Stage_ID from  Empirical_DataStage.QLDTenantsMovingFromOnePropertyToOtherPropertyWithBatchID where Tenant_Stage_ID in (
select * from tempforMakingAddressNULLTSID 
));
-- 8547 - chnaged 

select count(*) from  Empirical_DataStage.Tenants_Stage_ParsedAddress where Tenant_Stage_ID in (
select * from tempforMakingAddressNULLTSIDIngestedOne 
);

-- 35 for only we  have Parsed address

select * from  Empirical_DataStage.Tenants_Stage_ParsedAddress where Tenant_Stage_ID in (
select * from tempforMakingAddressNULLTSIDIngestedOne 
) and level_number is not NULL and flat_number is not NULL;


select * from  Empirical_DataStage.Tenants_Stage_ParsedAddress   where Tenant_Stage_ID in (
select * from tempforMakingAddressNULLTSIDIngestedOne 
)  and Tenant_Stage_ID=1101213 ;

select * from  Empirical_DataStage.Tenants_Stage_ParsedAddress   where  Tenant_Stage_ID=66624 ;

update  Empirical_DataStage.Tenants_Stage_ParsedAddress set flat_number=NULL , level_number=NULL   where Tenant_Stage_ID in (
select * from tempforMakingAddressNULLTSIDIngestedOne 
);


-- 64965
select PropertyID, BranchID, TenantName from Empirical_DataStage.Tenants_Stage where Tenant_Stage_Id in ( 64965);