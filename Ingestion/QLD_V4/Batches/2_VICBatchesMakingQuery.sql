drop temporary table if exists tmpduplicateVendorsinRCT;
create temporary table tmpduplicateVendorsinRCT(
select Vendor<PERSON> from Empirical_DataStage.QLD_Raw_Confirmed_Tenants group by VendorID having count(*)>1); 

-- update Empirical_DataStage.QLD_Raw_Confirmed_Tenants set BatchID=29300 where Vendor<PERSON> in (select * from tmpduplicateVendorsinRCT);

drop temporary table if exists tmpSingleVendorinRCT;
create temporary table tmpSingleVendorinRCT(
select Vendor<PERSON> from Empirical_DataStage.QLD_Raw_Confirmed_Tenants group by VendorID having count(*)=1); 

select count(*) from Empirical_DataStage.QLD_Raw_Confirmed_Tenants where Vendor<PERSON> in (select * from tmpduplicateVendorsinRCT); -- Vendor ID to be avoided

drop temporary table if exists tempTSMultiVendorID;
create temporary table tempTSMultiVendorID(
SELECT b.VendorID from tmpSingleVendorinRCT as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID group by b.Vendor<PERSON> having count(*)>1);

-- update Empirical_DataStage.QLD_Raw_Confirmed_Tenants set BatchID=29700 where Vendor<PERSON> in (select * from tempTSMultiVendorID);

select count(*) from Empirical_DataStage.QLD_Raw_Confirmed_Tenants where VendorID in (select * from tempTSMultiVendorID); -- VendorID to be avoided

drop temporary table if exists tmpRCTFinal;
create temporary table tmpRCTFinal(
select VendorID from tmpSingleVendorinRCT where VendorID not in (select * from tempTSMultiVendorID) ); 

drop temporary table if exists tempTS;
create temporary table tempTS (select a.VendorID,a.BranchID from Empirical_DataStage.Tenants_Stage as a
inner join tmpRCTFinal as b on a.VendorID=b.VendorID);

drop temporary table if exists tmpTSwithDuplicateBranchID;
create temporary table tmpTSwithDuplicateBranchID(
select VendorID from tempTS where BranchID is not null group by BranchID having count(*)>1);

drop temporary table if exists tmpRCTFinal_1;
create temporary table tmpRCTFinal_1( select VendorID from tmpRCTFinal where VendorID not in (select VendorID from tmpTSwithDuplicateBranchID));

drop temporary table if exists tempTS;
create temporary table tempTS (select a.VendorID,a.BranchID from Empirical_DataStage.Tenants_Stage as a
inner join tmpRCTFinal_1 as b on a.VendorID=b.VendorID);

drop temporary table if exists tempTS_1;
create temporary table tempTS_1 (select VendorID from tempTS);

/*drop temporary table if exists tmpduplicateVendorsinRCTNewVendors;
create temporary table tmpduplicateVendorsinRCTNewVendors (select a.VendorID  from tmpRCTFinal_1 as a where a.VendorID not in (select VendorID from tempTS_1));
*/
drop temporary table if exists tempBranchNullVendor;
create temporary table tempBranchNullVendor(
select VendorID from tempTS where BranchID is null);

drop temporary table if exists tempBranchNotNullVendor;
create temporary table tempBranchNotNullVendor(
select VendorID from tempTS where BranchID is not null);
/**
update Empirical_DataStage.QLD_Raw_Confirmed_Tenants set BatchID=29300 where VendorID in (select * from tmpduplicateVendorsinRCT); -- Multi VendorID in QLD_Raw_Confirmed_Tenants
update Empirical_DataStage.QLD_Raw_Confirmed_Tenants set BatchID=29700 where VendorID in (select * from tempTSMultiVendorID); -- Multi VendorID in Tenants Stage
update Empirical_DataStage.QLD_Raw_Confirmed_Tenants set BatchID=29800 where VendorID in (select VendorID from tmpTSwithDuplicateBranchID); -- Multi BranchID in Tenants Stage
update Empirical_DataStage.QLD_Raw_Confirmed_Tenants set BatchID=29100 where VendorID in (select VendorID from tempBranchNullVendor); -- Null BranchID in Tenants Stage
update Empirical_DataStage.QLD_Raw_Confirmed_Tenants set BatchID=29000 where VendorID in (select VendorID from tempBranchNotNullVendor); -- Not Null BranchID in Tenants Stage
update Empirical_DataStage.QLD_Raw_Confirmed_Tenants set BatchID=29200 where BatchID is  null; -- Not Null BatchId in Tenants Stage


**/
select count(*),BatchID from Empirical_DataStage.QLD_Raw_Confirmed_Tenants group by BatchID;
-- update Empirical_DataStage.QLD_Raw_Confirmed_Tenants set BatchID=NULL where BatchID=3700;