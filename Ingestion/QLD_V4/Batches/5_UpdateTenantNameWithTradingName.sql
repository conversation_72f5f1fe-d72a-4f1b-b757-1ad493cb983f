/*drop temporary table if exists tempTenantName;
create temporary table tempTenantName(select TenantName,TradingName,TradingNames_illion,VendorID from Empirical_DataStage.NSW_Raw_CT_with_Unique_IPDUNS_Under_SameProperty);

select * from tempTenantName where TenantName!=TradingName and TradingNames_illion is not null and TradingNames_illion not like '%,%' and TradingNames_illion not like '%none%' and TradingNames_illion !='';

drop temporary table if exists temp14Provider;
create temporary table temp14Provider(
select a.Tenant_Stage_Id,a.ConfirmedTenantID,a.BranchID,a.TenantName,a.VendorID from Empirical_DataStage.Tenants_Stage as a 
inner join Empirical_DataStage.NSW_Raw_Confirmed_Tenants as c on c.Tenant_Stage_ID=a.Tenant_Stage_ID 
where a.ProviderID=14 and c.BatchID not in (9900,9800,9700,9391,9392,9397,9398,9390));

drop temporary table if exists temp1;
create temporary table temp1(
select a.Tenant_Stage_Id,a.Confirmed<PERSON>,a.<PERSON>,a.TenantName,b.Tenant<PERSON> as NewTenantName,b.Trading<PERSON>ame as NewTradeName,b.TradingNames_illion,a.VendorID  from temp14Provider as a
inner join Empirical_DataStage.NSW_Raw_CT_with_Unique_IPDUNS_Under_SameProperty as b on a.VendorID=b.VendorID  where b.TenantName!=b.TradingName 
and b.TradingNames_illion is not null 
and b.TradingNames_illion not like '%,%' and b.TradingNames_illion not like '%none%' and b.TradingNames_illion !=''
);
select * from temp1;
drop temporary table if exists tempCompany;
create temporary table tempCompany(
select * from Empirical_Prod.Company where CompanyID in (select BranchID from temp1)  and CreatedDate >'2024-02-26'
);

drop temporary table if exists tempParentCompany;
create temporary table tempParentCompany(
select * from Empirical_Prod.Company where CompanyID in (
select ParentCompanyID from Empirical_Prod.CompanyRelationship where ChildCompanyID in (select BranchID from temp1))  and CreatedDate >'2024-02-26'
);

drop temporary table if exists tempConfirmedTenants;
create temporary table tempConfirmedTenants(
select * from Empirical_Tenants.ConfirmedTenants where CompanyID in (select BranchID from temp1) and CreatedDate >'2024-02-26'
);


update Empirical_DataStage.Tenants_Stage as TS 
inner join temp1 as temp on TS.Tenant_Stage_Id =temp.Tenant_Stage_Id and TS.VendorID=temp.VendorID
set TS.TenantName=temp.NewTradeName;

update Empirical_Prod.Company as C
inner join temp1 as temp on C.CompanyID =temp.BranchID 
set C.AltCompanyName=temp.NewTradeName;



update Empirical_Prod.Company as C
inner join Empirical_Prod.CompanyRelationship as CR on CR.ParentCompanyID=C.CompanyId
inner join temp1 as temp on CR.ChildCompanyID =temp.BranchID 
set C.CompanyName=temp.NewTradeName ;

update Empirical_Tenants.ConfirmedTenants as CT
inner join temp1 as temp on CT.CompanyID=temp.BranchID
set CT.TenantName=temp.NewTradeName and CT.CompanyName=temp.NewTradeName;
*/

drop temporary table if exists tempTenantName;
create temporary table tempTenantName(select TenantName,TradingName,TradingNames_illion,VendorID from Empirical_DataStage.QLD_Raw_CT_with_Unique_IPDUNS_Under_SameProperty);

drop temporary table if exists tempTenantName1;
create temporary table tempTenantName1(
select * from tempTenantName where TenantName!=TradingName and TradingNames_illion is not null and TradingNames_illion not like '%,%' and TradingNames_illion not like '%none%' and TradingNames_illion !='');

drop temporary table if exists tempTenantName2;
create temporary table tempTenantName2(
select temp.*,TS.QLD_Raw_Confirmed_TenantsID from Empirical_DataStage.QLD_Raw_Confirmed_Tenants as TS 
inner join tempTenantName1 as temp on  TS.VendorID=temp.VendorID
);

update Empirical_DataStage.QLD_Raw_Confirmed_Tenants as TS 
inner join tempTenantName2 as temp on  TS.VendorID=temp.VendorID and TS.QLD_Raw_Confirmed_TenantsID=temp.QLD_Raw_Confirmed_TenantsID
set TS.TenantName=temp.TradingName ;