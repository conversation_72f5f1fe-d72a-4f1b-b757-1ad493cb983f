
drop temporary table if exists tempLeaseCT;
create temporary table tempLeaseCT(
select distinct a.*,b.ConfirmedTenantID from Empirical_DataStage.QLD_Raw_Confirmed_Tenants a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and  a.BatchID in (29000,29100,29200)
);

drop temporary table if exists tempLeaseProp;
create temporary table tempLeaseProp(
select distinct a.PropertyID from Empirical_DataStage.QLD_Raw_Confirmed_Tenants a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and  a.BatchID in (29000,29100,29200)
);

drop temporary table if exists tempLeaseRawCT;
create temporary table tempLeaseRawCT(
select distinct a.* from Empirical_DataStage.QLD_Raw_Confirmed_Tenants a
inner join tempLeaseProp b on a.PropertyID=b.PropertyID and  a.BatchID in (29000,29100,29200)
);


drop temporary table if exists tempLeaseTenants;
create temporary table tempLeaseTenants(
select b.* from Empirical_DataStage.Tenants_Stage as a
inner join Empirical_DataStage.QLD_Raw_Confirmed_Tenants b on a.VendorID=b.VendorID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where a.BranchID in (select CompanyID from Empirical_Tenants.ConfirmedTenants 
    where ConfirmedTenantID in (select distinct ConfirmedTenantID from tempLeaseCT)) and a.ProviderID=6 and b.BatchID in (29000,29100,29200)
);

/*

update Empirical_DataStage.QLD_Raw_Confirmed_Tenants set BatchID=29600 -- Existing Tenants with Lease
where QLD_Raw_Confirmed_TenantsID in (select QLD_Raw_Confirmed_TenantsID from tempLeaseTenants);*/

