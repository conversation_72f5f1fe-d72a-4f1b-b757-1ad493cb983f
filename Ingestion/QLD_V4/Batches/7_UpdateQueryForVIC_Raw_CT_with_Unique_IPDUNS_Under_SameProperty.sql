drop temporary table if exists temp;
create temporary table temp(
select a.QLD_Raw_Confirmed_TenantsID from Empirical_DataStage.QLD_Raw_Confirmed_Tenants as a 
left outer join Empirical_DataStage.QLD_Raw_CT_with_Unique_IPDUNS_Under_SameProperty as b on a.VendorID=b.VendorID 
where  b.VendorID is null and a.BatchID in (29000, 29100, 29200 ) #group by a.BatchID#,9800,9700
);

update Empirical_DataStage.QLD_Raw_Confirmed_Tenants set BatchID=29400 where QLD_Raw_Confirmed_TenantsID in (select * from temp);