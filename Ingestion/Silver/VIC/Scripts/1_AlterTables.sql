ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_Confirmed_Tenants` 
ADD COLUMN `Silver_VIC_Raw_Confirmed_TenantsID` INT NOT NULL AUTO_INCREMENT ,
ADD PRIMARY KEY (`Silver_VIC_Raw_Confirmed_TenantsID`);

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_Confirmed_Tenants` 
ADD COLUMN `IsProcessed` INT NULL DEFAULT NULL AFTER `Silver_VIC_Raw_Confirmed_TenantsID`,
ADD COLUMN `Tenant_Stage_ID` INT NULL DEFAULT NULL AFTER `IsProcessed`;

-- ALTER TABLE `Empirical_DataStage`.`QLD_Raw_Illion_Tenants` 
-- ADD COLUMN `Tenant_Stage_ID` INT NULL DEFAULT NULL AFTER `StreetNoMax`;

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_Overture_Tenants` 
ADD COLUMN `Tenant_Stage_ID` INT NULL DEFAULT NULL AFTER `StreetNoMax`;

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_ZoomInfo_Tenants` 
ADD COLUMN `Tenant_Stage_ID` INT NULL DEFAULT NULL AFTER `StreetNoMax`;

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_GoogleMaps_Tenants` 
ADD COLUMN `Tenant_Stage_ID` INT NULL DEFAULT NULL AFTER `StreetNoMax`;

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_ZoomInfo_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) VISIBLE,
ADD INDEX `idx_VendorID`(`VendorID`)  VISIBLE;

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_Confirmed_Tenants` 
CHANGE COLUMN `VendorID` `VendorID` VARCHAR(500) NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_Confirmed_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) VISIBLE,
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;
;

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_Confirmed_Tenants_Fields_Reference` 
CHANGE COLUMN `VendorID` `VendorID` VARCHAR(500) NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_Confirmed_Tenants_Fields_Reference`  
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_Confirmed_Tenants` 
ADD COLUMN `BatchID` INT NULL DEFAULT NULL AFTER `Tenant_Stage_ID`;

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_Overture_Tenants` 
CHANGE COLUMN `VendorID` `VendorID` VARCHAR(500) NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_ZoomInfo_Tenants` 
ADD INDEX `idx_main_ID` (`main_ID` ASC) VISIBLE;

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_Overture_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) VISIBLE,
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE,
ADD INDEX `idx_main_ID` (`main_ID` ASC) VISIBLE;

ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_GoogleMaps_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) VISIBLE,
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE,
ADD INDEX `idx_main_ID` (`main_ID` ASC) VISIBLE;

-- ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_Confirmed_Tenants` 
-- ADD COLUMN `NationalID` VARCHAR(500) NULL AFTER `BatchID`;


ALTER TABLE `Empirical_DataStage`.`Silver_VIC_Raw_Confirmed_Tenants` 
ADD COLUMN `ProviderID` INT NULL DEFAULT NULL AFTER `BatchID`;
UPDATE Empirical_DataStage.Silver_VIC_Raw_Confirmed_Tenants set ProviderID=14;