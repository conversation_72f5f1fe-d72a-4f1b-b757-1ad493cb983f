drop temporary table if exists temp3000;
create temporary table temp3000(
select distinct a.* from Empirical_DataStage.Silver_VIC_Raw_Confirmed_Tenants a
inner join Empirical_Prod.Address b on a.PropertyID=b.ParentID and b.ParentTableID=1 and b.Sequence=1 and b.IsActive=1
where b.ZipCode=3000 and  a.BatchID in (59000,59100,59200)
);

update Empirical_DataStage.Silver_VIC_Raw_Confirmed_Tenants set BatchID=59500
where Silver_VIC_Raw_Confirmed_TenantsID in (select Silver_VIC_Raw_Confirmed_TenantsID from temp3000);

drop temporary table if exists tempLeaseCT;
create temporary table tempLeaseCT(
select distinct a.*,b.ConfirmedTenantID from Empirical_DataStage.Silver_VIC_Raw_Confirmed_Tenants a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and  a.BatchID in (59000,59100,59200)
);

drop temporary table if exists tempLeaseProp;
create temporary table tempLeaseProp(
select distinct a.PropertyID from Empirical_DataStage.Silver_VIC_Raw_Confirmed_Tenants a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and  a.BatchID in (59000,59100,59200)
);

drop temporary table if exists tempLeaseRawCT;
create temporary table tempLeaseRawCT(
select distinct a.* from Empirical_DataStage.Silver_VIC_Raw_Confirmed_Tenants a
inner join tempLeaseProp b on a.PropertyID=b.PropertyID and  a.BatchID in (59000,59100,59200)
);


drop temporary table if exists tempLeaseTenants;
create temporary table tempLeaseTenants(
select b.* from Empirical_DataStage.Tenants_Stage as a
inner join Empirical_DataStage.Silver_VIC_Raw_Confirmed_Tenants b on a.VendorID=b.VendorID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where a.BranchID in (select CompanyID from Empirical_Tenants.ConfirmedTenants 
    where ConfirmedTenantID in (select distinct ConfirmedTenantID from tempLeaseCT)) and a.ProviderID=15 and b.BatchID in (59000,59100,59200)
);

/*

update Empirical_DataStage.Silver_VIC_Raw_Confirmed_Tenants set BatchID=59600 -- Existing Tenants with Lease
where Silver_VIC_Raw_Confirmed_TenantsID in (select Silver_VIC_Raw_Confirmed_TenantsID from tempLeaseTenants);*/

