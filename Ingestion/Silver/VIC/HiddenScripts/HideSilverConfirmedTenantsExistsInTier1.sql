drop temporary table if exists tempCTNames;
create temporary table tempCTNames(
SELECT 
		 ct.CompanyName,P.PropertyID,`ct`.`ConfirmedTenantID` from  `Empirical_Prod`.`Property` `P`
		inner JOIN `Empirical_Prod`.`SuiteTenant` `st` ON `st`.`PropertyID`=`P`.`PropertyID`AND (`st`.`IsActive`=1) 
							AND (`st`.`TenantStatusID`=1)							
		inner join `Empirical_Tenants`.`ConfirmedTenants` ct on `st`.`ConfirmedTenantID`=`ct`.`ConfirmedTenantID` and ct.ProviderID=1
		inner join `Empirical_Prod`.`Company` c on `c`.`CompanyName`=`ct`.`CompanyName`)        ;

drop temporary table if exists tempSilveTenantName;
create temporary table tempSilveTenantName(
select TenantName as SilverTenantName,VendorID as SilverVendorID,PropertyID as SilverPropertyID from Empirical_DataStage.Silver_VIC_Raw_Confirmed_Tenants
);

drop table if exists Empirical_DataStage.SilverVICDuplicateTenantName;
create table Empirical_DataStage.SilverVICDuplicateTenantName(
select * from tempCTNames as tCTN
inner join tempSilveTenantName tSTN on tCTN.CompanyName =  tSTN.SilverTenantName and tCTN.PropertyID =  tSTN.SilverPropertyID 
);

select * from Empirical_DataStage.SilverVICDuplicateTenantName;

drop temporary table if exists temp1;
create temporary table temp1(
select distinct ts.BranchID from Empirical_DataStage.Tenants_Stage as ts
inner join Empirical_DataStage.SilverVICDuplicateTenantName as svdt on svdt.SilverVendorID=ts.VendorID where ts.BranchID is not null
);

UPDATE Empirical_Prod.Company 
SET 
   ModifiedBy=22,
   ModifiedDate= CURRENT_TIMESTAMP(),
    IsHidden = 1,
    HidedBy = 22,
    HidedDate = CURRENT_TIMESTAMP(),
    HideReasonID = 3,
    SubHideReasonID = NULL,
    HideReasonComments = 'Hiding Silver Confirmed Tenants which are already exist in Tier1'
WHERE
    CompanyID IN (select BranchID from temp1)
    AND (IsHidden=0 or IsHidden is null);
