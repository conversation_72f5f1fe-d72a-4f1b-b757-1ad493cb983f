drop temporary table if exists temp1;
create temporary table temp1(
select a.Vendor<PERSON>,a.main_ID from Empirical_DataStage.Silver_NSW_Raw_Overture_Tenants a inner join Empirical_DataStage.NSW_Raw_Overture_Tenants b
on a.VendorID=b.VendorID
union
select a.VendorID,a.main_ID from Empirical_DataStage.Silver_NSW_Raw_GoogleMaps_Tenants a inner join Empirical_DataStage.NSW_Raw_GoogleMaps_Tenants b
on a.VendorID=b.VendorID
);

drop temporary table if exists temp2;
create temporary table temp2(
select a.VendorID from Empirical_DataStage.Silver_NSW_Raw_Confirmed_Tenants a inner join Empirical_DataStage.NSW_Raw_ZoomInfo_Tenants b
on a.VendorID=b.VendorID
);

select count(*) from Empirical_DataStage.Silver_NSW_Raw_Confirmed_Tenants as a
-- left join temp1 b on a.VendorID=b.main_ID
left join temp2 c on a.VendorID=c.VendorID where
 -- b.main_ID is  null and
 c.Vendor<PERSON> is  not null;

drop temporary table if exists temp5;
create temporary table temp5 (
select distinct Silver_NSW_Raw_Confirmed_TenantsID from Empirical_DataStage.Silver_NSW_Raw_Confirmed_Tenants as a
left join temp1 b on a.VendorID=b.main_ID
-- left join temp2 c on a.VendorID=c.VendorID 
where 
b.main_ID is not null);
-- and c.VendorID is  null;


update Empirical_DataStage.Silver_NSW_Raw_Confirmed_Tenants as a
 left join temp2 c on a.VendorID=c.VendorID
set BatchID=69400 where  c.VendorID is not null;

update Empirical_DataStage.Silver_NSW_Raw_Confirmed_Tenants as a
left join temp1 b on a.VendorID=b.main_ID
set BatchID=69400 where a.Silver_NSW_Raw_Confirmed_TenantsID in (select * from temp5);


select count(*),BatchID from Empirical_DataStage.Silver_NSW_Raw_Confirmed_Tenants group by BatchID;
