

 select count(*), BatchID from Empirical_DataStage.Silver_NSW_Raw_Confirmed_Tenants group by BatchID;


-- For Tenants having Lease with new property before ingestion


drop temporary table if exists temp1;
create temporary table temp1(
select a.*,b.BranchID from Empirical_DataStage.Silver_NSW_Raw_Confirmed_Tenants as a 
 inner join Empirical_DataStage.Tenants_Stage b on a.VendorID=b.VendorID
 where b.ProviderID=15 and a.BatchID  in (69000,69100,69200) and a.PropertyID!=b.PropertyID and b.BranchID is not null
 );
 
 
drop temporary table if exists temp2;
create temporary table temp2(
 select a.* from temp1 a inner join Empirical_Tenants.ConfirmedTenants b on a.BranchID=b.CompanyID and b.ProviderID=1 and b.IsActive=1
 inner join Empirical_Prod.Lease c on c.ConfirmedTenantID=b.ConfirmedTenantID and a.PropertyID=c.PropertyID and
 -- c.TransactionOriginationTypeID=1 
  c.IsActive=1
 );
 
 update Empirical_DataStage.Silver_NSW_Raw_Confirmed_Tenants set BatchID=89600 where Vendor<PERSON> in (Select VendorID from temp2);
 