

drop temporary table if exists zoominfoDuplicatewithcompanyandpropertyID;
create temporary table zoominfoDuplicatewithcompanyandpropertyID(
SELECT *
FROM Empirical_DataStage.Silver_NSW_Raw_ZoomInfo_Tenants
WHERE (CompanyID, PropertyID) IN (
    SELECT CompanyID, PropertyID
    FROM Empirical_DataStage.Silver_NSW_Raw_ZoomInfo_Tenants
    -- WHERE ProviderID = 15
    GROUP BY CompanyID, PropertyID
    HAVING COUNT(*) > 1
) AND ProviderID = 15
-- ORDER BY ZI_C_LAST_UPDATED_DATE ASC
-- LIMIT 1
);

desc zoominfoDuplicatewithcompanyandpropertyID ;

select count(*), CompanyID,PropertyID# VendorID,  CompanyID, PropertyID,TenantName, ZI_C_LAST_UPDATED_DATE 
 from zoominfoDuplicatewithcompanyandpropertyID group by CompanyID,PropertyID;


drop temporary table if exists zoominfoDuplicatewithcompanyandpropertyIDlRAEq1;
create temporary table zoominfoDuplicatewithcompanyandpropertyIDlRAEq1(
SELECT VendorID, CompanyID, PropertyID, TenantName, ZI_C_LAST_UPDATED_DATE
FROM (
    SELECT VendorID, CompanyID, PropertyID, TenantName, ZI_C_LAST_UPDATED_DATE,
           ROW_NUMBER() OVER (PARTITION BY CompanyID, PropertyID ORDER BY ZI_C_LAST_UPDATED_DATE) AS rn
    FROM zoominfoDuplicatewithcompanyandpropertyID
) AS ranked
WHERE rn =1
);


drop temporary table if exists zoominfoDuplicatewithcompanyandpropertyIDlRAGt1;
create temporary table zoominfoDuplicatewithcompanyandpropertyIDlRAGt1(
SELECT VendorID, CompanyID, PropertyID, TenantName, ZI_C_LAST_UPDATED_DATE
FROM (
    SELECT VendorID, CompanyID, PropertyID, TenantName, ZI_C_LAST_UPDATED_DATE,
           ROW_NUMBER() OVER (PARTITION BY CompanyID, PropertyID ORDER BY ZI_C_LAST_UPDATED_DATE) AS rn
    FROM zoominfoDuplicatewithcompanyandpropertyID
) AS ranked
WHERE rn >1
);

drop temporary table if exists tempTSNSW;
create temporary table tempTSNSW(
select distinct ts.BranchID from Empirical_DataStage.Tenants_Stage ts 
inner join zoominfoDuplicatewithcompanyandpropertyIDlRAGt1 as gt1 on gt1.VendorID=ts.VendorID
);
select count(*) from tempTSNSW;

UPDATE Empirical_Prod.Company 
SET 
   ModifiedBy=22,
   ModifiedDate= CURRENT_TIMESTAMP(),
    IsHidden = NULL,
    HidedBy = NULL,
    HidedDate = NULL,
    HideReasonID = NULL,
    SubHideReasonID = NULL,
    HideReasonComments = 'Hiding Silver Confirmed Tenants which are duplicate from the source'
WHERE
    CompanyID IN (select BranchID from tempTSNSW);

