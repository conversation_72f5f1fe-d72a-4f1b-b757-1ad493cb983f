CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Property_Tenant_Matrix_Save_MergedView_Changelog_1`(
IN P_DataJason text,
IN P_LoginEntityID INT)
BEGIN
DECLARE x INT DEFAULT 0;
DECLARE y INT DEFAULT 0;
	
    -- Get the array length to loop
    SELECT JSON_LENGTH(P_DataJason) INTO @listcount;
    SET @Counter = 0;
    
    DROP TEMPORARY TABLE IF EXISTS tempChangeLog;
    CREATE TEMPORARY TABLE tempChangeLog 
    (
		ConfirmedTenantFieldAuditID INT,
		ConfirmedTenantID INT,
        ProviderID INT,
        Source_Tenant_Record_ID INT,
        FieldID INT,
        CreatedBy INT,
        ModifiedBy INT,
        IsActive TINYINT(1)
    );
    
    While @Counter < @listcount DO
		SELECT 
        JSON_UNQUOTE(JSON_EXTRACT (P_DataJason, concat('$[',@Counter,'].ConfirmedTenantID')))  ConfirmedTenantID ,
        JSON_UNQUOTE(JSON_EXTRACT (P_DataJason, concat('$[',@Counter,'].ProviderID'))) as ProviderID ,
        JSON_UNQUOTE(JSON_EXTRACT (P_DataJason, concat('$[',@Counter,'].Source_Tenant_Record_ID'))) as Source_Tenant_Record_ID ,
        JSON_UNQUOTE(JSON_EXTRACT (P_DataJason, concat('$[',@Counter,'].FieldID'))) as FieldID 
        INTO  @ConfirmedTenantID,@ProviderID,@Source_Tenant_Record_ID,@FieldID;
        -- Check if active change log already exists for the same provider and field for the same merged view.
        IF EXISTS 
        (
				SELECT 1 From Empirical_Tenants.ConfirmedTenantsFieldAudit 
				Where 
					ConfirmedTenantID=@ConfirmedTenantID
				-- AND ProviderID=@ProviderID
				AND FieldID=@FieldID
				AND IsActive=1 
		) THEN
				-- 1. If Found, get the Primary Key of the change log field
				SELECT ConfirmedTenantFieldAuditID INTO @ConfirmedTenantFieldAuditID From Empirical_Tenants.ConfirmedTenantsFieldAudit 
				Where 
					ConfirmedTenantID=@ConfirmedTenantID
				-- AND ProviderID=@ProviderID
				AND FieldID=@FieldID
				AND IsActive=1
                LIMIT 1;
                
				-- 2. Change log entry already exists. so prepare data to mark the existing one as inactive and create new one to maintain history
				INSERT INTO tempChangeLog
				Select @ConfirmedTenantFieldAuditID,@ConfirmedTenantID,@ProviderID,@Source_Tenant_Record_ID,@FieldID,P_LoginEntityID,P_LoginEntityID,0;
				
                INSERT INTO tempChangeLog
				Select @ConfirmedTenantFieldAuditID,@ConfirmedTenantID,@ProviderID,@Source_Tenant_Record_ID,@FieldID,P_LoginEntityID,P_LoginEntityID,1;
        ELSE
			 INSERT INTO tempChangeLog
			 Select @ConfirmedTenantFieldAuditID,@ConfirmedTenantID,@ProviderID,@Source_Tenant_Record_ID,@FieldID,P_LoginEntityID,P_LoginEntityID,1;
        END IF;
        
        SET @Counter = @Counter + 1;
	END WHILE;
    
    -- Inactivate all changlog fields
	UPDATE Empirical_Tenants.ConfirmedTenantsFieldAudit  A join tempChangeLog T on A.ConfirmedTenantFieldAuditID=T.ConfirmedTenantFieldAuditID
    SET A.IsActive=0, A.ModifiedBy=T.ModifiedBy, A.ModifiedDate=current_timestamp()
    Where T.IsActive=0;
    
    -- Insert new change log fields
    INSERT INTO Empirical_Tenants.ConfirmedTenantsFieldAudit     
    select null,ConfirmedTenantID,ProviderID,Source_Tenant_Record_ID,FieldID,CreatedBy,current_timestamp(),ModifiedBy,current_timestamp(),IsActive 
    from tempChangeLog 
    Where IsActive=1
    GROUP BY ConfirmedTenantID,ProviderID,FieldID,IsActive;
    
    
    DROP TEMPORARY TABLE IF EXISTS tempChangeLog;
END