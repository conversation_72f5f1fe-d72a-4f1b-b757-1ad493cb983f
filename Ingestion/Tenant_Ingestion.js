const sql = require('mysql');

// Database configuration
const config = {
  user: 'imperium_admin',
  password: 'CoVayKfSgNgq6n8HJU4O',
  server: 'localhost',
  database: 'Empirical_Prod',
  port: 3391,
  options: {
    encrypt: true, // For SQL Server
  },
};

// Stored procedure name
const tenantStageImportStoredProcedureName = 'CRE_Tenant_Stage_Import_DataSet_V1';
const verifiedTenantImportStoredProcudureName = 'CRE_Verified_Tenant_Import_V1';

const tenantIngestion = async (BatchID, StateID, MetroId, Bucket, State) => {

  return new Promise(function (resolve, reject) {
    try {
      //connect to the database

      console.log(BatchID, "Started");

      const pool = sql.createPool(config);
      let spcall1 = `CALL Empirical_DataStage.${tenantStageImportStoredProcedureName}(${BatchID},"${State}", "${Bucket}");`;
      pool.getConnection(async function (error, connection) {
        if (connection) {
          connection.query(spcall1, async (error, results) => {
            if (error) {
              //  console.log(spcall1, 'Error', error);
              resolve("reject");
            }
            if (results) {
              console.log(spcall1, 'Success', new Date());
              //connection.query(`SET SESSION group_concat_max_len = 1000000;`);
              let group_concat_max_len_query = `SET SESSION group_concat_max_len = 1000000;`;
              connection.query(group_concat_max_len_query, (error, results) => {

                console.log("Set session Started", new Date());
                if (error) {
                  //  console.log(group_concat_max_len_query, 'Error', error);
                  resolve(error);
                }
                // console.log(results);
                if (results) {
                  console.log("Stage2 Started", new Date());
                  let spcall = `CALL Empirical_DataStage.${verifiedTenantImportStoredProcudureName}(${BatchID},${StateID},14,14,${MetroId}, "${State}", "${Bucket}" );`;
                  connection.query(spcall, (error, results) => {
                    if (error) {
                      //   console.log(spcall, 'Error', error);
                      resolve(error);
                    }
                    if (results) {
                      console.log(spcall, 'Success', new Date());
                      resolve(results);
                    }
                  });
                }
              });
            }
          });
          connection.release();
        } else if (error) {
          // console.log(spcall1, 'Error', error);
          resolve(error);
        }
      });
    } catch (error) {
      // console.log(spcall1, 'Error', error);
      resolve(error);
    }
  });
}





// Function to iterate over the array of objects and call tenantIngestion for each object
const processBatchObjects = async (batchObjects) => {
  try {
    for (const batchObject of batchObjects) {
      const { BatchID, StateID, MetroId, Bucket, State } = batchObject;
      try {
        const response = await tenantIngestion(BatchID, StateID, MetroId, Bucket, State);
        console.log(response);
      } catch (error) {
        console.error('Error processing objects:', error);
      }
    }
    console.log('All Batches processed successfully.');
  } catch (error) {
    console.error('Error processing objects:', error);
  }
};


const batchObjects = [
  // { BatchID: 142336400, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' }, rows
  // { BatchID: 142336401, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
  // { BatchID: 142336402, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
  // { BatchID: 142336403, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
  // { BatchID: 142336404, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
  // { BatchID: 142336405, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
  // { BatchID: 142336406, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
  // { BatchID: 142336407, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' }, rows
  // { BatchID: 142336408, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
  // { BatchID: 142336409, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
  // { BatchID: 142336410, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
 // { BatchID: 142336411, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
  // { BatchID: 142335400, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' }, rows
  // { BatchID: 142335401, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
  // { BatchID: 142335402, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
  // { BatchID: 142335403, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
  // { BatchID: 142335404, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
  // { BatchID: 142335405, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
  // { BatchID: 142335406, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
  // { BatchID: 142335407, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
  // { BatchID: 142335408, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
  // { BatchID: 142335409, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
  // { BatchID: 142335410, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' }, - rows

  // { BatchID: 142335411, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
  // { BatchID: 142335412, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
  // { BatchID: 142335413, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
  // { BatchID: 142337400, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
  // { BatchID: 142337401, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
  // { BatchID: 142337402, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
  // { BatchID: 142337403, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
  // { BatchID: 142337404, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
  // { BatchID: 142337405, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
  // { BatchID: 142337406, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
  //{ BatchID: 142337407, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' }

 // tested-tested-tested
 // start done
//   { BatchID: 442335000, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' }, 
// { BatchID: 442335001, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335002, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335003, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335004, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335005, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335006, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335007, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335008, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335009, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335010, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335011, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335012, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335013, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335014, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335015, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335016, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335017, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335018, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335019, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335020, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335021, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335022, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335023, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335024, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335025, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335026, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335027, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335028, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335029, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335030, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335031, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335032, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335033, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335100, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335101, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335102, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335103, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335104, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335105, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335106, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335107, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335108, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335109, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335110, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335111, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335112, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335113, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335114, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335115, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
//  { BatchID: 442335116, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335117, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335118, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335119, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335120, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442335121, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Gold' },
// { BatchID: 442336000, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336001, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336002, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336003, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336004, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336005, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336006, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336007, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336008, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336009, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336010, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336011, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336012, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336013, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336014, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336015, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336016, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336017, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336018, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336019, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336020, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336021, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336022, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336023, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336024, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336025, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336026, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336027, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336028, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336029, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336030, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336031, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336100, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336101, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336102, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336103, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336104, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336105, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336106, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336107, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336108, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336109, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336110, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336111, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336112, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336113, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336114, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336115, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336116, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336117, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336118, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },
// { BatchID: 442336119, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Gold' },


// { BatchID: 442337000, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337001, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337002, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337003, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337004, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337005, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337006, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337007, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337008, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337009, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337010, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337011, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337012, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337013, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337014, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337015, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337100, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337101, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337102, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337103, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337104, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337105, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337106, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337107, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337108, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },
// { BatchID: 442337109, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Gold' },

// { BatchID: 442345000, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345001, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345002, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345003, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345004, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345005, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345006, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345007, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345008, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345009, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345010, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345011, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// // { BatchID: 442345012, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345013, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345014, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345015, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345016, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345017, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345018, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345019, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345020, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345021, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345022, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345100, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345101, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345102, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345103, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345104, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345105, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345106, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345107, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345108, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345109, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345110, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345111, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },

// done 

// { BatchID: 442345112, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345113, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345114, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345115, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345116, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345117, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345118, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345119, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345120, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },

// done in the morning 
// { BatchID: 442345121, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345122, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345123, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345124, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345125, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },
// { BatchID: 442345126, StateID: 57, State: 'NSW', MetroId: 348, Bucket: 'Silver' },

// { BatchID: 442346000, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346001, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346002, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346003, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346004, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346005, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346006, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346007, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346008, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346009, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346010, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346011, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346012, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346013, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346014, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346015, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346016, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346017, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346018, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346019, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346020, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346021, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346022, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346023, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346024, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346025, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346026, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346027, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346100, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346101, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346102, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346103, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346104, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346105, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346106, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346107, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346108, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },
// { BatchID: 442346109, StateID: 59, State: 'VIC', MetroId: 349, Bucket: 'Silver' },



// { BatchID: 442347000, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347001, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347002, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347003, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347004, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347005, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347006, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347007, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347008, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347009, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347010, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347011, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347012, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347013, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347014, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347015, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347016, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347017, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347100, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347101, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347102, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347103, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347104, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347105, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347106, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347107, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347108, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' },
// { BatchID: 442347109, StateID: 58, State: 'QLD', MetroId: 350, Bucket: 'Silver' }


];

processBatchObjects(batchObjects);