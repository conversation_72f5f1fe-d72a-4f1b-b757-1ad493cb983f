
--
-- Table structure for table `Tenant_Ingestion_Data`
--

DROP TABLE IF EXISTS `Tenant_Ingestion_Data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Tenant_Ingestion_Data` (
  `ABN` bigint DEFAULT NULL,
  `ABNStatus` text,
  `ABN_StatusFromDate` text,
  `ACN` double DEFAULT NULL,
  `ANZSICCode` double DEFAULT NULL,
  `ASICEntityClass` text,
  `ASICEntityStatus` text,
  `ASICEntityType` text,
  `Address1` text,
  `Address2` text,
  `CEOName` text,
  `CEOTitle` text,
  `City` text,
  `Country` double DEFAULT NULL,
  `DomesticParentCountry` text,
  `DomesticParentDUNS` double DEFAULT NULL,
  `DomesticParentName` text,
  `Email` text,
  `EmployeeCount` double DEFAULT NULL,
  `EmployeeIndicator` text,
  `EmployeesAtLocation` double DEFAULT NULL,
  `EntityAge` double DEFAULT NULL,
  `Fax` text,
  `GST_Status` text,
  `GST_StatusFromDate` text,
  `GlobalUltimateParentCountry` text,
  `GlobalUltimateParentDUNS` double DEFAULT NULL,
  `GlobalUltimateParentName` text,
  `HQ_CompanyName` text,
  `HQ_ID` double DEFAULT NULL,
  `ImmediateParentCountry` text,
  `ImmediateParentDUNS` double DEFAULT NULL,
  `ImmediateParentName` text,
  `Latitude` double DEFAULT NULL,
  `LegalStatus` text,
  `LineOfBusiness` text,
  `Longitude` double DEFAULT NULL,
  `MarketableFlag` bigint DEFAULT NULL,
  `NumberofMembersinHierarchy` double DEFAULT NULL,
  `OfficePhone` text,
  `PostalCode` bigint DEFAULT NULL,
  `PrimarySIC2Digit` double DEFAULT NULL,
  `PrimarySIC2DigitDesc` text,
  `PrimarySIC3Digit` double DEFAULT NULL,
  `PrimarySIC3DigitDesc` text,
  `PrimarySICDesc` text,
  `PrimarySICDivision` text,
  `PrimarySICDivisionDesc` text,
  `PropertyID` bigint DEFAULT NULL,
  `RegistrationOrIncorporationDate` text,
  `Revenue` double DEFAULT NULL,
  `RevenueIndicator` text,
  `SICCode` double DEFAULT NULL,
  `State` double DEFAULT NULL,
  `StateAbbr` text,
  `SubsidiaryCode` double DEFAULT NULL,
  `TenantName` text,
  `VendorID` bigint DEFAULT NULL,
  `WebsiteURL` text,
  `MatchingScore_zoominfo` double DEFAULT NULL,
  `MatchingScore_google_maps` double DEFAULT NULL,
  `MatchingScore_overture` double DEFAULT NULL,
  `Tenant_Ingestion_Data_ID` int NOT NULL AUTO_INCREMENT,
  `BatchID` int DEFAULT '1000',
  `CountryCode` int DEFAULT NULL,
  `ProviderID` int DEFAULT NULL,
  `NationalID` varchar(500) DEFAULT NULL,
  `Tenant_Stage_ID` int DEFAULT NULL,
  PRIMARY KEY (`Tenant_Ingestion_Data_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=10930 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Tenant_Ingestion_Fileds_Providers_Data`
--

DROP TABLE IF EXISTS `Tenant_Ingestion_Fileds_Providers_Data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Tenant_Ingestion_Fileds_Providers_Data` (
  `ABN` text,
  `ABNStatus` text,
  `ABN_StatusFromDate` text,
  `ACN` text,
  `ANZSICCode` text,
  `ASICEntityClass` text,
  `ASICEntityStatus` text,
  `ASICEntityType` text,
  `Address1` text,
  `Address2` text,
  `CEOName` text,
  `CEOTitle` text,
  `City` text,
  `Country` double DEFAULT NULL,
  `DomesticParentCountry` text,
  `DomesticParentDUNS` text,
  `DomesticParentName` text,
  `Email` text,
  `EmployeeCount` text,
  `EmployeeIndicator` text,
  `EmployeesAtLocation` double DEFAULT NULL,
  `EntityAge` text,
  `Fax` text,
  `GST_Status` text,
  `GST_StatusFromDate` text,
  `GlobalUltimateParentCountry` text,
  `GlobalUltimateParentDUNS` text,
  `GlobalUltimateParentName` text,
  `HQ_CompanyName` text,
  `HQ_ID` text,
  `ImmediateParentCountry` text,
  `ImmediateParentDUNS` text,
  `ImmediateParentName` text,
  `Latitude` text,
  `LegalStatus` text,
  `LineOfBusiness` text,
  `Longitude` text,
  `MarketableFlag` text,
  `NumberofMembersinHierarchy` text,
  `OfficePhone` text,
  `PostalCode` text,
  `PrimarySIC2Digit` text,
  `PrimarySIC2DigitDesc` text,
  `PrimarySIC3Digit` text,
  `PrimarySIC3DigitDesc` text,
  `PrimarySICDesc` text,
  `PrimarySICDivision` text,
  `PrimarySICDivisionDesc` text,
  `PropertyID` text,
  `RegistrationOrIncorporationDate` text,
  `Revenue` text,
  `RevenueIndicator` text,
  `SICCode` text,
  `State` double DEFAULT NULL,
  `StateAbbr` text,
  `SubsidiaryCode` text,
  `TenantName` text,
  `VendorID` bigint DEFAULT NULL,
  `WebsiteURL` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Tenant_Stage_Illion_NSW`
--

DROP TABLE IF EXISTS `Tenant_Stage_Illion_NSW`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Tenant_Stage_Illion_NSW` (
  `VendorID` bigint DEFAULT NULL,
  `TenantName` text,
  `TradingNames` text,
  `Address1` text,
  `Address2` text,
  `City` text,
  `State` double DEFAULT NULL,
  `StateAbbr` text,
  `PostalCode` double DEFAULT NULL,
  `NationalID` double DEFAULT NULL,
  `OfficePhone` double DEFAULT NULL,
  `Fax` double DEFAULT NULL,
  `CEOName` text,
  `CEOTitle` text,
  `LineOfBusiness` text,
  `SICCode` double DEFAULT NULL,
  `Revenue` double DEFAULT NULL,
  `EmployeesAtLocation` double DEFAULT NULL,
  `EmployeeCount` double DEFAULT NULL,
  `LegalStatus` text,
  `StatusCode` double DEFAULT NULL,
  `SubsidiaryCode` double DEFAULT NULL,
  `ConfirmedTenantID` double DEFAULT NULL,
  `PropertyID` bigint DEFAULT NULL,
  `MatchingScore` double DEFAULT NULL,
  `CreatedDate` double DEFAULT NULL,
  `ModifiedDate` double DEFAULT NULL,
  `Latitude` double DEFAULT NULL,
  `Longitude` double DEFAULT NULL,
  `ParentCompanyID` double DEFAULT NULL,
  `BranchID` double DEFAULT NULL,
  `BatchID` double DEFAULT NULL,
  `ProviderID` double DEFAULT NULL,
  `Provider` text,
  `IsDefault` double DEFAULT NULL,
  `NAICSCode` double DEFAULT NULL,
  `NACECode` double DEFAULT NULL,
  `Email` text,
  `WebsiteURL` text,
  `ModifiedBy` double DEFAULT NULL,
  `IsHidden` double DEFAULT NULL,
  `IsDeleted` double DEFAULT NULL,
  `HidedBy` double DEFAULT NULL,
  `HidedDate` double DEFAULT NULL,
  `HideReasonID` double DEFAULT NULL,
  `HideReasonComments` double DEFAULT NULL,
  `ASICEntityStatus` text,
  `ASICEntityType` text,
  `ASICEntityClass` text,
  `ABNStatus` text,
  `ABN_StatusFromDate` text,
  `GST_Status` text,
  `GST_StatusFromDate` text,
  `RegistrationOrIncorporationDate` text,
  `EntityAge` double DEFAULT NULL,
  `EmployeeIndicator` text,
  `RevenueIndicator` text,
  `HQ_ID` double DEFAULT NULL,
  `HQ_CompanyName` double DEFAULT NULL,
  `NumberofMembersinHierarchy` double DEFAULT NULL,
  `ImmediateParentDUNS` double DEFAULT NULL,
  `ImmediateParentName` text,
  `ImmediateParentCountry` text,
  `DomesticParentDUNS` double DEFAULT NULL,
  `DomesticParentName` text,
  `DomesticParentCountry` text,
  `GlobalUltimateParentDUNS` double DEFAULT NULL,
  `GlobalUltimateParentName` text,
  `GlobalUltimateParentCountry` text,
  `PrimarySICDesc` text,
  `PrimarySIC3Digit` double DEFAULT NULL,
  `PrimarySIC3DigitDesc` text,
  `PrimarySIC2Digit` double DEFAULT NULL,
  `PrimarySIC2DigitDesc` text,
  `PrimarySICDivision` text,
  `PrimarySICDivisionDesc` text,
  `SubHideReasonID` double DEFAULT NULL,
  `ANZSICCode` double DEFAULT NULL,
  `CountryCode` int DEFAULT '14'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Tenants_Stage`
--

DROP TABLE IF EXISTS `Tenants_Stage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Tenants_Stage` (
  `VendorID` varchar(50) DEFAULT NULL,
  `TenantName` varchar(225) DEFAULT NULL,
  `Address1` varchar(255) DEFAULT NULL,
  `Address2` varchar(255) DEFAULT NULL,
  `City` varchar(255) DEFAULT NULL,
  `State` varchar(50) DEFAULT NULL,
  `StateAbbr` varchar(50) DEFAULT NULL,
  `CountryCode` varchar(50) DEFAULT NULL,
  `PostalCode` varchar(50) DEFAULT NULL,
  `NationalID` varchar(255) DEFAULT NULL,
  `OfficePhone` varchar(50) DEFAULT NULL,
  `Fax` varchar(50) DEFAULT NULL,
  `CEOName` varchar(255) DEFAULT NULL,
  `CEOTitle` varchar(50) DEFAULT NULL,
  `LineOfBusiness` varchar(50) DEFAULT NULL,
  `SICCode` varchar(50) DEFAULT NULL,
  `Revenue` decimal(14,2) DEFAULT NULL,
  `EmployeesAtLocation` int DEFAULT NULL,
  `EmployeeCount` int DEFAULT NULL,
  `LegalStatus` varchar(50) DEFAULT NULL,
  `StatusCode` varchar(50) DEFAULT NULL,
  `SubsidiaryCode` varchar(50) DEFAULT NULL,
  `IsProcessed` tinyint DEFAULT NULL,
  `ConfirmedTenantID` int DEFAULT NULL,
  `PropertyID` int DEFAULT NULL,
  `MatchingScore` int DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `ModifiedDate` datetime DEFAULT NULL,
  `Tenant_Stage_Id` int NOT NULL AUTO_INCREMENT,
  `Latitude` decimal(21,14) DEFAULT NULL,
  `Longitude` decimal(21,14) DEFAULT NULL,
  `ParentCompanyID` int DEFAULT NULL,
  `BranchID` int DEFAULT NULL,
  `BatchID` int DEFAULT NULL,
  `ProviderID` int DEFAULT NULL,
  `IsDefault` tinyint(1) DEFAULT NULL,
  `NAICSCode` varchar(255) DEFAULT NULL,
  `NACECode` varchar(255) DEFAULT NULL,
  `Email` varchar(255) DEFAULT NULL,
  `WebsiteURL` varchar(255) DEFAULT NULL,
  `ModifiedBy` int DEFAULT NULL,
  `IsHidden` tinyint(1) DEFAULT NULL,
  `IsDeleted` tinyint(1) DEFAULT NULL,
  `HidedBy` int DEFAULT NULL,
  `HidedDate` datetime DEFAULT NULL,
  `HideReasonID` int DEFAULT NULL,
  `HideReasonComments` varchar(1000) DEFAULT NULL,
  `ASICEntityStatus` varchar(45) DEFAULT NULL,
  `ASICEntityType` varchar(45) DEFAULT NULL,
  `ASICEntityClass` varchar(45) DEFAULT NULL,
  `ABNStatus` varchar(45) DEFAULT NULL,
  `ABN_StatusFromDate` varchar(45) DEFAULT NULL,
  `GST_Status` varchar(45) DEFAULT NULL,
  `GST_StatusFromDate` varchar(45) DEFAULT NULL,
  `RegistrationOrIncorporationDate` varchar(45) DEFAULT NULL,
  `EntityAge` varchar(10) DEFAULT NULL,
  `EmployeeIndicator` varchar(45) DEFAULT NULL,
  `RevenueIndicator` varchar(45) DEFAULT NULL,
  `HQ_ID` varchar(45) DEFAULT NULL,
  `HQ_CompanyName` varchar(100) DEFAULT NULL,
  `NumberofMembersinHierarchy` varchar(45) DEFAULT NULL,
  `ImmediateParentDUNS` varchar(45) DEFAULT NULL,
  `ImmediateParentName` varchar(100) DEFAULT NULL,
  `ImmediateParentCountry` varchar(45) DEFAULT NULL,
  `DomesticParentDUNS` varchar(45) DEFAULT NULL,
  `DomesticParentName` varchar(100) DEFAULT NULL,
  `DomesticParentCountry` varchar(45) DEFAULT NULL,
  `GlobalUltimateParentDUNS` varchar(45) DEFAULT NULL,
  `GlobalUltimateParentName` varchar(100) DEFAULT NULL,
  `GlobalUltimateParentCountry` varchar(45) DEFAULT NULL,
  `PrimarySICDesc` varchar(45) DEFAULT NULL,
  `PrimarySIC3Digit` varchar(45) CHARACTER SET big5 DEFAULT NULL,
  `PrimarySIC3DigitDesc` varchar(45) DEFAULT NULL,
  `PrimarySIC2Digit` varchar(45) DEFAULT NULL,
  `PrimarySIC2DigitDesc` varchar(45) DEFAULT NULL,
  `PrimarySICDivision` varchar(45) DEFAULT NULL,
  `PrimarySICDivisionDesc` varchar(45) DEFAULT NULL,
  `SubHideReasonID` int DEFAULT NULL,
  `ANZSICCode` int DEFAULT NULL,
  PRIMARY KEY (`Tenant_Stage_Id`),
  KEY `idx_tenants_ConfirmedTeanntID_modifieddate` (`ConfirmedTenantID`,`ModifiedDate`),
  KEY `idx_tenants_latitude` (`Latitude`),
  KEY `idx_tenants_longitude` (`Longitude`),
  KEY `idx_tenants_providerid` (`ProviderID`),
  KEY `idx_tenantstage_stateabbr` (`StateAbbr`),
  KEY `idx_tenants_zipcode` (`PostalCode`),
  KEY `idx_tenants_latlong` (`Latitude`,`Longitude`),
  KEY `idx_tenants_isdeleted` (`IsDeleted`),
  KEY `idx_tenants_ishidden` (`IsHidden`),
  KEY `fk_tenantstage_hidebyby_idx` (`HidedBy`),
  KEY `fk_tenantstage_hidereason_idx` (`HideReasonID`),
  KEY `fk_tenantstage_propertyid_idx` (`PropertyID`),
  KEY `fk_tenantstage_modifiedby_idx` (`ModifiedBy`),
  KEY `idx_tenants_isprocessed_zipcode` (`PostalCode`,`Latitude`,`Longitude`),
  KEY `idx_tenants_PropertyID_isprocessed` (`IsProcessed`,`PropertyID`,`BranchID`),
  KEY `idex_tenants_confirmedtenantid` (`ConfirmedTenantID`),
  KEY `idx_tenants_isprocessed` (`IsProcessed`),
  KEY `idx_tenants_branchid` (`BranchID`),
  KEY `idx_tenants_Search_Filters` (`NationalID`,`SICCode`),
  KEY `idx_tenants_vendorid` (`VendorID`),
  KEY `idx_tenants_zipcode_propertyid` (`PostalCode`,`PropertyID`),
  KEY `idx_tenants_email` (`Email`),
  KEY `idx_tenants_officephone` (`OfficePhone`),
  KEY `fk_tenantstage_subhidereason_idx` (`SubHideReasonID`),
  KEY `fk_tenantstage_anzsiccode` (`ANZSICCode`),
  FULLTEXT KEY `idx_tenantstage_tenantname` (`TenantName`),
  FULLTEXT KEY `idx_tenantstage_address1` (`Address1`),
  FULLTEXT KEY `idx_teannts_nationalid` (`NationalID`),
  CONSTRAINT `fk_tenantsstage_confirmedtenantid` FOREIGN KEY (`ConfirmedTenantID`) REFERENCES `Empirical_Tenants`.`ConfirmedTenants` (`ConfirmedTenantID`),
  CONSTRAINT `fk_tenantsstage_providerid` FOREIGN KEY (`ProviderID`) REFERENCES `Empirical_Prod`.`Providers` (`ProviderID`),
  CONSTRAINT `fk_tenantstage_anzsiccode` FOREIGN KEY (`ANZSICCode`) REFERENCES `Empirical_Prod`.`ANZSICCodes` (`Code`),
  CONSTRAINT `fk_tenantstage_hidebyby` FOREIGN KEY (`HidedBy`) REFERENCES `Empirical_Prod`.`Entity` (`EntityID`),
  CONSTRAINT `fk_tenantstage_hidereason` FOREIGN KEY (`HideReasonID`) REFERENCES `Empirical_Prod`.`HideReasons` (`HideReasonID`),
  CONSTRAINT `fk_tenantstage_modifiedby` FOREIGN KEY (`ModifiedBy`) REFERENCES `Empirical_Prod`.`Entity` (`EntityID`),
  CONSTRAINT `fk_tenantstage_propertyid` FOREIGN KEY (`PropertyID`) REFERENCES `Empirical_Prod`.`Property` (`PropertyID`),
  CONSTRAINT `fk_tenantstage_subhidereason` FOREIGN KEY (`SubHideReasonID`) REFERENCES `Empirical_Prod`.`SubHideReasons` (`SubHideReasonID`)
) ENGINE=InnoDB AUTO_INCREMENT=7030374 DEFAULT CHARSET=latin1;
