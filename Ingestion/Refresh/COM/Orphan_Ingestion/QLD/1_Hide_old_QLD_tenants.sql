drop temporary table if exists tempprop;
create temporary table tempprop(
select distinct PropertyID from Empirical_DataStage.Tenants_Stage 
);

drop temporary table if exists temp1;
create temporary table temp1(
select ts.* from tempprop ts 
inner join Empirical_Prod.Address a on a.ParentID=ts.PropertyID
where a.StateID=58
and a.Sequence=1 and a.IsActive=1 and a.ParentTableID=1
);

drop temporary table if exists tempTS;
create temporary table tempTS(
select VendorID,Tenant_Stage_Id,PropertyID,BranchID,ConfirmedTenantID from Empirical_DataStage.Tenants_Stage 
where PropertyID in (select * from temp1) or (State in (58))
);

drop temporary table if exists temp2;
create temporary table temp2(
select c.CompanyID from Empirical_Prod.Company c inner join Empirical_Prod.Address a on a.ParentID=c.CompanyID
where a.StateID=58
and a.Sequence=1 and a.IsActive=1 and a.ParentTableID=6
);

drop temporary table if exists temp3;
create temporary table temp3(
select ConfirmedTenantID,CompanyID from Empirical_Tenants.ConfirmedTenants
where CompanyID in (select * from temp2) and ProviderID=1
);


drop temporary table if exists tempLeaseCT;
create temporary table tempLeaseCT(
select * from Empirical_Prod.Lease 
where ConfirmedTenantID in (select ConfirmedTenantID from temp3) or ConfirmedTenantID in (select ConfirmedTenantID from tempTS) and IsActive=1
);

drop temporary table if exists tempFinalTS;
create temporary table tempFinalTS(
select * from tempTS where ConfirmedTenantID not in (select ConfirmedTenantID from tempLeaseCT) or ConfirmedTenantID is null
);

drop temporary table if exists tempFinalCT;
create temporary table tempFinalCT(
select * from temp3 where ConfirmedTenantID not in (select ConfirmedTenantID from tempLeaseCT) or ConfirmedTenantID is null
);


select count(*) from tempFinalTS where PropertyID is null;
select count(*) from tempFinalTS where PropertyID is not null and BranchID is null;
select count(*) from tempFinalTS where PropertyID is not null and (BranchID is not null or ConfirmedTenantID is not null);

drop temporary table if exists deleteOrphans;
create temporary table deleteOrphans(
select * from tempFinalTS where PropertyID is null
);

-- delete from Empirical_Prod.NationalIdentifiers where Tenant_Stage_ID in (select Tenant_Stage_ID from deleteOrphans);
-- delete from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select Tenant_Stage_ID from deleteOrphans) and PropertyID is null and CreatedDate<"2024-05-10" and (ModifiedDate<"2024-05-16" or ModifiedDate is null);

   /**
 UPDATE Empirical_Prod.Company 
SET 
    HidedBy = 22,
    HidedDate = CURRENT_TIMESTAMP(),
    HideReasonID = 8,
    HideReasonComments = 'Hiding old companies in QLD',
    ModifiedBy = 22,
    IsHidden = 1,
    ModifiedDate = CURRENT_TIMESTAMP()
WHERE
    CompanyID in (SELECT CompanyID FROM tempFinalCT);
    
    
    
    UPDATE Empirical_DataStage.Tenants_Stage 
SET 
    HidedBy = 22,
    HidedDate = CURRENT_TIMESTAMP(),
    HideReasonID = 8,
    HideReasonComments = 'Hiding old companies in QLD',
    ModifiedBy = 22,
    IsHidden = 1,
    ModifiedDate = CURRENT_TIMESTAMP()
WHERE
    Tenant_Stage_Id in (SELECT Tenant_Stage_Id FROM tempFinalTS) 
    and CreatedDate<"2024-05-10" and (ModifiedDate<"2024-05-16" or ModifiedDate is null)
    and (HideReasonID!=8 or HideReasonID is null);
    **/
    
     select count(*) from Empirical_DataStage.Tenants_Stage 
WHERE
    Tenant_Stage_Id in (SELECT Tenant_Stage_Id FROM tempFinalTS) 
    and CreatedDate<"2024-05-10" and (ModifiedDate<"2024-05-16" or ModifiedDate is null) -- 743740
    and (HideReasonID!=8 or HideReasonID is null); -- 0
