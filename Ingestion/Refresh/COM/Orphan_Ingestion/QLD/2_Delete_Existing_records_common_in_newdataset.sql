drop temporary table if exists tempD<PERSON><PERSON><PERSON><PERSON>han;
create temporary table tempDele<PERSON><PERSON><PERSON>han(
SELECT ts.Tenant_Stage_Id,bt.VendorID,ts.<PERSON>id<PERSON>,ts.State as TSState,bt.State as BTState FROM Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 bt inner join Empirical_DataStage.Tenants_Stage ts 
on bt.VendorID=ts.VendorID #where ts.BranchID is not null or ts.ConfirmedTenantID is not null
where #ts.IsHidden=1 and 
 bt.TSI_State='QLD'
);

drop temporary table if exists tempDeleteOrphanHidden;
create temporary table tempDeleteOrphanHidden(
SELECT ts.Tenant_Stage_Id,bt.VendorID,ts.IsHidden,ts.State as TSState,bt.State as BTState FROM Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 bt inner join Empirical_DataStage.Tenants_Stage ts 
on bt.VendorID=ts.VendorID #where ts.BranchID is not null or ts.ConfirmedTenantID is not null
where ts.<PERSON>Hidden=1 and 
 bt.TSI_State='QLD'
);

select count(*) from Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 bt inner join tempDeleteBronze t on t.VendorID=bt.VendorID where t.IsHidden is null or t.IsHidden=0;

-- update Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 bt inner join tempDeleteOrphan t on t.VendorID=bt.VendorID set BatchID=20240522 where (t.IsHidden is null or t.IsHidden=0) and  bt.TSI_State='QLD';

select * from Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 where VendorID in (select VendorID from tempDeleteBronze);
select * from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select Tenant_Stage_Id from tempDeleteOrphan);

select count(*),IsHidden from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select Tenant_Stage_Id from tempDeleteOrphan) group by IsHidden;


delete from Empirical_Prod.NationalIdentifiers where Tenant_Stage_ID in (select Tenant_Stage_Id from tempDeleteOrphanHidden);
delete from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select Tenant_Stage_Id from tempDeleteOrphanHidden);
