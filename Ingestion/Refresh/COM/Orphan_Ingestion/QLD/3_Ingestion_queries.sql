select count(*) from Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 group by VendorID having count(*)>1;

Update Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 set BatchID=null where TSI_State='QLD' and BatchID!=20240522;

drop  temporary table if exists  OrphanVendors;
create temporary table OrphanVendors(
SELECT  VendorID FROM Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 where Batch<PERSON> is null and TSI_State='QLD');

drop  temporary table if exists  OrphanduplicateVendors;
create temporary table OrphanduplicateVendors(
SELECT  VendorID FROM OrphanVendors group by VendorID having count(*)>1);


select count(*),BatchID from Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 where TSI_State='QLD' group by BatchID;


update Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 set State=58 where StateAbbr='QLD' and TSI_State='QLD';

UPDATE Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 as a
SET a.NationalID = 
    CASE
        WHEN a.ACN IS NOT NULL AND a.ABN IS NOT NULL THEN CONCAT('ACN: ', a.ACN, ' ABN: ', a.ABN)
        WHEN a.ACN IS NOT NULL THEN CONCAT('ACN: ', a.ACN)
        WHEN a.ABN IS NOT NULL THEN CONCAT('ABN: ', a.ABN)
        ELSE NULL
    END
WHERE TSI_State='QLD';


update Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 set BatchID=********* where BatchID is null and TSI_State='QLD';


select count(*),BatchID,IsProcessed from Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 where TSI_State='QLD' group by BatchID,IsProcessed;




drop temporary table if exists tempOrphan;
create temporary table tempOrphan(
SELECT ts.Tenant_Stage_Id,bt.VendorID,ts.IsHidden,ts.State as TSState,bt.State as BTState 
FROM Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 bt inner join Empirical_DataStage.Tenants_Stage ts 
on bt.VendorID=ts.VendorID where  bt.TSI_State='QLD' and bt.BatchID=*********
);




drop temporary table if exists tempOrphan;
create temporary table tempOrphan(
SELECT ts.Tenant_Stage_Id,bt.VendorID,ts.IsHidden,ts.State as TSState,bt.State as BTState 
FROM Empirical_DataStage.BT_Orphan_Tenants_2024_05_20 bt inner join Empirical_DataStage.Tenants_Stage ts 
on bt.VendorID=ts.VendorID where  bt.TSI_State='QLD' and bt.BatchID=*********
);


select count(*) from Empirical_DataStage.Tenants_Stage where State=58 and PropertyID is null and IsHidden is  null;

CALL CRE_Tenant_Stage_Orphan_DataSet_Batch(*********,100000);





call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(*********);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240522101);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240522102);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240522103);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240522104);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240522105);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240522106);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240522107);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240522108);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240522109);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240522110);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240522111);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240522112);