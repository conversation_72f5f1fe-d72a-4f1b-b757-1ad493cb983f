
select count(*) from Empirical_DataStage.BT_Orphan_Tenants_2024_05_17;

Update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set BatchID=null;

drop  temporary table if exists  OrphanVendors;
create temporary table OrphanVendors(
SELECT  VendorID FROM Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 where BatchID is null);

drop  temporary table if exists  OrphanduplicateVendors;
create temporary table OrphanduplicateVendors(
SELECT  VendorID FROM OrphanVendors group by VendorID having count(*)>1);


select count(*),BatchID from Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 group by BatchID;


update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set State=59 where StateAbbr='VIC';

UPDATE Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 as a
SET a.NationalID = 
    CASE
        WHEN a.ACN IS NOT NULL AND a.ABN IS NOT NULL THEN CONCAT('ACN: ', a.ACN, ' ABN: ', a.ABN)
        WHEN a.ACN IS NOT NULL THEN CONCAT('ACN: ', a.ACN)
        WHEN a.ABN IS NOT NULL THEN CONCAT('ABN: ', a.ABN)
        ELSE NULL
    END;


update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set BatchID=********* where BatchID is null;


select count(*),BatchID,IsProcessed from Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 group by BatchID,IsProcessed;


drop temporary table if exists tempOrphan;
create temporary table tempOrphan(
SELECT ts.Tenant_Stage_Id,bt.VendorID,ts.IsHidden,ts.State as TSState,bt.State as BTState FROM Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 bt inner join Empirical_DataStage.Tenants_Stage ts 
on bt.VendorID=ts.VendorID
);

update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set BatchID=20240517 where VendorID in (select VendorID from tempOrphan);



update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set BatchID=********* where BatchID=********* limit 100000;
update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set BatchID=********* where BatchID=********* limit 100000;
update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set BatchID=240517103 where BatchID=********* limit 100000;
update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set BatchID=240517104 where BatchID=********* limit 100000;
update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set BatchID=240517105 where BatchID=********* limit 100000;
update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set BatchID=240517106 where BatchID=********* limit 100000;
update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set BatchID=240517107 where BatchID=********* limit 100000;
update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set BatchID=240517108 where BatchID=********* limit 100000;
update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set BatchID=240517109 where BatchID=********* limit 100000;


select count(*) from Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 where BatchID in (*********,*********,*********,240517103,240517104,240517105,240517106,240517107,240517108,240517109);


select count(*) from Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 where BatchID in (*********,*********,*********,240517103,240517104,240517105,240517106,240517107,240517108,240517109) and PropertyID is not null;


update Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 set PropertyID=null where BatchID in (*********,*********,*********,240517103,240517104,240517105,240517106,240517107,240517108,240517109) and PropertyID is not null;

select PostalCode,count(*) from Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 group by PostalCode;


call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(*********);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(*********);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(*********);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240517103);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240517104);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240517105);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240517106);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240517107);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240517108);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(240517109);
