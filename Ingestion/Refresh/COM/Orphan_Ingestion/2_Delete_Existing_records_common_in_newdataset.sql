drop temporary table if exists tempD<PERSON><PERSON><PERSON><PERSON>han;
create temporary table tempD<PERSON><PERSON><PERSON><PERSON>han(
SELECT ts.Tenant_Stage_Id,bt.Vendor<PERSON>,ts.<PERSON><PERSON><PERSON><PERSON>,ts.State as TSState,bt.State as BTState FROM Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 bt inner join Empirical_DataStage.Tenants_Stage ts 
on bt.VendorID=ts.VendorID #where ts.BranchID is not null or ts.ConfirmedTenantID is not null
where ts.IsHidden=1 and  ts.CreatedDate<"2024-05-10" #and ts.State=59 and bt.State=59 #and (ts.ModifiedDate<"2024-05-16" or ts.ModifiedDate is null)
);

select count(*) from Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 bt inner join tempDeleteBronze t on t.VendorID=bt.VendorID where t.<PERSON><PERSON><PERSON> is null;

-- update Empirical_DataStage.BT_Orphan_Tenants_2024_05_16 bt inner join tempDeleteBronze t on t.VendorID=bt.Vendor<PERSON> set BatchID=20240516 where t.<PERSON><PERSON><PERSON><PERSON> is null;

select * from Empirical_DataStage.BT_Orphan_Tenants_2024_05_17 where VendorID in (select VendorID from tempDeleteBronze);
select * from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select Tenant_Stage_Id from tempDeleteOrphan);

select count(*),IsHidden from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select Tenant_Stage_Id from tempDeleteOrphan) group by IsHidden;


delete from Empirical_Prod.NationalIdentifiers where Tenant_Stage_ID in (select Tenant_Stage_Id from tempDeleteOrphan);
delete from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select Tenant_Stage_Id from tempDeleteOrphan);
