CREATE TABLE Empirical_DataStage.Bronze_Tenants_2024_05_10_clone AS
SELECT *
FROM Empirical_DataStage.Bronze_Tenants_2024_05_10;

update Empirical_DataStage.Bronze_Tenants_2024_05_10 set PropertyID=null where Bronze_Tenants is not null;

Update Empirical_DataStage.Orphan_Tenants_2024_05_10 set BatchID=null;
Update Empirical_DataStage.Bronze_Tenants_2024_05_10 set BatchID=null;

drop  temporary table if exists  OrphanVendors;
create temporary table OrphanVendors(
SELECT  VendorID FROM Empirical_DataStage.Orphan_Tenants_2024_05_10 where Batch<PERSON> is null);

drop  temporary table if exists  OrphanduplicateVendors;
create temporary table OrphanduplicateVendors(
SELECT  VendorID FROM OrphanVendors group by VendorID having count(*)>1);


drop  temporary table if exists  bronzeVendors;
create temporary table bronzeVendors(
SELECT  VendorID FROM Empirical_DataStage.Bronze_Tenants_2024_05_10 where BatchID is null);

drop  temporary table if exists  bronzeduplicateVendors;
create temporary table bronzeduplicateVendors(
SELECT  VendorID FROM bronzeVendors group by VendorID having count(*)>1);



select count(*),BatchID from Empirical_DataStage.Orphan_Tenants_2024_04_23 group by BatchID;


update Empirical_DataStage.Bronze_Tenants_2024_05_10 set State=59 where StateAbbr='VIC';
update Empirical_DataStage.Orphan_Tenants_2024_05_10 set State=59 where StateAbbr='VIC';

INSERT INTO `Empirical_Prod`.`Providers` (`ProviderID`, `ProviderName`, `IsActive`, `IsTenantProvider`, `DisplayOrder`, `DisplayText`, `LabelColor`) VALUES ('19', 'CompanyWebsites', '1', '1', '10', 'P6', '#bfa649');



update Empirical_DataStage.Bronze_Tenants_2024_05_10 set BatchID=*********;
update Empirical_DataStage.Orphan_Tenants_2024_05_10 set BatchID=*********;


select count(*),BatchID,IsProcessed from Empirical_DataStage.Orphan_Tenants_2024_05_10 group by BatchID,IsProcessed;
select count(*),BatchID,IsProcessed from Empirical_DataStage.Bronze_Tenants_2024_05_10 group by BatchID,IsProcessed;

select * from Empirical_DataStage.Bronze_Tenants_2024_05_10 where IsProcessed is null;
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(*********);
call Empirical_DataStage.CRE_Tenant_Stage_Import_Orphan_DataSet(*********);
