CREATE TABLE Empirical_DataStage.Tenants_Stage_BAPM_2024_05_15 AS
SELECT count(*) FROM Empirical_DataStage.Tenants_Stage where PostalCode in (

3000,3002,3003,3004,3006,3008,3031,3051,3052,3053,3054);


select count(*) from  Empirical_DataStage.BT_Address_PID_Matching_2024_05_15 as btm
inner join Empirical_DataStage.Tenants_Stage  as ts on ts.VendorID = btm.VendorID and
btm.ProviderID=ts.ProviderID and ts.PostalCode in (
3000,3002,3003,3004,3006,3008,3031,3051,3052,3053,3054);

update Empirical_DataStage.BT_Address_PID_Matching_2024_05_15 as btm
inner join Empirical_DataStage.Tenants_Stage  as ts on ts.VendorID = btm.VendorID and
btm.ProviderID=ts.ProviderID and ts.PostalCode in (
3000,3002,3003,3004,3006,3008,3031,3051,3052,3053,3054)
SET  btm.Tenant_Stage_ID = ts.Tenant_Stage_Id ;


select ts.ModifiedBy, ts.ModifiedDate, ts.CreatedDate, ts.PropertyID  as tspropertyID, btm.PropertyID, ts.VendorID, ts.TenantName from Empirical_DataStage.Tenants_Stage AS ts
INNER JOIN Empirical_DataStage.BT_Address_PID_Matching_2024_05_15 AS btm
ON ts.VendorID = btm.VendorID AND ts.ProviderID = btm.ProviderID and ts.PostalCode in (
3000,3002,3003,3004,3006,3008,3031,3051,3052,3053,3054) and btm.Tenant_Stage_ID = ts.Tenant_Stage_Id 
and ts.PropertyID is not null and ts.PropertyID != btm.PropertyID;


select ts.ModifiedBy, ts.ModifiedDate, ts.CreatedDate, btm.PropertyID as btmproperty, ts.PropertyID, ts.VendorID, ts.TenantName, btm.PropertyID, ts.VendorID, ts.TenantName from Empirical_DataStage.Tenants_Stage AS ts
INNER JOIN Empirical_DataStage.BT_Address_PID_Matching_2024_05_15 AS btm
ON ts.VendorID = btm.VendorID AND ts.ProviderID = btm.ProviderID and ts.PostalCode in (
3000,3002,3003,3004,3006,3008,3031,3051,3052,3053,3054) and btm.Tenant_Stage_ID = ts.Tenant_Stage_Id 
and ts.PropertyID is not null and ts.PropertyID = btm.PropertyID where ts.isHidden=1;



select * from Empirical_Prod.Lease where 
ConfirmedTenantID  in (
select ConfirmedTenantID from Empirical_Tenants.ConfirmedTenants where CompanyID in (
select ts.BranchID  from Empirical_DataStage.Tenants_Stage AS ts
INNER JOIN Empirical_DataStage.BT_Address_PID_Matching_2024_05_15 AS btm
ON ts.VendorID = btm.VendorID AND ts.ProviderID = btm.ProviderID and ts.PostalCode in (
3000,3002,3003,3004,3006,3008,3031,3051,3052,3053,3054) and btm.Tenant_Stage_ID = ts.Tenant_Stage_Id 
and ts.PropertyID is not null and ts.PropertyID = btm.PropertyID where ts.isHidden=1 and ts.BranchID is not null)
);


-- SET ts.PropertyID = btm.PropertyID;

/**
UPDATE Empirical_DataStage.Tenants_Stage AS ts
INNER JOIN Empirical_DataStage.BT_Address_PID_Matching_2024_05_15 AS btm
ON ts.VendorID = btm.VendorID AND ts.ProviderID = btm.ProviderID and ts.PostalCode in (
3000,3002,3003,3004,3006,3008,3031,3051,3052,3053,3054) and btm.Tenant_Stage_ID = ts.Tenant_Stage_Id 
SET ts.PropertyID = btm.PropertyID, ts.ModifiedBy=22, ts.ModifiedDate=now();
**/

SELECT * FROM Empirical_DataStage.Bronze_Tenants_2024_05_10 where VendorID in (
select VendorID from Empirical_DataStage.BT_Address_PID_Matching_2024_05_15 );

select count(*) from Empirical_DataStage.BT_Address_PID_Matching_2024_05_15 where PropertyID = AL_PropertyID;





CREATE TABLE Empirical_DataStage.Tenants_Stage_AAPM_2024_05_15 AS
SELECT count(*) FROM Empirical_DataStage.Tenants_Stage where PostalCode in (

3000,3002,3003,3004,3006,3008,3031,3051,3052,3053,3054);