CREATE TABLE Empirical_DataStage.Tenants_Stage_BAPM_2024_05_23_QLD AS
SELECT * FROM Empirical_DataStage.Tenants_Stage where State=58;

select * from Empirical_DataStage.Tenants_Stage_BAPM_2024_05_23_QLD;

select count(*) from  Empirical_DataStage.BT_Bronze_Tenants_2024_05_20 as btm
inner join Empirical_DataStage.Tenants_Stage  as ts on ts.VendorID = btm.VendorID and
btm.ProviderID=ts.ProviderID and ts.State=58 and btm.StateAbbr="QLD" where (ts.<PERSON>H<PERSON><PERSON> is null or ts.IsHidden=0) and ts.PropertyID is null;

update Empirical_DataStage.BT_Bronze_Tenants_2024_05_20 as btm
inner join Empirical_DataStage.Tenants_Stage  as ts on ts.VendorID = btm.VendorID and
btm.ProviderID=ts.ProviderID and ts.State=58 and btm.StateAbbr="QLD"
SET  btm.Tenant_Stage_ID = ts.Tenant_Stage_Id
   where ( ts.<PERSON><PERSON><PERSON><PERSON> is null or ts.IsHidden=0) and ts.PropertyID is null;


select count(*)from  Empirical_DataStage.Tenants_Stage AS ts
INNER JOIN Empirical_DataStage.BT_Bronze_Tenants_2024_05_20 AS btm
ON ts.VendorID = btm.VendorID AND ts.ProviderID = btm.ProviderID and ts.State=58 and btm.StateAbbr="QLD"
 and btm.Tenant_Stage_ID = ts.Tenant_Stage_Id 
  where ( ts.IsHidden is null or ts.IsHidden=0) and ts.PropertyID is null;


select  distinct StateAbbr, State from BT_Bronze_Tenants_2024_05_20 where TSI_State="QLD"and  StateAbbr="QLD";

/**

UPDATE Empirical_DataStage.Tenants_Stage AS ts
INNER JOIN Empirical_DataStage.BT_Bronze_Tenants_2024_05_20 AS btm
ON ts.VendorID = btm.VendorID AND ts.ProviderID = btm.ProviderID and ts.State=58 and btm.StateAbbr="QLD"
and btm.Tenant_Stage_ID = ts.Tenant_Stage_Id 
SET ts.PropertyID = btm.PropertyID, ts.ModifiedBy=22, ts.ModifiedDate=now()
where ( ts.IsHidden is null or ts.IsHidden=0) and ts.PropertyID is null;
**/




CREATE TABLE Empirical_DataStage.Tenants_Stage_AAPM_2024_05_23_QLD AS
SELECT count(*) FROM Empirical_DataStage.Tenants_Stage where  State=58;