CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Tenant_Stage_IDs_Mapping`(IN P_Market Varchar(100),IN P_Bucket varchar(100))
BEGIN

	-- Set NationalID with the combination of ACN and ABN
	UPDATE Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 as a
		SET a.NationalID = 
			CASE
				WHEN a.ACN IS NOT NULL AND a.ABN IS NOT NULL THEN CONCAT('ACN: ', a.ACN, ' ABN: ', a.ABN)
				WHEN a.ACN IS NOT NULL THEN CONCAT('ACN: ', a.ACN)
				WHEN a.ABN IS NOT NULL THEN CONCAT('ABN: ', a.ABN)
				ELSE NULL
			END;
    
    -- Set IsProcessed and Tenant_Stage_ID to null before starting the ingestion
	update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set IsProcessed =NULL and Tenant_Stage_ID= NULL where TSI_State='NSW' and TSI_Bucket='Gold';
	update Empirical_DataStage.Raw_Matched_Tenants_2024_04_23 set Tenant_Stage_ID=null where TSI_State='NSW' and TSI_Bucket='Gold';

	-- Mapping Tenant_Stage_IDs for all the tenants in illion provider
	DROP TEMPORARY TABLE IF EXISTS `Empirical_DataStage`.`TenantSourceVendor`;
	create temporary table Empirical_DataStage.TenantSourceVendor (
		select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=6);

	update Empirical_DataStage.Raw_Matched_Tenants_2024_04_23 as a
		inner join Empirical_DataStage.TenantSourceVendor as b on a.VendorID=b.VendorID
		set a.Tenant_Stage_ID=b.Tenant_Stage_Id where a.TSI_State=P_Market and a.TSI_Bucket=P_Bucket;
        
	-- Mapping Tenant_Stage_IDs for all the tenants in zoominfo provider
	DROP TEMPORARY TABLE IF EXISTS `Empirical_DataStage`.`TenantSourceVendor`;
	create temporary table Empirical_DataStage.TenantSourceVendor (
		select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=15);

	update Empirical_DataStage.Raw_Matched_Tenants_2024_04_23 as a
		inner join Empirical_DataStage.TenantSourceVendor as b on a.VendorID=b.VendorID
		set a.Tenant_Stage_ID=b.Tenant_Stage_Id where a.TSI_State=P_Market and a.TSI_Bucket=P_Bucket;
        
	-- Mapping Tenant_Stage_IDs for all the tenants in overture provider
	DROP TEMPORARY TABLE IF EXISTS `Empirical_DataStage`.`TenantSourceVendor`;
	create temporary table Empirical_DataStage.TenantSourceVendor (
		select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=16);

	update Empirical_DataStage.Raw_Matched_Tenants_2024_04_23 as a
		inner join Empirical_DataStage.TenantSourceVendor as b on a.VendorID=b.VendorID
		set a.Tenant_Stage_ID=b.Tenant_Stage_Id where a.TSI_State=P_Market and a.TSI_Bucket=P_Bucket;
        
	-- Mapping Tenant_Stage_IDs for all the tenants in google provider
	DROP TEMPORARY TABLE IF EXISTS `Empirical_DataStage`.`TenantSourceVendor`;
	create temporary table Empirical_DataStage.TenantSourceVendor (
		select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=18);

	update Empirical_DataStage.Raw_Matched_Tenants_2024_04_23 as a
		inner join Empirical_DataStage.TenantSourceVendor as b on a.VendorID=b.VendorID
		set a.Tenant_Stage_ID=b.Tenant_Stage_Id where a.TSI_State=P_Market and a.TSI_Bucket=P_Bucket;

    
	-- Mapping Tenant_Stage_IDs for all the tenants in companyWebsites provider
	DROP TEMPORARY TABLE IF EXISTS `Empirical_DataStage`.`TenantSourceVendor`;
	create temporary table Empirical_DataStage.TenantSourceVendor (
		select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=19);

	update Empirical_DataStage.Raw_Matched_Tenants_2024_04_23 as a
		inner join Empirical_DataStage.TenantSourceVendor as b on a.VendorID=b.VendorID
		set a.Tenant_Stage_ID=b.Tenant_Stage_Id where a.TSI_State=P_Market and a.TSI_Bucket=P_Bucket;
        
	-- Mapping Tenant_Stage_IDs for all the tenants in ALA provider
	DROP TEMPORARY TABLE IF EXISTS `Empirical_DataStage`.`TenantSourceVendor`;
	create temporary table Empirical_DataStage.TenantSourceVendor (
		select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=14);

	update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 as a
		inner join Empirical_DataStage.TenantSourceVendor as b on a.VendorID=b.VendorID
		set a.Tenant_Stage_ID=b.Tenant_Stage_Id where a.TSI_State=P_Market and a.TSI_Bucket=P_Bucket;
        
END