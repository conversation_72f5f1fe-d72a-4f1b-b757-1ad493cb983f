select count(*) from Empirical_DataStage.Tenants_Stage as ts
     inner join Empirical_Prod.Address as ad on ad.ParentID=ts.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
    where (ts.City<>c.CityName or ts.City or ts.City is null or ts.City='') and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1 and ts.State=58;

select * from Empirical_DataStage.Tenants_Stage as ts
     inner join Empirical_Prod.Address as ad on ad.ParentID=ts.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
    where (ts.City<>c.CityName or ts.City or ts.City is null or ts.City='') and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1 and ts.State=58;
    

  update Empirical_DataStage.Tenants_Stage as ts
     inner join Empirical_Prod.Address as ad on ad.ParentID=ts.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
     set ts.City=c.CityName 
    where  (ts.City<>c.CityName or ts.City or ts.City is null or ts.City='') and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1 and ts.State=58;
    