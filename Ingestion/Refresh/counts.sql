-- Silver
drop temporary table if exists TenantsIN2000Or3000;
create temporary table TenantsIN2000Or3000(
select distinct a.* from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a
inner join Empirical_Prod.Address b on a.PropertyID=b.ParentID and b.ParentTableID=1 and b.Sequence=1 and b.IsActive=1
where b.<PERSON>ipC<PERSON> in (3000, 2000) and a.TSI_Bucket='Silver'
);

drop temporary table if exists tempLeaseCT;
create temporary table tempLeaseCT(
select distinct a.*,b.ConfirmedTenantID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and a.TSI_Bucket='Silver'
);

drop temporary table if exists tempLeaseProp;
create temporary table tempLeaseProp(
select distinct a.PropertyID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and a.TSI_Bucket='Silver'
);

drop temporary table if exists tempLeaseRawCT;
create temporary table tempLeaseRawCT(
select distinct a.* from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a
inner join tempLeaseProp b on a.PropertyID=b.PropertyID
where a.TSI_Bucket='Silver'
);

drop temporary table if exists tempLeaseTenants;
create temporary table tempLeaseTenants(
select b.* from Empirical_DataStage.Tenants_Stage as a
inner join Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 b on a.VendorID=b.VendorID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where a.BranchID in (select CompanyID from Empirical_Tenants.ConfirmedTenants 
    where ConfirmedTenantID in (select distinct ConfirmedTenantID from tempLeaseCT)) and a.ProviderID=6 and b.TSI_Bucket='Silver');


select count(*) from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from tempLeaseTenants); -- 0

select count(*) from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from TenantsIN2000Or3000); -- 14010

drop temporary table if exists temp1;
create temporary table temp1(
select a.VendorID,a.PropertyID as NewProperty,b.PropertyID as OldProperty from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a inner join Empirical_DataStage.Silver_NSW_Raw_Confirmed_Tenants b 
on a.VendorID=b.VendorID and a.TSI_Bucket='Silver'
union
select a.VendorID,a.PropertyID as NewProperty,b.PropertyID as OldProperty from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a inner join Empirical_DataStage.Silver_VIC_Raw_Confirmed_Tenants b 
on a.VendorID=b.VendorID and a.TSI_Bucket='Silver'
union
select a.VendorID,a.PropertyID as NewProperty,b.PropertyID as OldProperty from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a inner join Empirical_DataStage.Silver_QLD_Raw_Confirmed_Tenants b 
on a.VendorID=b.VendorID and a.TSI_Bucket='Silver'
);

select count(*) from temp1 where NewProperty=OldProperty; -- 40434
select count(*) from temp1 where NewProperty!=OldProperty; -- 6522

-- Upgrade from Bronze - 77155
drop temporary table if exists temp1;
create temporary table temp1(
select b.Tenant_Stage_ID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a inner join Empirical_DataStage.Tenants_Stage b 
on a.VendorID=b.VendorID where b.BranchID is null and b.PropertyID is not null and a.TSI_Bucket='Silver' and b.ProviderID=15
);

-- Downgrade to Bronze - 53463
drop temporary table if exists temp1;
create temporary table temp1(
select a.VendorID from Empirical_DataStage.Bronze_Tenants_2024_04_23 a inner join Empirical_DataStage.Silver_NSW_Raw_Confirmed_Tenants b 
on a.VendorID=b.VendorID
union
select a.VendorID from Empirical_DataStage.Bronze_Tenants_2024_04_23 a inner join Empirical_DataStage.Silver_VIC_Raw_Confirmed_Tenants b 
on a.VendorID=b.VendorID
union
select a.VendorID from Empirical_DataStage.Bronze_Tenants_2024_04_23 a inner join Empirical_DataStage.Silver_QLD_Raw_Confirmed_Tenants b 
on a.VendorID=b.VendorID
);

-- Gold

drop temporary table if exists TenantsIN2000Or3000;
create temporary table TenantsIN2000Or3000(
select distinct a.* from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a
inner join Empirical_Prod.Address b on a.PropertyID=b.ParentID and b.ParentTableID=1 and b.Sequence=1 and b.IsActive=1
where b.ZipCode in (3000, 2000) and a.TSI_Bucket='Gold'
);

drop temporary table if exists tempLeaseCT;
create temporary table tempLeaseCT(
select distinct a.*,b.ConfirmedTenantID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and a.TSI_Bucket='Gold'
);

drop temporary table if exists tempLeaseProp;
create temporary table tempLeaseProp(
select distinct a.PropertyID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and a.TSI_Bucket='Gold'
);

drop temporary table if exists tempLeaseRawCT;
create temporary table tempLeaseRawCT(
select distinct a.* from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a
inner join tempLeaseProp b on a.PropertyID=b.PropertyID
where a.TSI_Bucket='Gold'
);

drop temporary table if exists tempLeaseTenants;
create temporary table tempLeaseTenants(
select b.* from Empirical_DataStage.Tenants_Stage as a
inner join Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 b on a.VendorID=b.VendorID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where a.BranchID in (select CompanyID from Empirical_Tenants.ConfirmedTenants 
    where ConfirmedTenantID in (select distinct ConfirmedTenantID from tempLeaseCT)) and a.ProviderID=6 and b.TSI_Bucket='Gold');


select count(*) from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from tempLeaseTenants); -- 9394

select count(*) from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from TenantsIN2000Or3000); -- 25599

drop temporary table if exists temp1;
create temporary table temp1(
select a.VendorID,a.PropertyID as NewProperty,b.PropertyID as OldProperty from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a inner join Empirical_DataStage.NSW_Raw_Confirmed_Tenants b 
on a.VendorID=b.VendorID and a.TSI_Bucket='Gold'
union
select a.VendorID,a.PropertyID as NewProperty,b.PropertyID as OldProperty from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a inner join Empirical_DataStage.VIC_Raw_Confirmed_Tenants b 
on a.VendorID=b.VendorID and a.TSI_Bucket='Gold'
union
select a.VendorID,a.PropertyID as NewProperty,b.PropertyID as OldProperty from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a inner join Empirical_DataStage.QLD_Raw_Confirmed_Tenants b 
on a.VendorID=b.VendorID and a.TSI_Bucket='Gold'
);

select count(*) from temp1 where NewProperty=OldProperty; -- 125526
select count(*) from temp1 where NewProperty!=OldProperty; -- 48607

-- Upgrade from Bronze - 85803
drop temporary table if exists temp1;
create temporary table temp1(
select b.Tenant_Stage_ID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a inner join Empirical_DataStage.Tenants_Stage b 
on a.VendorID=b.VendorID where b.BranchID is null and b.PropertyID is not null and a.TSI_Bucket='Gold' and b.ProviderID=6
);

-- Downgrade to Bronze - 143588
drop temporary table if exists temp1;
create temporary table temp1(
select a.VendorID from Empirical_DataStage.Bronze_Tenants_2024_04_23 a inner join Empirical_DataStage.NSW_Raw_Confirmed_Tenants b 
on a.VendorID=b.VendorID
union
select a.VendorID from Empirical_DataStage.Bronze_Tenants_2024_04_23 a inner join Empirical_DataStage.VIC_Raw_Confirmed_Tenants b 
on a.VendorID=b.VendorID
union
select a.VendorID from Empirical_DataStage.Bronze_Tenants_2024_04_23 a inner join Empirical_DataStage.QLD_Raw_Confirmed_Tenants b 
on a.VendorID=b.VendorID
);

-- CT not in TS - 424356
select count(*) from Empirical_Tenants.ConfirmedTenants  as ct 
left  join Empirical_DataStage.Tenants_Stage as ts on ts.BranchID=ct.CompanyID
where ts.BranchID is null and ct.ProviderID=1;

drop temporary table if exists temp1;
create temporary table temp1(
select ConfirmedTenantID,ExtVendorID,CompanyID from Empirical_Tenants.ConfirmedTenants where ProviderID=1
);

select count(*) from temp1 a left join Empirical_DataStage.Tenants_Stage b on a.ExtVendorID=b.VendorID where b.VendorID is null; -- 513404
select count(*) from temp1 a left join Empirical_DataStage.Tenants_Stage b on a.ConfirmedTenantID=b.ConfirmedTenantID where b.ConfirmedTenantID is null; -- 502755
select count(*) from temp1 a left join Empirical_DataStage.Tenants_Stage b on a.CompanyID=b.BranchID where b.BranchID is null; -- 427811

select count(*) from temp1 a 
left join Empirical_DataStage.Tenants_Stage b on a.ExtVendorID=b.VendorID
left join Empirical_DataStage.Tenants_Stage c on a.ConfirmedTenantID=c.ConfirmedTenantID
left join Empirical_DataStage.Tenants_Stage d on a.CompanyID=d.BranchID 
where b.VendorID is null and c.ConfirmedTenantID is null and d.BranchID is null; -- 424356

-- TS Not in New dataset - 2138041

drop temporary table if exists tempBronzeAndOrphanTenants;
create temporary table tempBronzeAndOrphanTenants(
select distinct VendorID from Empirical_DataStage.Bronze_Tenants_2024_04_23 union
select distinct VendorID from Empirical_DataStage.Orphan_Tenants_2024_04_23
);

drop temporary table if exists tempTSTenants;
create temporary table tempTSTenants(
select ts.* from Empirical_DataStage.Tenants_Stage ts left join tempBronzeAndOrphanTenants bot on ts.VendorID=bot.VendorID where bot.VendorID is null
);

create table TenantsInTenantStageNotInBronzeOrOrphan as
select ts.*,ProviderName from tempTSTenants ts inner join Providers p on ts.ProviderID=p.ProviderID;
