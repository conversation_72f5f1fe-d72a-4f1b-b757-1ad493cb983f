drop temporary table if exists temp1;
create temporary table temp1(
select * from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where TSI_Bucket='Gold' and ImmediateParentDUNS is not null
);

update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335400 where TSI_Bucket='Gold' and ImmediateParentDUNS is not null and TSI_State='NSW';
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42336400 where TSI_Bucket='Gold' and ImmediateParentDUNS is not null and TSI_State='VIC';
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42337400 where TSI_Bucket='Gold' and ImmediateParentDUNS is not null and TSI_State='QLD';
