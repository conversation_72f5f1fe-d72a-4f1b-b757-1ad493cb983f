drop temporary table if exists tmpduplicateVendorsinRCT;
create temporary table tmpduplicateVendorsinRCT(
select Vendor<PERSON> from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where TSI_State='NSW' and TSI_Bucket='Gold' group by VendorID having count(*)>1); 

update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335300 where Vendor<PERSON> in (select * from tmpduplicateVendorsinRCT) and TSI_State='NSW' and TSI_Bucket='Gold';

drop temporary table if exists tmpSingleVendorinRCT;
create temporary table tmpSingleVendorinRCT(
select VendorID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where TSI_State='NSW' and TSI_Bucket='Gold' group by VendorID having count(*)=1); 

drop temporary table if exists tempTSMultiVendorID;
create temporary table tempTSMultiVendorID(
SELECT b.VendorID from tmpSingleVendorinRCT as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=6 group by b.VendorID having count(*)>1);

update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335700 where VendorID in (select * from tempTSMultiVendorID) and TSI_State='NSW' and TSI_Bucket='Gold';

drop temporary table if exists tmpRCTFinal;
create temporary table tmpRCTFinal(
select a.VendorID from tmpSingleVendorinRCT a left join tempTSMultiVendorID b on a.VendorID=b.VendorID 
where b.VendorID is null); 

drop temporary table if exists tempTS;
create temporary table tempTS (select a.VendorID,a.BranchID from Empirical_DataStage.Tenants_Stage as a
inner join tmpRCTFinal as b on a.VendorID=b.VendorID where a.ProviderID=6);

drop temporary table if exists tmpTSwithDuplicateBranchID;
create temporary table tmpTSwithDuplicateBranchID(
select VendorID from tempTS where BranchID is not null group by BranchID having count(*)>1);

update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335800 where VendorID in (select VendorID from tmpTSwithDuplicateBranchID) and TSI_State='NSW' and TSI_Bucket='Gold';

drop temporary table if exists tmpRCTFinal_1;
create temporary table tmpRCTFinal_1( select a.VendorID from tmpRCTFinal a left join tmpTSwithDuplicateBranchID b on a.VendorID=b.VendorID 
where b.VendorID is null); #where VendorID not in (select VendorID from tmpTSwithDuplicateBranchID));

drop temporary table if exists tempTS;
create temporary table tempTS (select a.VendorID,a.BranchID from Empirical_DataStage.Tenants_Stage as a
inner join tmpRCTFinal_1 as b on a.VendorID=b.VendorID where a.ProviderID=6);

drop temporary table if exists tempTS_1;
create temporary table tempTS_1 (select VendorID from tempTS);

drop temporary table if exists tempBranchNullVendor;
create temporary table tempBranchNullVendor(
select VendorID from tempTS where BranchID is null);

update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335100 where VendorID in (select VendorID from tempBranchNullVendor) and TSI_State='NSW' and TSI_Bucket='Gold';

drop temporary table if exists tempBranchNotNullVendor;
create temporary table tempBranchNotNullVendor(
select VendorID from tempTS where BranchID is not null);

update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335000 where VendorID in (select VendorID from tempBranchNotNullVendor) and TSI_State='NSW' and TSI_Bucket='Gold';
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335200 where BatchID is  null and TSI_Bucket='Gold' and TSI_State='NSW';


-- ----- Tenants in 2000 or 3000 postalcodes ---------

drop temporary table if exists TenantsIN2000Or3000;
create temporary table TenantsIN2000Or3000(
select distinct a.* from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a
inner join Empirical_Prod.Address b on a.PropertyID=b.ParentID and b.ParentTableID=1 and b.Sequence=1 and b.IsActive=1
where b.ZipCode in (3000, 2000) and a.TSI_Bucket='Gold' and a.TSI_State='NSW'
);

update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335500
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from TenantsIN2000Or3000) and TSI_Bucket='Gold' and TSI_State='NSW';


-- ---- Lease related Batches --------


drop temporary table if exists tempLeaseCT;
create temporary table tempLeaseCT(
select distinct a.*,b.ConfirmedTenantID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and  a.BatchID in (42335000,42335100,42335200) and a.TSI_Bucket='Gold' and a.TSI_State='NSW'
);

drop temporary table if exists tempLeaseProp;
create temporary table tempLeaseProp(
select distinct a.PropertyID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and  a.BatchID in (42335000,42335100,42335200) and a.TSI_Bucket='Gold' and a.TSI_State='NSW'
);

drop temporary table if exists tempLeaseRawCT;
create temporary table tempLeaseRawCT(
select distinct a.* from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a
inner join tempLeaseProp b on a.PropertyID=b.PropertyID and  a.BatchID in (42335000,42335100,42335200) and a.TSI_Bucket='Gold' and a.TSI_State='NSW'
);


drop temporary table if exists tempLeaseTenants;
create temporary table tempLeaseTenants(
select b.* from Empirical_DataStage.Tenants_Stage as a
inner join Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 b on a.VendorID=b.VendorID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where a.BranchID in (select CompanyID from Empirical_Tenants.ConfirmedTenants 
    where ConfirmedTenantID in (select distinct ConfirmedTenantID from tempLeaseCT)) and a.ProviderID=6 and b.BatchID in (42335000,42335100,42335200)
	and b.TSI_Bucket='Gold' and b.TSI_State='NSW');


update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335600 -- Existing Tenants with Lease
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from tempLeaseTenants) and TSI_Bucket='Gold' and TSI_State='NSW';

-- ----Single Confirmed Tenant -----------------


drop temporary table if exists temp1;
create temporary table temp1(
select distinct BranchID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a inner join 
Empirical_DataStage.Tenants_Stage b on a.VendorID=b.VendorID where a.TSI_Bucket='Gold' and a.TSI_State='NSW'
);

drop temporary table if exists temp2;
create temporary table temp2(
select distinct BranchID from temp1 a inner join Empirical_Tenants.ConfirmedTenants b on a.BranchID=b.CompanyID group by b.CompanyID having count(*)=1
);

drop temporary table if exists temp3;
create temporary table temp3(
select distinct a.VendorID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a inner join Empirical_DataStage.Tenants_Stage b on a.VendorID=b.VendorID
where BranchID in (select BranchID from temp2) and a.TSI_Bucket='Gold' and a.TSI_State='NSW'
);

 update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID= 42335900 where VendorID in (select * from temp3) and 
 BatchID in (42335000,42335100,42335200) and TSI_Bucket='Gold' and TSI_State='NSW';


select count(*),BatchID, TSI_Bucket,TSI_State from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 group by BatchID, TSI_Bucket,TSI_State;
