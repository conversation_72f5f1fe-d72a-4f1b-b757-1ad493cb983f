DROP temporary TABLE if exists `Empirical_DataStage`.`TenantVendors`;
create temporary table Empirical_DataStage.TenantVendors (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=6);

update Empirical_DataStage.Raw_Matched_Tenants_2024_04_23 as a
inner join Empirical_DataStage.TenantVendors as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID;

DROP temporary TABLE if exists `Empirical_DataStage`.`TenantVendors`;
create temporary table Empirical_DataStage.TenantVendors (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=15);

update Empirical_DataStage.Raw_Matched_Tenants_2024_04_23 as a
inner join Empirical_DataStage.TenantVendors as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID;

DROP temporary TABLE if exists `Empirical_DataStage`.`TenantVendors`;
create temporary table Empirical_DataStage.TenantVendors (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=16);

update Empirical_DataStage.Raw_Matched_Tenants_2024_04_23 as a
inner join Empirical_DataStage.TenantVendors as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID;

DROP temporary TABLE if exists `Empirical_DataStage`.`TenantVendors`;
create temporary table Empirical_DataStage.TenantVendors (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=18);

update Empirical_DataStage.Raw_Matched_Tenants_2024_04_23 as a
inner join Empirical_DataStage.TenantVendors as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID;

DROP temporary TABLE if exists `Empirical_DataStage`.`TenantVendors`;
create temporary table Empirical_DataStage.TenantVendors (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=19);

update Empirical_DataStage.Raw_Matched_Tenants_2024_04_23 as a
inner join Empirical_DataStage.TenantVendors as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID;

DROP temporary TABLE if exists `Empirical_DataStage`.`TenantVendors`;
create temporary table Empirical_DataStage.TenantVendors (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=14);

update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 as a
inner join Empirical_DataStage.TenantVendors as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID;