drop temporary table if exists temp1;
create temporary table temp1(
select V<PERSON><PERSON><PERSON> from Empirical_DataStage.Confirmed_Tenants_IPDUNS_Relation_Under_SameProperty
);

drop temporary table if exists temp2;
create temporary table temp2(
select Vendor<PERSON>,PropertyID,Raw_Confirmed_TenantsID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where V<PERSON><PERSON><PERSON> in (select * from temp1));

drop temporary table if exists duplicateVendors;
create temporary table duplicateVendors(
select Vendor<PERSON> from temp2 group by VendorID having count(*)>1); 

select count(*),BatchID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where V<PERSON><PERSON><PERSON> in (select * from duplicateVendors) group by BatchID;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335300 where Vendor<PERSON> in (select * from duplicateVendors) and BatchID=42335400;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42336300 where Vend<PERSON><PERSON> in (select * from duplicateVendors) and BatchID=42336400;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42337300 where Vend<PERSON><PERSON> in (select * from duplicateVendors) and BatchID=42337400;

drop temporary table if exists SingleVendor;
create temporary table SingleVendor(
select VendorID,PropertyID,Raw_Confirmed_TenantsID from temp2 group by VendorID having count(*)=1); 

drop temporary table if exists tempTSMultiVendorID;
create temporary table tempTSMultiVendorID(
SELECT b.VendorID from SingleVendor as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=6 group by b.VendorID having count(*)>1);

select count(*),BatchID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where VendorID in (select * from tempTSMultiVendorID) group by BatchID;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335700 where VendorID in (select * from tempTSMultiVendorID) and BatchID=42335400;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42336700 where VendorID in (select * from tempTSMultiVendorID) and BatchID=42336400;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42337700 where VendorID in (select * from tempTSMultiVendorID) and BatchID=42337400;

drop temporary table if exists tmpRCTFinal;
create temporary table tmpRCTFinal(
select a.VendorID,a.PropertyID,Raw_Confirmed_TenantsID from SingleVendor a left join tempTSMultiVendorID b on a.VendorID=b.VendorID 
where b.VendorID is null); 

drop temporary table if exists tempTS;
create temporary table tempTS (select a.VendorID,a.BranchID from Empirical_DataStage.Tenants_Stage as a
inner join tmpRCTFinal as b on a.VendorID=b.VendorID where a.ProviderID=6);

drop temporary table if exists tmpTSwithDuplicateBranchID;
create temporary table tmpTSwithDuplicateBranchID(
select VendorID from tempTS where BranchID is not null group by BranchID having count(*)>1);

select count(*),BatchID from  Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where VendorID in (select VendorID from tmpTSwithDuplicateBranchID) group by BatchID;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335800 where VendorID in (select * from tmpTSwithDuplicateBranchID) and BatchID=42335400;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42336800 where VendorID in (select * from tmpTSwithDuplicateBranchID) and BatchID=42336400;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42337800 where VendorID in (select * from tmpTSwithDuplicateBranchID) and BatchID=42337400;

drop temporary table if exists tmpRCTFinal2;
create temporary table tmpRCTFinal2(
select a.VendorID,a.PropertyID,Raw_Confirmed_TenantsID from tmpRCTFinal a left join tmpTSwithDuplicateBranchID b on a.VendorID=b.VendorID 
where b.VendorID is null); 

drop temporary table if exists TenantsIN2000Or3000;
create temporary table TenantsIN2000Or3000(
select distinct a.* from tmpRCTFinal2 a
inner join Empirical_Prod.Address b on a.PropertyID=b.ParentID and b.ParentTableID=1 and b.Sequence=1 and b.IsActive=1
where b.ZipCode in (3000, 2000)
);

select count(*),BatchID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from TenantsIN2000Or3000) group by BatchID;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335500 where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from TenantsIN2000Or3000) and BatchID=42335400;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42336500 where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from TenantsIN2000Or3000) and BatchID=42336400;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42337500 where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from TenantsIN2000Or3000) and BatchID=42337400;

drop temporary table if exists tmpRCTFinal3;
create temporary table tmpRCTFinal3(
select a.VendorID,a.PropertyID,a.Raw_Confirmed_TenantsID from tmpRCTFinal2 a left join TenantsIN2000Or3000 b on a.VendorID=b.VendorID 
where b.VendorID is null); 


drop temporary table if exists tempLeaseCT;
create temporary table tempLeaseCT(
select distinct a.*,b.ConfirmedTenantID from tmpRCTFinal3 a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1
);

drop temporary table if exists tempTS;
create temporary table tempTS(
select a.BranchID,b.* from Empirical_DataStage.Tenants_Stage as a
inner join tmpRCTFinal3 b on a.VendorID=b.VendorID where a.ProviderID=6
);

drop temporary table if exists tempLeaseTenants;
create temporary table tempLeaseTenants(
select * from tempTS
#inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where BranchID in (select CompanyID from Empirical_Tenants.ConfirmedTenants 
    where ConfirmedTenantID in (select distinct ConfirmedTenantID from tempLeaseCT)) );#and ProviderID=6);

select count(*),BatchID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from tempLeaseTenants) group by BatchID;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335600 where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from tempLeaseTenants) and BatchID=42335400;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42336600 where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from tempLeaseTenants) and BatchID=42336400;
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42337600 where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from tempLeaseTenants) and BatchID=42337400;

select count(*) from tmpRCTFinal3 a left join tempLeaseTenants b on a.Raw_Confirmed_TenantsID=b.Raw_Confirmed_TenantsID where b.Raw_Confirmed_TenantsID is null;

drop temporary table if exists FinalIngestionForDUNS;
create temporary table FinalIngestionForDUNS(
select a.VendorID,a.PropertyID,a.Raw_Confirmed_TenantsID from tmpRCTFinal3 a left join tempLeaseTenants b on a.Raw_Confirmed_TenantsID=b.Raw_Confirmed_TenantsID where b.Raw_Confirmed_TenantsID is null); 

select distinct BatchID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from FinalIngestionForDUNS);

select count(*),BatchID from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where BatchID in (42335400,42336400,42337400) group by BatchID;