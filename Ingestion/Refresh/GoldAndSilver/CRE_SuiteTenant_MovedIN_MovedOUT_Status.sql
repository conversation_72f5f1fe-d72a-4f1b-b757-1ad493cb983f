-- call Empirical_DataStage.CRE_SuiteTenant_MovedIN_MovedOUT_Status();

DROP TEMPORARY TABLE IF EXISTS tempTenantStage;
    DROP TEMPORARY TABLE IF EXISTS tempDeleteSuiteTenant;
    DROP TEMPORARY TABLE IF EXISTS tempUpdateSuiteTenant;
    
 -- Create temporary table to store tenant stage data
    CREATE TEMPORARY TABLE IF NOT EXISTS tempTenantStage AS
    SELECT a.ConfirmedTenantID, b.OldProperty, b.NewProperty
    FROM Empirical_DataStage.Tenants_Stage a
    INNER JOIN Empirical_DataStage.GoldAndSilverTenantsMovingFromOnePropertyToOtherProperty b 
    ON a.Tenant_Stage_Id = b.Tenant_Stage_ID 
    WHERE b.BatchID = 123;

    -- Temporary tables to store SuiteTenantID for deletion and update
    CREATE TEMPORARY TABLE IF NOT EXISTS tempDeleteSuiteTenant AS
    SELECT a.*
    FROM Empirical_Prod.SuiteTenant a
    INNER JOIN tempTenantStage b ON a.ConfirmedTenantID = b.ConfirmedTenantID
    WHERE a.PropertyID = b.NewProperty 
    AND a.ModifiedDate > "2024-04-28"
    AND a.ModifiedBy = 22
    ORDER BY a.ModifiedDate DESC;

    CREATE TEMPORARY TABLE IF NOT EXISTS tempUpdateSuiteTenant AS
    SELECT a.*, b.NewProperty
    FROM Empirical_Prod.SuiteTenant a
    INNER JOIN tempTenantStage b ON a.ConfirmedTenantID = b.ConfirmedTenantID
    WHERE a.PropertyID = b.OldProperty 
    AND a.ModifiedDate > "2024-04-28"
    AND a.ModifiedBy = 22
    ORDER BY a.ModifiedDate DESC;
        
	/* SELECT * FROM Empirical_Prod.SuiteTenant 
    WHERE SuiteTenantID IN (SELECT SuiteTenantID FROM tempDeleteSuiteTenant);

    SELECT * FROM Empirical_Prod.SuiteTenant a
    INNER JOIN tempUpdateSuiteTenant b ON a.SuiteTenantID = b.SuiteTenantID; */
drop temporary table if exists deleteAndUpdateIDS;
create temporary table deleteAndUpdateIDS(
select a.SuiteTenantID as DeleteID,a.ConfirmedTenantID as deleteCT,a.TenantStatusID as deleteTSID,a.PropertyID as deletePID,b.SuiteTenantID as UpdateID,b.ConfirmedTenantID as updateCT,b.TenantStatusID as updateTSID,b.PropertyID as updatePID,b.NewProperty from tempDeleteSuiteTenant a inner join tempUpdateSuiteTenant b on a.ConfirmedTenantID=b.ConfirmedTenantID where a.TenantStatusID=1 and b.TenantStatusID=2 group by a.ConfirmedTenantID having count(*)=1
);


select * from Empirical_Prod.SuiteTenant where SuiteTenantID in (select DeleteID from deleteAndUpdateIDS);
select * from Empirical_Prod.SuiteTenant a inner join deleteAndUpdateIDS b on a.SuiteTenantID=b.UpdateID;# set a.PropertyID=b.NewProperty,a.TenantStatusID=1,a.IsActive=1;

/*
delete from Empirical_Prod.SuiteTenant where SuiteTenantID in (select DeleteID from deleteAndUpdateIDS);
update Empirical_Prod.SuiteTenant a inner join deleteAndUpdateIDS b on a.SuiteTenantID=b.UpdateID set a.PropertyID=b.NewProperty,a.TenantStatusID=1,a.IsActive=1;*/