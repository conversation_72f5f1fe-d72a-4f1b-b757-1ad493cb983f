select count(*) from Empirical_DataStage.Bronze_Tenants_2024_04_23   as   bronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=  bronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
    -- set   bronze.City=c.CityName 
    where   bronze.City is  null and   bronze.BatchID in (  2442359100,   2442359200,   2442359000,   2442359300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    
/*
  update Empirical_DataStage.Bronze_Tenants_2024_04_23   as   bronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=  bronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
     set   bronze.City=c.CityName 
    where   bronze.City is  null and   bronze.BatchID in (  2442359100,   2442359200,   2442359000,   2442359300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    */
    
    select count(*) from Empirical_DataStage.Bronze_Tenants_2024_04_23   as   bronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=  bronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
    -- set   bronze.City=c.CityName 
    where   bronze.City is  null and   bronze.BatchID in (  2442359100,   2442359200,   2442359000,   2442359300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    
       select count(*) from Empirical_DataStage.Bronze_Tenants_2024_04_23   as   bronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=  bronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
    -- set   bronze.City=c.CityName 
    where bronze.City!=c.CityName and  bronze.City is not null and   bronze.BatchID in (  2442359100,   2442359200,   2442359000,   2442359300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    /* update Empirical_DataStage.Bronze_Tenants_2024_04_23   as   bronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=  bronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
     set   bronze.City=c.CityName 
    where  bronze.City!=c.CityName and bronze.City is not null and   bronze.BatchID in (  2442359100,   2442359200,   2442359000,   2442359300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
   
   
   **/
    
    select count(*) from Empirical_DataStage.Bronze_Tenants_2024_04_23   as   bronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=  bronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
    -- set   bronze.City=c.CityName 
    where bronze.City=c.CityName and  bronze.City is not null and   bronze.BatchID in (  2442359100,   2442359200,   2442359000,   2442359300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
      select count(*) from Empirical_DataStage.Bronze_Tenants_2024_04_23
      where State=59  and
       City is not null
       and CountryCode=14
      and 
      BatchID in (  2442359100,   2442359200,   2442359000,   2442359300);