
-- VIC





drop  temporary table if exists duplicateVendors;
create temporary table duplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.Bronze_Tenants_2024_04_23 where State=59 group by VendorID having count(*)>1
);
update  Empirical_DataStage.Bronze_Tenants_2024_04_23 set BatchID=2442359000 where Vendor<PERSON> in (select * from duplicateVendors);



drop temporary table   if exists  uniqueVendors;
 create temporary table  uniqueVendors(
 SELECT VendorID from  Empirical_DataStage.Bronze_Tenants_2024_04_23 where State=59  group by VendorID having count(*)=1)
;
 update  Empirical_DataStage.Bronze_Tenants_2024_04_23  set BatchID=**********  where VendorID in (
select * from uniqueVendors 
) ; 






 drop  temporary table   if exists  uniqueVendorsWithPropertyID;
  create temporary table  uniqueVendorsWithPropertyID(
 SELECT VendorID from  Empirical_DataStage.Bronze_Tenants_2024_04_23  where State=59 and BatchID =********** and PropertyID is not null)
;

  drop  temporary table   if exists uniqueVendorsWithPropertyIDInExistingTS;
   create temporary table  uniqueVendorsWithPropertyIDInExistingTS(
 select Vendor<PERSON>, ProviderID from  Empirical_DataStage.Tenants_Stage where Vendor<PERSON> in (
 select * from uniqueVendorsWithPropertyID)
 );

  update  Empirical_DataStage.Bronze_Tenants_2024_04_23  set BatchID=**********  where State=59 and VendorID in (
select VendorID from uniqueVendorsWithPropertyIDInExistingTS 
);


  drop  temporary table   if exists uniqueVendorsWithPropertyIDInExistingTSWIthBranchID;
   create temporary table  uniqueVendorsWithPropertyIDInExistingTSWIthBranchID(
  select * from   Empirical_DataStage.Tenants_Stage 
  where  VendorID in (select VendorID from uniqueVendorsWithPropertyIDInExistingTS )
   and BranchID is not null
  );
  
update Empirical_DataStage.Bronze_Tenants_2024_04_23  set BatchID=**********
  where BatchID =********** and State=59
  and VendorID in (select VendorID from uniqueVendorsWithPropertyIDInExistingTSWIthBranchID);
