
-- QLD

drop  temporary table if exists duplicateVendors;
create temporary table duplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.Bronze_Tenants_2024_04_23 where State=58 group by VendorID having count(*)>1
);
update  Empirical_DataStage.Bronze_Tenants_2024_04_23 set BatchID= 442358000 where Vendor<PERSON> in (select * from duplicateVendors);



drop temporary table   if exists  uniqueVendors;
 create temporary table  uniqueVendors(
 SELECT VendorID from  Empirical_DataStage.Bronze_Tenants_2024_04_23 where State=58  group by VendorID having count(*)=1)
;
 update  Empirical_DataStage.Bronze_Tenants_2024_04_23  set BatchID= 442358100  where VendorID in (
select * from uniqueVendors 
) ; 


 drop  temporary table   if exists  uniqueVendorsWithPropertyID;
  create temporary table  uniqueVendorsWithPropertyID(
 SELECT VendorID from  Empirical_DataStage.Bronze_Tenants_2024_04_23  where State=58 and BatchID = 442358100 and PropertyID is not null)
;

  drop  temporary table   if exists uniqueVendorsWithPropertyIDInExistingTS;
   create temporary table  uniqueVendorsWithPropertyIDInExistingTS(
 select Vendor<PERSON>, ProviderID from  Empirical_DataStage.Tenants_Stage where Vendor<PERSON> in (
 select * from uniqueVendorsWithPropertyID)
 );

  update  Empirical_DataStage.Bronze_Tenants_2024_04_23  set BatchID= 442358200  where State=58 and VendorID in (
select VendorID from uniqueVendorsWithPropertyIDInExistingTS 
);


  drop  temporary table   if exists uniqueVendorsWithPropertyIDInExistingTSWIthBranchID;
   create temporary table  uniqueVendorsWithPropertyIDInExistingTSWIthBranchID(
  select * from   Empirical_DataStage.Tenants_Stage 
  where  VendorID in (select VendorID from uniqueVendorsWithPropertyIDInExistingTS )
   and BranchID is not null
  );
  
update Empirical_DataStage.Bronze_Tenants_2024_04_23  set BatchID= 442358300
  where BatchID = 442358200 and State=58
  and VendorID in (select VendorID from uniqueVendorsWithPropertyIDInExistingTSWIthBranchID);
