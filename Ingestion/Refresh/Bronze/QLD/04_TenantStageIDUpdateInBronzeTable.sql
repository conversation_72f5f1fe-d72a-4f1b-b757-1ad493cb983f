

DROP TABLE IF EXISTS `Empirical_DataStage`.`TenantIllionVendorBronze`;

create table Empirical_DataStage.TenantIllionVendorBronze (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=6);

update Empirical_DataStage.Bronze_Tenants_2024_04_23 as a
inner join Empirical_DataStage.TenantIllionVendorBronze as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID where a.BatchID >=42358100 and a.BatchID <42358300;





DROP TABLE IF EXISTS `Empirical_DataStage`.`TenantIllionVendorBronze`;

create table Empirical_DataStage.TenantIllionVendorBronze (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=15);

update Empirical_DataStage.Bronze_Tenants_2024_04_23 as a
inner join Empirical_DataStage.TenantIllionVendorBronze as b on a.VendorID=b.Vendor<PERSON>
set a.Tenant_Stage_ID=b.Tenant_Stage_ID where a.BatchID >=42358100 and a.<PERSON>chID <42358300;




DROP TABLE IF EXISTS `Empirical_DataStage`.`TenantIllionVendorBronze`;

create table Empirical_DataStage.TenantIllionVendorBronze (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=15);

update Empirical_DataStage.Bronze_Tenants_2024_04_23 as a
inner join Empirical_DataStage.TenantIllionVendorBronze as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID where a.BatchID >=42358100 and a.BatchID <42358300;


DROP TABLE IF EXISTS `Empirical_DataStage`.`TenantIllionVendorBronze`;

create table Empirical_DataStage.TenantIllionVendorBronze (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=16);

update Empirical_DataStage.Bronze_Tenants_2024_04_23 as a
inner join Empirical_DataStage.TenantIllionVendorBronze as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID where a.BatchID >=42358100 and a.BatchID <42358300;



DROP TABLE IF EXISTS `Empirical_DataStage`.`TenantIllionVendorBronze`;

create table Empirical_DataStage.TenantIllionVendorBronze (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=18);

update Empirical_DataStage.Bronze_Tenants_2024_04_23 as a
inner join Empirical_DataStage.TenantIllionVendorBronze as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID where a.BatchID >=42358100 and a.BatchID <42358300;


DROP TABLE IF EXISTS `Empirical_DataStage`.`TenantIllionVendorBronze`;

create table Empirical_DataStage.TenantIllionVendorBronze (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=19);

update Empirical_DataStage.Bronze_Tenants_2024_04_23 as a
inner join Empirical_DataStage.TenantIllionVendorBronze as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID where a.BatchID >=42358100 and a.BatchID <42358300;