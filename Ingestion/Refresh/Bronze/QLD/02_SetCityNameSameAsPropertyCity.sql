select count(*) from Empirical_DataStage.Bronze_Tenants_2024_04_23   as   bronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=  bronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
    -- set   bronze.City=c.CityName 
    where   bronze.City is  null and   bronze.BatchID in (  442358100,   442358200,   442358000,   442358300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    
/*
  update Empirical_DataStage.Bronze_Tenants_2024_04_23   as   bronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=  bronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
     set   bronze.City=c.CityName 
    where   bronze.City is  null and   bronze.BatchID in (  442358100,   442358200,   442358000,   442358300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    */
    
    select count(*) from Empirical_DataStage.Bronze_Tenants_2024_04_23   as   bronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=  bronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
    -- set   bronze.City=c.CityName 
    where   bronze.City is  null and   bronze.BatchID in (  442358100,   442358200,   442358000,   442358300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    
       select count(*) from Empirical_DataStage.Bronze_Tenants_2024_04_23   as   bronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=  bronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
    -- set   bronze.City=c.CityName 
    where bronze.City!=c.CityName and  bronze.City is not null and   bronze.BatchID in (  442358100,   442358200,   442358000,   442358300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    /* update Empirical_DataStage.Bronze_Tenants_2024_04_23   as   bronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=  bronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
     set   bronze.City=c.CityName 
    where  bronze.City!=c.CityName and bronze.City is not null and   bronze.BatchID in (  442358100,   442358200,   442358000,   442358300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
   
   
   **/
    
    select count(*) from Empirical_DataStage.Bronze_Tenants_2024_04_23   as   bronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=  bronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
    -- set   bronze.City=c.CityName 
    where bronze.City=c.CityName and  bronze.City is not null and   bronze.BatchID in (  442358100,   442358200,   442358000,   442358300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
      select count(*) from Empirical_DataStage.Bronze_Tenants_2024_04_23
      where State=58  and
       City is not null
       and CountryCode=14
      and 
      BatchID in (  442358100,   442358200,   442358000,   442358300);