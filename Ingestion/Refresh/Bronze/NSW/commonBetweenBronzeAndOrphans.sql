
drop  table if exists commonBetweenBronzeAndOrphans;
create  table commonBetweenBronzeAndOrphans(
select  b.<PERSON>endor<PERSON>, b.<PERSON>,b.ProviderID, r.VendorID as orphanVendorID, b.State, b.<PERSON>, r.TSI_State
from Empirical_DataStage.Bronze_Tenants_2024_04_23 as b 
inner join Empirical_DataStage.Orphan_Tenants_2024_04_23 as r on r.VendorID = b.VendorID
);

-- create 
select count(*),ProviderID from commonBetweenBronzeAndOrphans  group by ProviderID having count(*)>1;

select count(*)  from Empirical_DataStage.Orphan_Tenants_2024_04_23 where Vendor<PERSON> in ( select VendorID
from commonBetweenBronzeAndOrphans)
 ;
 
delete from Empirical_DataStage.Orphan_Tenants_2024_04_23 where VendorID in ( select VendorID
from commonBetweenBronzeAndOrphans)
 ;