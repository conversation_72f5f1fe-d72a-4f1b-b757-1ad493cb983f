
-- DownGrade

  --  1 check April22 orphans with Tenant Stage if PropertyID Mapped and Modify =Import
    --   not part of latest bronze



drop temporary table if exists  tenanstExistInNewOrphansAndTSandNotModifyByRST;
create temporary table tenanstExistInNewOrphansAndTSandNotModifyByRST(
select b.VendorID, b.ProviderID, b.PropertyID
from Empirical_DataStage.Orphan_Tenants_2024_04_23 as b 
inner join Empirical_DataStage.Tenants_Stage
 as ts on ts.VendorID = b.VendorID
where ts.ModifiedBy=22 and ts.PropertyID is not null -- and ts.BranchID is null
);


drop temporary table if exists  tenanstNotinBronzeandExistInNewOrphansAndTSandNotModifyByRST;
create temporary table tenanstNotinBronzeandExistInNewOrphansAndTSandNotModifyByRST(
select * 
from tenanstExistInNewOrphansAndTSandNotModifyByRST where VendorID not in(
select Vendor<PERSON> from Empirical_DataStage.Bronze_Tenants_2024_04_23 where Batch<PERSON> in (4231000,4232000,4233000))
);


update Empirical_DataStage.Orphan_Tenants_2024_04_23 set BatchID=4236000 -- 42357600
where VendorID in (select VendorID from tenanstNotinBronzeandExistInNewOrphansAndTSandNotModifyByRST);
