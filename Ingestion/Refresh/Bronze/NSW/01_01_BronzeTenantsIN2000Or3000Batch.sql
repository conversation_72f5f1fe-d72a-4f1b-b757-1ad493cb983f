drop temporary table if exists BronzeTenantsIN2000Or3000;
create temporary table BronzeTenantsIN2000Or3000(
select distinct a.* from Empirical_DataStage.Bronze_Tenants_2024_04_23 a
inner join Empirical_Prod.Address b on a.PropertyID=b.ParentID and b.ParentTableID=1 and b.Sequence=1 and b.IsActive=1
where b.<PERSON><PERSON><PERSON> in (3000, 2000)
);

update Empirical_DataStage.Bronze_Tenants_2024_04_23 set BatchID=4234000
where Bronze_Tenants in (select Bronze_Tenants from BronzeTenantsIN2000Or3000);
