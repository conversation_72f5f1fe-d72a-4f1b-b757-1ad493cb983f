--  NSW

drop  temporary table if exists duplicateVendors;
create temporary table duplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.Bronze_Tenants_2024_04_23  where (BatchID not in (4234000,4235000) or Batch<PERSON> is null) group by VendorID having count(*)>1
);
update  Empirical_DataStage.Bronze_Tenants_2024_04_23 set BatchID=4230000 where VendorID in (select * from duplicateVendors);

drop  temporary table if exists duplicateVendors;
create temporary table duplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.Bronze_Tenants_2024_04_23  where  BatchID is null group by VendorID having count(*)>1
);
update  Empirical_DataStage.Bronze_Tenants_2024_04_23 set BatchID=4230000 where Vendor<PERSON> in (select * from duplicateVendors);


SELECT distinct BatchID,count(*) from  Empirical_DataStage.Bronze_Tenants_2024_04_23  group by BatchID ;

drop temporary table   if exists  uniqueVendors;
 create temporary table  uniqueVendors(
 SELECT VendorID from  Empirical_DataStage.Bronze_Tenants_2024_04_23  where (<PERSON><PERSON><PERSON> is null or Batch<PERSON> not in (4234000,4235000,4230000))  group by VendorID having count(*)=1)
;
 update  Empirical_DataStage.Bronze_Tenants_2024_04_23  set BatchID=4231000  where VendorID in (
select * from uniqueVendors 
) ; 






 drop  temporary table   if exists  uniqueVendorsWithPropertyID;
  create temporary table  uniqueVendorsWithPropertyID(
 SELECT VendorID from  Empirical_DataStage.Bronze_Tenants_2024_04_23  where BatchID =4231000 and PropertyID is not null)
;

  drop  temporary table   if exists uniqueVendorsWithPropertyIDInExistingTS;
   create temporary table  uniqueVendorsWithPropertyIDInExistingTS(
 select VendorID, ProviderID from  Empirical_DataStage.Tenants_Stage where VendorID in (
 select * from uniqueVendorsWithPropertyID)
 );

  update  Empirical_DataStage.Bronze_Tenants_2024_04_23  set BatchID=4232000  where   VendorID in (
select VendorID from uniqueVendorsWithPropertyIDInExistingTS 
);


  drop  temporary table   if exists uniqueVendorsWithPropertyIDInExistingTSWIthBranchID;
   create temporary table  uniqueVendorsWithPropertyIDInExistingTSWIthBranchID(
  select * from   Empirical_DataStage.Tenants_Stage 
  where  VendorID in (select VendorID from uniqueVendorsWithPropertyIDInExistingTS )
   and BranchID is not null
  );
  
update Empirical_DataStage.Bronze_Tenants_2024_04_23  set BatchID=4233000
  where BatchID =4232000 
  and VendorID in (select VendorID from uniqueVendorsWithPropertyIDInExistingTSWIthBranchID);
