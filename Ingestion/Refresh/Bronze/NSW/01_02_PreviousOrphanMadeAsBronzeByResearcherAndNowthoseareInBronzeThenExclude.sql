drop temporary table if exists OldOrphansTenantsNowINBronze;
create temporary table OldOrphansTenantsNowINBronze(
select b.Vendor<PERSON>, b.ProviderID, b.PropertyID
from Empirical_DataStage.Bronze_Tenants_2024_04_23 as b 
inner join Empirical_DataStage.NSW_Raw_Orphan_Illion_Tenants as r on r.VendorID = b.VendorID
where b.State=57

union 

select b.VendorID, b.ProviderID,  b.PropertyID
from Empirical_DataStage.Bronze_Tenants_2024_04_23 as b 
inner join Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants as r on r.VendorID = b.VendorID
where b.State=57

union

select b.VendorID, b.ProviderID, b.PropertyID
from Empirical_DataStage.Bronze_Tenants_2024_04_23 as b 
inner join Empirical_DataStage.VIC_Raw_Orphan_Illion_Tenants as r on r.VendorID = b.VendorID
where b.State=59

union 

select b.VendorID, b.ProviderID,  b.PropertyID
from Empirical_DataStage.Bronze_Tenants_2024_04_23 as b 
inner join Empirical_DataStage.VIC_Raw_Orphan_Zoominfo_Tenants as r on r.VendorID = b.VendorID
where b.State=59

union 

select b.VendorID, b.ProviderID, b.PropertyID
from Empirical_DataStage.Bronze_Tenants_2024_04_23 as b 
inner join Empirical_DataStage.QLD_Raw_Orphan_Illion_Tenants as r on r.VendorID = b.VendorID
where b.State=58

union 

select b.VendorID, b.ProviderID,  b.PropertyID
from Empirical_DataStage.Bronze_Tenants_2024_04_23 as b 
inner join Empirical_DataStage.QLD_Raw_Orphan_Zoominfo_Tenants as r on r.VendorID = b.VendorID
where b.State=58
);

drop temporary table if exists OldOrphansTenantsNowINBronzeButAlreadyMappedRST;
create temporary table OldOrphansTenantsNowINBronzeButAlreadyMappedRST(
select b.VendorID from OldOrphansTenantsNowINBronze as b
 inner join Tenants_Stage as ts 
 on b.VendorID = ts.VendorID where ts.PropertyID is not null  and ts.ProviderID not in (14) and ts.ModifiedBy!=22
 );


update Empirical_DataStage.Bronze_Tenants_2024_04_23 set BatchID=4235000
where VendorID in (select VendorID from OldOrphansTenantsNowINBronzeButAlreadyMappedRST);


