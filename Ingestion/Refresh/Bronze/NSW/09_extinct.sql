drop  temporary table if exists currentTenantsRecordsfromallDataSet;
create temporary table currentTenantsRecordsfromallDataSet(
select Vendor<PERSON> from Empirical_DataStage.Bronze_Tenants_2024_04_23
union
select Vendor<PERSON> from Empirical_DataStage.Gold_Raw_Confirmed_Tenants_2024_04_23
union
select Vendor<PERSON> from Empirical_DataStage.Orphan_Tenants_2024_04_23
);

select count(*) from Empirical_DataStage.Tenants_Stage as ts
left join Empirical_DataStage.currentTenantsRecordsfromallDataSet as c on c.VendorID=ts.VendorID
where c.VendorID is null;
;


--  6581251
-- 4688430

