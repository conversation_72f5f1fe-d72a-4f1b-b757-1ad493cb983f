Update Empirical_DataStage.Orphan_Tenants_2024_04_23 set BatchID=null where <PERSON>ch<PERSON> not in (4236000,4236001,4236009);

drop  temporary table if exists  OrphanVendors;
create temporary table OrphanVendors(
SELECT  VendorID FROM Empirical_DataStage.Orphan_Tenants_2024_04_23 where <PERSON>ch<PERSON> is null);

drop  temporary table if exists  OrphanduplicateVendors;
create temporary table OrphanduplicateVendors(
SELECT  VendorID FROM OrphanVendors group by VendorID having count(*)>1);

update  Empirical_DataStage.Orphan_Tenants_2024_04_23  set BatchID=42320000  where Vendor<PERSON> in (
select * from OrphanduplicateVendors 
) and (BatchID not in (4236000,4236001,4236009) or BatchID is null);

drop temporary table if exists OrphanuniqueVendors;
 create temporary table OrphanuniqueVendors(
 SELECT VendorID FROM OrphanVendors group by VendorID having count(*)=1)
;

 update  Empirical_DataStage.Orphan_Tenants_2024_04_23  set BatchID=42329000  where Vendor<PERSON> in (
select * from OrphanuniqueVendors 
)  and (Batch<PERSON> not in (4236000,4236001,4236009) or BatchID is null);

  drop  temporary table if exists OrphanuniqueVendorsInExistingTS;
   create temporary table OrphanuniqueVendorsInExistingTS(
 select VendorID, ProviderID, ModifiedBy from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select * from OrphanuniqueVendors) and ProviderID in (6,15,16,18,19)
 );
 
  update  Empirical_DataStage.Orphan_Tenants_2024_04_23  set BatchID=42325000  where VendorID in (
select VendorID from OrphanuniqueVendorsInExistingTS where ModifiedBy!=22 and ModifiedBy is not null
)  and (BatchID not in (4236000,4236001,4236009) or BatchID is null);


  update  Empirical_DataStage.Orphan_Tenants_2024_04_23  set BatchID=42322000  where VendorID in (
select VendorID from OrphanuniqueVendorsInExistingTS where (ModifiedBy=22 or ModifiedBy is null)
)  and (BatchID not in (4236000,4236001,4236009) or BatchID is null);


 drop  temporary table if exists OrphanuniqueVendorsInExistingTSWithPropertyID;
   create temporary table OrphanuniqueVendorsInExistingTSWithPropertyID(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select VendorID from OrphanuniqueVendorsInExistingTS where (ModifiedBy=22 or ModifiedBy is null)) and 
 PropertyID is not null and BranchID is not null
 );
 
  update  Empirical_DataStage.Orphan_Tenants_2024_04_23  set BatchID=42323000  where VendorID in (
select VendorID from OrphanuniqueVendorsInExistingTSWithPropertyID 
) and (BatchID not in (4236000,4236001,4236009) or BatchID is null);

  drop  temporary table if exists VendorsInExTSWithPropertyIDEmptyHavingBranchID;
   create temporary table VendorsInExTSWithPropertyIDEmptyHavingBranchID(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select VendorID from OrphanuniqueVendorsInExistingTS  where (ModifiedBy=22 or ModifiedBy is null)) and 
 BranchID is Not null and PropertyID is null
 );
 
  update  Empirical_DataStage.Orphan_Tenants_2024_04_23  set BatchID=42328000  where VendorID in (
select VendorID from VendorsInExTSWithPropertyIDEmptyHavingBranchID 
)  and (BatchID not in (4236000,4236001,4236009) or BatchID is null);


select count(*),BatchID from Empirical_DataStage.Orphan_Tenants_2024_04_23 group by BatchID;