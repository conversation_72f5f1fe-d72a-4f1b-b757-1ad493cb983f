drop temporary table if exists temp1;
create temporary table temp1(
select a.Tenant_Stage_ID from Empirical_DataStage.Tenants_Stage a inner join Empirical_DataStage.Orphan_Tenants_2024_04_23 b on a.VendorID=b.VendorID where b.BatchID=42322000
); 

-- delete from Empirical_Prod.NationalIdentifiers where Tenant_Stage_ID in (select Tenant_Stage_Id from temp1 );
-- delete from Empirical_DataStage.Tenants_Stage where PropertyID is null and Tenant_Stage_ID in (select Tenant_Stage_Id from temp1 );
