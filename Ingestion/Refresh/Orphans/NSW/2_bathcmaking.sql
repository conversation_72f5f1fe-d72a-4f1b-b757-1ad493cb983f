




drop  temporary table if exists  OrphanduplicateVendors;
create temporary table OrphanduplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.Orphan_Tenants_2024_04_23  group by VendorID having count(*)>1);

/**
update  Empirical_DataStage.Orphan_Tenants_2024_04_23  set BatchID=12000  where Vendor<PERSON> in (
select * from OrphanduplicateVendors 
);
**/



drop temporary table if exists OrphanuniqueVendors;
 create temporary table OrphanuniqueVendors(
 SELECT VendorID FROM Empirical_DataStage.Orphan_Tenants_2024_04_23  group by VendorID having count(*)=1)
;

/**
 update  Empirical_DataStage.Orphan_Tenants_2024_04_23  set BatchID=12100  where VendorID in (
select * from OrphanuniqueVendors 
) ; 
**/

 

  drop  temporary table if exists OrphanuniqueVendorsInExistingTS;
   create temporary table OrphanuniqueVendorsInExistingTS(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select * from OrphanuniqueVendors) and ProviderID=6
 );
 

  update  Empirical_DataStage.Orphan_Tenants_2024_04_23  set BatchID=12200  where VendorID in (
select VendorID from OrphanuniqueVendorsInExistingTS 
);



 drop  temporary table if exists OrphanuniqueVendorsInExistingTSWithPropertyID;
   create temporary table OrphanuniqueVendorsInExistingTSWithPropertyID(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select VendorID from OrphanuniqueVendorsInExistingTS) and ProviderID=6 and PropertyID is not null
 );
 
/**
  update  Empirical_DataStage.Orphan_Tenants_2024_04_23  set BatchID=12300  where VendorID in (
select VendorID from OrphanuniqueVendorsInExistingTSWithPropertyID 
);

**/

drop  temporary table if exists  OrphanduplicateVendors;
create temporary table OrphanduplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.Orphan_Tenants_2024_04_23  group by VendorID having count(*)>1);

  drop  temporary table if exists OUVendorsInExTSWithPropertyIDEmptyHavingBranchID;
   create temporary table OUVendorsInExTSWithPropertyIDEmptyHavingBranchID(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select * from OrphanuniqueVendors) and ProviderID=6 and BranchID is Not null and PropertyID is null
 );
 
/**
  update  Empirical_DataStage.Orphan_Tenants_2024_04_23  set BatchID=12600  where VendorID in (
select VendorID from OUVendorsInExTSWithPropertyIDEmptyHavingBranchID 
);
**/

drop temporary table if exists tempGoldRawIllionTenants;
create temporary table tempGoldRawIllionTenants(
select nroit.VendorID from Empirical_DataStage.Orphan_Tenants_2024_04_23 as nroit
inner join Empirical_DataStage._Raw_Confirmed_Tenants as nrct on nrct.VendorID=nroit.VendorID
);

drop temporary table if exists tempBronzeRawIllionTenants;
create temporary table tempBronzeRawIllionTenants(
select nroit.VendorID from Empirical_DataStage.Orphan_Tenants_2024_04_23 as nroit
inner join Empirical_DataStage._Bronze_Tenants_With_ProviderID_V2 as nbtwp on nbtwp.VendorID=nroit.VendorID and nbtwp.ProviderID=6
);

-- update Empirical_DataStage.Orphan_Tenants_2024_04_23  set BatchID=12400 where VendorID in (select * from tempGoldRawIllionTenants);
-- update Empirical_DataStage.Orphan_Tenants_2024_04_23  set BatchID=12500 where VendorID in (select * from tempBronzeRawIllionTenants);