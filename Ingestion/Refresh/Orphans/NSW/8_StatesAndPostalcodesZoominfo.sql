update Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants set BatchID=212700 where (PostalCode<1000 or PostalCode>2599) and (PostalCode<2619 or PostalCode>2899)  and (PostalCode<2921 or PostalCode>2999);

update Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants set BatchID=212700 where VendorId in (select VendorID from Empirical_DataStage.Tenants_Stage   
where (PostalCode<1000 or PostalCode>2599) and (PostalCode<2619 or PostalCode>2899)  and (PostalCode<2921 or PostalCode>2999) and PropertyID is null and StateAbbr='NSW');

select count(*) from Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants where (PostalCode<1000 or PostalCode>2599) and (PostalCode<2619 or PostalCode>2899)  and (PostalCode<2921 or PostalCode>2999);
select count(*) from Empirical_DataStage.Tenants_Stage   
where (PostalCode<1000 or PostalCode>2599) and (PostalCode<2619 or PostalCode>2899)  and (PostalCode<2921 or PostalCode>2999) and PropertyID is null and StateAbbr='NSW';
