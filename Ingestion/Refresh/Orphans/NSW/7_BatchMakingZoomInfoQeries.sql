drop  temporary table if exists  NSWOrphanduplicateVendors;
create temporary table NSWOrphanduplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants  group by VendorID having count(*)>1);

update  Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants  set BatchID=212000  where VendorID in (
select * from NSWOrphanduplicateVendors 
);

drop temporary table if exists NSWOrphanuniqueVendors;
 create temporary table NSWOrphanuniqueVendors(
 SELECT VendorID FROM Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants  group by VendorID having count(*)=1)
;

 update  Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants  set BatchID=212100  where VendorID in (
select * from NSWOrphanuniqueVendors 
) ; 

  drop  temporary table if exists NSWOrphanuniqueVendorsInExistingTS;
   create temporary table NSWOrphanuniqueVendorsInExistingTS(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select * from NSWOrphanuniqueVendors) and ProviderID=15
 );
 
update  Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants  set BatchID=212200  where VendorID in (
select VendorID from NSWOrphanuniqueVendorsInExistingTS 
);

 drop  temporary table if exists NSWOrphanuniqueVendorsInExistingTSWithPropertyID;
   create temporary table NSWOrphanuniqueVendorsInExistingTSWithPropertyID(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select VendorID from NSWOrphanuniqueVendorsInExistingTS) and ProviderID=15 and PropertyID is not null
 );
 

  update  Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants  set BatchID=212300  where VendorID in (
select VendorID from NSWOrphanuniqueVendorsInExistingTSWithPropertyID 
);

drop  temporary table if exists  NSWOrphanduplicateVendors;
create temporary table NSWOrphanduplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants  group by VendorID having count(*)>1);

  drop  temporary table if exists NSWOUVendorsInExTSWithPropertyIDEmptyHavingBranchID;
   create temporary table NSWOUVendorsInExTSWithPropertyIDEmptyHavingBranchID(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where 
 VendorID in (select * from NSWOrphanuniqueVendors) and ProviderID=15 and BranchID is Not null and PropertyID is null
 );
 
  update  Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants  set BatchID=212600  where VendorID in (
select VendorID from NSWOUVendorsInExTSWithPropertyIDEmptyHavingBranchID 
);

drop temporary table if exists tempNSWGoldRawIllionTenants;
create temporary table tempNSWGoldRawIllionTenants(
select nroit.VendorID from Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants as nroit
inner join Empirical_DataStage.NSW_Raw_Confirmed_Tenants as nrct on nrct.VendorID=nroit.VendorID
);

drop temporary table if exists tempNSWBronzeRawIllionTenants;
create temporary table tempNSWBronzeRawIllionTenants(
select nroit.VendorID from Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants as nroit
inner join Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 as nbtwp on nbtwp.VendorID=nroit.VendorID and nbtwp.ProviderID=6
);

 update Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants  set BatchID=212400 where VendorID in (select * from tempNSWGoldRawIllionTenants);
update Empirical_DataStage.NSW_Raw_Orphan_Zoominfo_Tenants  set BatchID=212500 where VendorID in (select * from tempNSWBronzeRawIllionTenants);