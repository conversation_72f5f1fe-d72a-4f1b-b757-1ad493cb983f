drop temporary table if exists temp1;
create temporary table temp1(
select Vendor<PERSON> from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 group by VendorID having count(*)>1
);

drop temporary table if exists tempRCT;
create temporary table tempRCT(
select * from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where Vendor<PERSON> in (select * from temp1) #and BatchID in ('442345300','42345300','442346300','42346300','42347300','442347300','442335300','42335300','442336300','42336300','42337300','442337300')
);

select distinct BatchID from tempRCT;

-- select count(*) from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where VendorID in (select * from temp1);

drop temporary table if exists TenantsIN2000Or3000;
create temporary table TenantsIN2000Or3000(
select distinct a.* from tempRCT a
inner join Empirical_Prod.Address b on a.PropertyID=b.ParentID and b.ParentTableID=1 and b.Sequence=1 and b.IsActive=1
where b.<PERSON>ipC<PERSON> in (3000, 2000) #and a.TSI_Bucket='Gold' and a.TSI_State='NSW'
);

update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335500
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from TenantsIN2000Or3000) and TSI_Bucket='Gold' and TSI_State='NSW';
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42336500
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from TenantsIN2000Or3000) and TSI_Bucket='Gold' and TSI_State='VIC';
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42337500
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from TenantsIN2000Or3000) and TSI_Bucket='Gold' and TSI_State='QLD';
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42345500
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from TenantsIN2000Or3000) and TSI_Bucket='Silver' and TSI_State='NSW';
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42346500
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from TenantsIN2000Or3000) and TSI_Bucket='Silver' and TSI_State='VIC';
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42347500
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from TenantsIN2000Or3000) and TSI_Bucket='Silver' and TSI_State='QLD';


-- ---- Lease related Batches --------


drop temporary table if exists tempLeaseCT;
create temporary table tempLeaseCT(
select distinct a.*,b.ConfirmedTenantID from tempRCT a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 #and  a.BatchID in (42335000,42335100,42335200) and a.TSI_Bucket='Gold' and a.TSI_State='NSW'
);

drop temporary table if exists tempLeases;
create temporary table tempLeases(
select VendorID,PropertyID from Empirical_DataStage.Tenants_Stage
where BranchID in (select CompanyID from Empirical_Tenants.ConfirmedTenants 
    where ConfirmedTenantID in (select distinct ConfirmedTenantID from tempLeaseCT)) and ProviderID in (6,15) #and b.BatchID in (42335000,42335100,42335200)and b.TSI_Bucket='Gold' and b.TSI_State='NSW'
    );
    

drop temporary table if exists tempLeaseTenants;
create temporary table tempLeaseTenants(
select a.* from tempRCT a inner join tempLeases b on a.VendorID=b.VendorID
);

update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42335600 -- Existing Tenants with Lease
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from tempLeaseTenants) and TSI_Bucket='Gold' and TSI_State='NSW';
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42336600 -- Existing Tenants with Lease
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from tempLeaseTenants) and TSI_Bucket='Gold' and TSI_State='VIC';
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42337600 -- Existing Tenants with Lease
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from tempLeaseTenants) and TSI_Bucket='Gold' and TSI_State='QLD';
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42345600 -- Existing Tenants with Lease
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from tempLeaseTenants) and TSI_Bucket='Silver' and TSI_State='NSW';
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42346600 -- Existing Tenants with Lease
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from tempLeaseTenants) and TSI_Bucket='Silver' and TSI_State='VIC';
update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 set BatchID=42347600 -- Existing Tenants with Lease
where Raw_Confirmed_TenantsID in (select Raw_Confirmed_TenantsID from tempLeaseTenants) and TSI_Bucket='Silver' and TSI_State='QLD';
