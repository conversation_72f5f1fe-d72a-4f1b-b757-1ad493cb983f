CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Suite_And_Floor_Import_Gold_Refresh_test`(P_BatchID INT)
BEGIN

	DECLARE JSONTEXT text;
    DECLARE minConfirmedTenantID INT;
DECLARE batchSize INT DEFAULT 50;
DECLARE offsetValue INT DEFAULT 0;
    -- Create temporary table tempTenantStageALA
    DROP TEMPORARY TABLE IF EXISTS tempTenantStageALA;
    CREATE TEMPORARY TABLE tempTenantStageALA AS
    SELECT 
        Tenant_Stage_Id, 
        VendorID,
        PropertyID,
        TenantName,
        FloorNumber, 
        FloorID,
        SuiteNumberDisplay
    FROM 
        Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 rct
    WHERE 
        (IsSuiteandFloorProcessed = 0 OR IsSuiteandFloorProcessed IS NULL) AND
        BatchID = P_BatchID;

    -- Create temporary table tempTenantStage
    DROP TEMPORARY TABLE IF EXISTS tempTenantStage;
    CREATE TEMPORARY TABLE tempTenantStage AS
    SELECT 
        tt.*, 
        t.Address2,
        t.State,
        t.PostalCode,
        t.BranchID,
        t.ConfirmedTenantID
    FROM 
        Empirical_DataStage.Tenants_Stage t 
    INNER JOIN 
        tempTenantStageALA tt ON t.Tenant_Stage_Id = tt.Tenant_Stage_Id;

    -- Create temporary table tempActiveSuiteTenantDetails
    DROP TEMPORARY TABLE IF EXISTS tempActiveSuiteTenants;
    CREATE TEMPORARY TABLE tempActiveSuiteTenants AS
    SELECT 
        PropertyID, 
        ConfirmedTenantID,
        SuiteID,
        SuiteTenantID
    FROM 
        Empirical_Prod.SuiteTenant where ConfirmedTenantID in (select ConfirmedTenantID from tempTenantStage) and IsActive = 1 and TenantStatusID = 1;

    DROP TEMPORARY TABLE IF EXISTS tempActiveSuiteTenantDetails;
    CREATE TEMPORARY TABLE tempActiveSuiteTenantDetails AS
    SELECT 
        st.PropertyID, 
        st.ConfirmedTenantID,
        st.SuiteID,
        st.SuiteTenantID,
        tt.FloorNumber,
        tt.FloorID,
        tt.SuiteNumberDisplay
    FROM 
        tempActiveSuiteTenants st 
    INNER JOIN 
        tempTenantStage tt ON st.ConfirmedTenantID = tt.ConfirmedTenantID;

    -- Create temporary table tempExistingSuite
    DROP TEMPORARY TABLE IF EXISTS tempExistingSuite;
    CREATE TEMPORARY TABLE tempExistingSuite AS
    SELECT 
        s.SuiteID,
        t.Tenant_Stage_ID,
        t.ConfirmedTenantID,
        t.PropertyID,
        t.BranchID 
    FROM 
        Empirical_Prod.Suite s 
    INNER JOIN 
        tempTenantStage t ON IFNULL(s.PropertyID,0) = IFNULL(t.PropertyID,0) 
        AND IFNULL(s.FloorID,0) = IFNULL(t.FloorID,0) 
        AND IFNULL(s.SuiteNumber,0) = IFNULL(t.SuiteNumberDisplay,0);

    -- Create temporary table tempNonExistingSuite
    DROP TEMPORARY TABLE IF EXISTS tempNonExistingSuite;
    CREATE TEMPORARY TABLE tempNonExistingSuite AS
    SELECT * 
    FROM tempTenantStage 
    WHERE Tenant_Stage_ID NOT IN (SELECT Tenant_Stage_ID FROM tempExistingSuite);

    -- Insert into Suite table for non-existing suites
    INSERT INTO Empirical_Prod.`Suite`(`FloorID`,`PropertyID`,`SuiteNumber`,`ModifiedBy`,`ModifiedDate`,`CreatedBy`,`CreatedDate`)
    SELECT 
        FloorID,
        PropertyID,
        SuiteNumberDisplay,
        22,
        CURRENT_TIMESTAMP(),
        22,
        CURRENT_TIMESTAMP() 
    FROM tempNonExistingSuite;

    -- Create temporary table tempReferenceDetails
    DROP TEMPORARY TABLE IF EXISTS tempReferenceDetailsForFields;
    CREATE TEMPORARY TABLE tempReferenceDetailsForFields AS
    SELECT 
        Address2,
        FloorNumber,
        VendorID
    FROM 
        Empirical_DataStage.Raw_Confirmed_Tenants_Fields_Reference_2024_04_23 where VendorID in (select VendorID from tempTenantStage);

    DROP TEMPORARY TABLE IF EXISTS tempReferenceDetails;
    CREATE TEMPORARY TABLE tempReferenceDetails AS
    SELECT 
        ref.Address2,
        ref.FloorNumber AS Ref_Floor,
        tt.SuiteNumberDisplay,
        tt.FloorNumber,
        ref.VendorID,
        tt.Tenant_Stage_ID,
        ct.ConfirmedTenantID
    FROM 
        tempReferenceDetailsForFields ref
    INNER JOIN 
        tempTenantStage tt ON tt.VendorID = ref.VendorID
    INNER JOIN 
        Empirical_Tenants.ConfirmedTenants ct ON ct.CompanyID = tt.BranchID
    WHERE 
        ct.ProviderID = 5;

    -- Iterate through each ConfirmedTenantID in tempReferenceDetails
    SELECT MIN(ConfirmedTenantID) INTO @minConfirmedTenantID FROM tempReferenceDetails;
        
    WHILE @minConfirmedTenantID IS NOT NULL DO
     SET offsetValue = 0;
      DROP TEMPORARY TABLE IF EXISTS tempChangeLog;
        CREATE TEMPORARY TABLE tempChangeLog
        (
			ConfirmedTenantID INT,
            ProviderID INT,
            Source_Tenant_Record_ID INT,
            FieldID INT            	
        );

    WHILE offsetValue < batchSize DO
        SELECT 
            SuiteNumberDisplay,
            Address2,
            Tenant_Stage_ID,
            FloorNumber,
            Ref_Floor
        INTO 
            @SuiteNumberDisplay,
            @ref_Address2,
            @Tenant_Stage_ID,
            @FloorNumber,
            @ref_FloorNumber
        FROM 
            tempReferenceDetails 
        WHERE 
            ConfirmedTenantID = @minConfirmedTenantID
            LIMIT 1 OFFSET offsetValue;
            

        -- Insert into tempChangeLog based on conditions
        IF TRIM(IFNULL(@SuiteNumberDisplay,'')) <> '' AND @ref_Address2 IS NOT NULL THEN
            INSERT INTO tempChangeLog
            SELECT @minConfirmedTenantID, @ref_Address2, @Tenant_Stage_ID, 6;
        END IF;

        IF TRIM(IFNULL(@FloorNumber,'')) <> '' AND @ref_FloorNumber IS NOT NULL THEN
            INSERT INTO tempChangeLog
            SELECT @minConfirmedTenantID, @ref_FloorNumber, @Tenant_Stage_ID, 30;
        END IF;

        SET offsetValue = offsetValue + 1;
        END WHILE;
        SELECT 
				CONCAT('[', FirstPass, ']') INTO JSONTEXT
			FROM
				(SELECT 
					GROUP_CONCAT('{', Jason1, '}'
							SEPARATOR ',') AS FirstPass
				FROM
					(
						SELECT 
						CONCAT('"ConfirmedTenantID":', '"', CL.ConfirmedTenantID, '"', ',',
								'"ProviderID":', '"', CL.ProviderID, '"', ',',
                                '"Source_Tenant_Record_ID":', '"', CL.Source_Tenant_Record_ID, '"', ',',
                                '"FieldID":', '"', CL.FieldID, '"'
							   ) AS Jason1
				FROM
					tempChangeLog CL) AS Jason2) AS Jason3;
		if JSONTEXT IS NOT NULL THEN
		 --  save change log
		  CALL Empirical_Prod.CRE_Property_Tenant_Matrix_Save_MergedView_Changelog_1(JSONTEXT,18);

		end if;
        -- Move to the next ConfirmedTenantID
        SELECT MIN(ConfirmedTenantID) INTO @minConfirmedTenantID FROM tempReferenceDetails WHERE ConfirmedTenantID > @minConfirmedTenantID ORDER BY ConfirmedTenantID;  
    END WHILE;
			

    -- Update ConfirmedTenants table
    UPDATE Empirical_Tenants.ConfirmedTenants ct 
    INNER JOIN tempTenantStage tt ON ct.CompanyID = tt.BranchID
    SET 
        ct.FloorNumber = tt.FloorNumber, 
        ct.Address2 = tt.SuiteNumberDisplay, 
        ct.ModifiedDate = CURRENT_TIMESTAMP(), 
        ct.ModifiedBy = 22
    WHERE 
        ct.ProviderID = 5;

    DROP TEMPORARY TABLE IF EXISTS tempSuiteInformation;
    CREATE TEMPORARY TABLE tempSuiteInformation AS
    SELECT tast.*,s.SuiteID as NewSuiteID from tempActiveSuiteTenantDetails tast
    INNER JOIN Empirical_Prod.Suite s ON IFNULL(s.PropertyID,0) = IFNULL(tast.PropertyID,0) 
        AND IFNULL(s.FloorID,0) = IFNULL(tast.FloorID,0) 
        AND IFNULL(s.SuiteNumber,0) = IFNULL(tast.SuiteNumberDisplay,0);

    -- Update SuiteTenant table
    UPDATE Empirical_Prod.SuiteTenant st
    INNER JOIN tempSuiteInformation tast ON st.SuiteTenantID = tast.SuiteTenantID
    SET 
        st.SuiteID = tast.NewSuiteID,
        st.ModifiedDate = CURRENT_TIMESTAMP(),
        st.ModifiedBy = 22 
    WHERE 
        st.PropertyID = tast.PropertyID 
        AND st.ConfirmedTenantID = tast.ConfirmedTenantID 
        AND st.IsActive = 1 
        AND st.TenantStatusID = 1;

    -- Update IsSuiteandFloorProcessed flag in Raw_Confirmed_Tenants_2024_04_23 table
    UPDATE Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 AS a
    INNER JOIN 
        tempTenantStageALA AS b ON a.Tenant_Stage_ID = b.Tenant_Stage_ID
    SET 
        a.IsSuiteandFloorProcessed = 1 
    WHERE 
        a.BatchID = P_BatchID;
END