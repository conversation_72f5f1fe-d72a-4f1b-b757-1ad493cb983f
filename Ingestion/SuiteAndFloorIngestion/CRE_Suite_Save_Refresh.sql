CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Suite_Save_Refresh`(
    IN P_SuiteID INT,
    IN P_PropertyID INT,
    IN P_FloorID INT,
    IN P_EntityId INT,
    IN P_SuiteNumber VARCHAR(45),
    OUT P_NewSuiteID INT
)
BEGIN
    SET @GlobalDateFormat = NULL;
    SELECT CTRY.DateFormatSave INTO @GlobalDateFormat FROM Empirical_Prod.Entity E 
    JOIN Empirical_Prod.Company C ON C.CompanyID = E.CompanyID
    JOIN Empirical_Prod.Country CTRY ON CTRY.CountryID = C.CountryID
    WHERE E.EntityID = P_EntityId;

    -- Find the Listing Group
    -- SET @GroupID = (SELECT GroupID FROM ListingGroup WHERE ListingID = P_ListingID AND SuiteID IS NULL LIMIT 1);

    -- Create Floor if no floors found
    /*
    IF P_FloorID IS NULL OR P_FloorID <= 0 THEN
        INSERT INTO Floor (PropertyID, FloorNumber) VALUES (P_PropertyID, '1');
        SET P_FloorID = LAST_INSERT_ID();
    END IF;
    */

    -- TODO: Consider PFS, PF, PS, P cases
    /*SELECT COUNT(*) INTO @count FROM Empirical_Prod.Suite 
    WHERE PropertyID = P_PropertyID AND FloorID = P_FloorID AND SuiteNumber = P_SuiteNumber;*/
if not exists (SELECT 1 FROM Empirical_Prod.Suite 
    WHERE ifnull(PropertyID,0) = ifnull(P_PropertyID,0) AND ifnull(FloorID,0) = ifnull(P_FloorID,0) AND ifnull(SuiteNumber,0) = ifnull(P_SuiteNumber,0)) then
   # IF @count < 1 THEN
        INSERT INTO Empirical_Prod.`Suite`
        (
            `FloorID`,
            `PropertyID`,
            `SuiteNumber`,
            `ModifiedBy`,
            `ModifiedDate`,
            `CreatedBy`,
            `CreatedDate`
        )
        VALUES
        (
            P_FloorID,
            P_PropertyID,
            P_SuiteNumber,
            P_EntityId,
            CURRENT_TIMESTAMP(),
            P_EntityId,
            CURRENT_TIMESTAMP()	
        );

        SET @NewSuiteId = LAST_INSERT_ID();

        /*
        IF P_FloorID IS NOT NULL AND P_FloorID <> "" THEN
            SELECT PropertyFloorStackID INTO @propertyFloorStackID FROM PropertyFloorStack 
            WHERE PropertyID = P_PropertyID AND FloorID = P_FloorID AND IsActive = 1;

            IF @propertyFloorStackID IS NOT NULL AND @propertyFloorStackID <> "" THEN
                CALL `CRE_Property_FormattedFloors_Save`(
                    P_PropertyID,       
                    @propertyFloorStackID, 
                    P_EntityId,   
                    @NewSuiteId,
                    3
                );
            END IF;
        END IF;
        */

        SET P_NewSuiteID = @NewSuiteId;
        
	-- END IF;
    ELSE
		SELECT SuiteID INTO @SuiteID FROM Empirical_Prod.Suite 
    WHERE ifnull(PropertyID,0) = ifnull(P_PropertyID,0) AND ifnull(FloorID,0) = ifnull(P_FloorID,0) AND ifnull(SuiteNumber,0) = ifnull(P_SuiteNumber,0) order by ModifiedDate desc limit 1;
    
    SET P_NewSuiteID = @SuiteID;
        /*
        UPDATE `Suite`
        SET
        `FloorID` = P_FloorID,
        `PropertyID` = P_PropertyID,
        `ModifiedBy` = P_EntityId,
        `ModifiedDate` = CURRENT_TIMESTAMP()
        WHERE `SuiteNumber` = P_SuiteID AND FloorID = P_FloorID AND PropertyID = P_PropertyID AND SuiteStatusID = 3 AND IsActive = 1 AND IsVacant = 0;

        SELECT SuiteID INTO P_NewSuiteID FROM `Suite` WHERE `SuiteNumber` = P_SuiteID;
        */

        /* can be removed from here 
        CALL CRE_Size_Save(3, P_NewSuiteID, NULL, 11);

        SET @SuiteAvailSpace = NULL;
        SELECT AvailSF.SizeValue INTO @SuiteAvailSpace FROM `Size` AvailSF 
        WHERE AvailSF.ParentID = P_NewSuiteID
                    AND AvailSF.ParentTableID = 3
                    AND AvailSF.SizeTypeID = 19;
        */
   END IF;

    -- Return LeaseId if created
    -- SELECT LeaseID AS LeaseID FROM Lease WHERE SuiteId = P_NewSuiteID AND IsActive = 1;
END
