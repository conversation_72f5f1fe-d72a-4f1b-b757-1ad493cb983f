CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `Bronze_To_Orphan`(IN P_BatchIDs VARCHAR(10000), IN tbl_name VARCHAR(40))
BEGIN
    -- Drop and create temporary tables for batch IDs and raw IDs
    DROP TEMPORARY TABLE IF EXISTS RawIDs_T;
    CREATE TEMPORARY TABLE RawIDs_T (RawIDs TEXT);
    INSERT INTO RawIDs_T VALUES (P_BatchIDs);

    DROP TEMPORARY TABLE IF EXISTS BatchIDs_T;
    CREATE TEMPORARY TABLE BatchIDs_T (Var INT PRIMARY KEY);
    
    -- If P_BatchIDs is not null, insert distinct batch IDs into BatchIDs_T
    IF P_BatchIDs IS NOT NULL THEN
        SET @sql = CONCAT("INSERT INTO BatchIDs_T (Var) SELECT DISTINCT CAST(RawIDs AS UNSIGNED) FROM RawIDs_T;");
        PREPARE stmt1 FROM @sql;
        EXECUTE stmt1;
        DEALLOCATE PREPARE stmt1;
    END IF;

    -- Drop temporary table if it exists
    DROP TEMPORARY TABLE IF EXISTS temp1;

    -- Create dynamic SQL statement for creating temp1
    SET @sql = CONCAT('
        CREATE TEMPORARY TABLE temp1 AS
        SELECT b.Tenant_Stage_Id AS Tenant_Stage_Id ,a.VendorID
        FROM ', tbl_name, ' AS a 
        INNER JOIN Empirical_DataStage.Tenants_Stage AS b ON a.VendorID = b.VendorID 
        WHERE a.BatchID IN (SELECT Var FROM BatchIDs_T)
    ');

    -- Prepare and execute the dynamic SQL statement
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    -- Update Tenants_Stage table
    UPDATE Tenants_Stage 
    SET PropertyID = NULL 
    WHERE BranchID IS NULL 
    AND Tenant_Stage_Id IN (SELECT Tenant_Stage_Id FROM temp1);
    
    update Empirical_DataStage.Orphan_Tenants_2024_04_23 a inner join temp1 b on a.VendorID=b.VendorID 
    set a.IsProcessed=1,a.Tenant_Stage_ID=b.Tenant_Stage_Id;
END