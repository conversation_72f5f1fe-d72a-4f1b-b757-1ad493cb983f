 select ConfirmedTenantID,count(*),SuiteID as cnt from Empirical_Prod.SuiteTenant where isActive=1 and TenantStatusID=1  group by ConfirmedTenantID;
 
 select st.ConfirmedTenantID,ct.CompanyID,count(ct.CompanyID) as cnt from Empirical_Prod.SuiteTenant st 
 inner join Empirical_Tenants.ConfirmedTenants  ct on ct.ConfirmedTenantID=st.ConfirmedTenantID where 
 ct.ProviderID=1 and st.isActive=1 and st.TenantStatusID=1 group by ct.CompanyID;
 
 select * from Empirical_Prod.SuiteTenant where ConfirmedTenantID =19;
-- -----------------------------------------------------Existing total counts--------------------------------------------------------------------------------
-- Existing Gold and Silver in SuitTable and SuiteTenant table
/*Suite Table*/
select SuiteNumber,FloorID,PropertyID from Empirical_Prod.Suite where SuiteNumber is not null and SuiteStatusID =3 and IsActive=1 and IsVacant=0 group by SuiteNumber,FloorID,PropertyID;-- 19155;

/*SuiteTenant*/
select ConfirmedTenantID,count(*) as cnt,SuiteID from Empirical_Prod.SuiteTenant where isActive=1 and TenantStatusID=1 group by ConfirmedTenantID; -- 40 / total 353182 -> new : 353142?

/*Tenants_Stage_ParsedAddress existing*/
select count(*) from Empirical_DataStage.Tenants_Stage_ParsedAddress;-- 740404
-- ------------------------------------------------new incoming counts------------------------------------------------------------------------------------
/*Tenants_Stage_ParsedAddress for bronze*/
SELECT count(ts.Tenant_Stage_Id)
    FROM Empirical_DataStage.Bronze_Tenants_2024_04_23 rct
    INNER JOIN Empirical_DataStage.Tenants_Stage ts ON rct.VendorID = ts.VendorID; -- 2422334
    /*
	    inserts = 2422334-72636 = 2349698
    */
    
/*Tenants_Stage_ParsedAddress for gold/silver*/
SELECT count(ts.Tenant_Stage_Id)
    FROM Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 rct
    INNER JOIN Empirical_DataStage.Tenants_Stage ts ON rct.VendorID = ts.VendorID;-- 601351
    /*
	    inserts = 601351-31547 = 569804
    */
    
/*Gold/Silver suite*/
SELECT count(*)  FROM  Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 rct
    INNER JOIN Empirical_DataStage.Tenants_Stage ts ON rct.VendorID = ts.VendorID where rct.SuiteNumberDisplay is not null and rct.PropertyID is not null;
  # 298376 
  # 66546
  /*
      inserts = 298376-920 = 297456
  */
  
/*Gold/Silver Suite Tenant*/
 /*
     inserts = 298376-13 = 298363
 */

-- ------------------------------------------combination----------------------------------------------------------------------------------------------
/*Tenants_Stage_ParsedAddress for bronze*/
select count(*) from Empirical_DataStage.Tenants_Stage_ParsedAddress where Tenant_Stage_ID in (
SELECT ts.Tenant_Stage_Id
    FROM Empirical_DataStage.Bronze_Tenants_2024_04_23 rct
    INNER JOIN Empirical_DataStage.Tenants_Stage ts ON rct.VendorID = ts.VendorID
); -- 72636

/*Tenants_Stage_ParsedAddress for gold/silver*/
select count(*) from Empirical_DataStage.Tenants_Stage_ParsedAddress where Tenant_Stage_ID in (
SELECT ts.Tenant_Stage_Id
    FROM Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 rct
    INNER JOIN Empirical_DataStage.Tenants_Stage ts ON rct.VendorID = ts.VendorID
); -- 31547

/*Suite for Gold/Silver */
DROP TEMPORARY TABLE IF EXISTS tempsuite;
 CREATE TEMPORARY TABLE tempsuite AS
 (SELECT rct.SuiteNumberDisplay,rct.PropertyID,rct.FloorNumber,ts.BranchID
    FROM Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 rct
    INNER JOIN Empirical_DataStage.Tenants_Stage ts ON rct.VendorID = ts.VendorID);
    
Select count(*) From Empirical_Prod.Suite s 
left join tempsuite ts on s.SuiteNumber = ts.SuiteNumberDisplay 
left join Empirical_Prod.Floor f on f.FloorNumber=ts.FloorNumber
Where s.FloorID = f.FloorID and s.PropertyID=ts.PropertyID and s.SuiteStatusID=3 and s.IsActive=1 and s.IsVacant=0; -- 974


/*Suite Tenant for Gold/silver*/
 select count(*) from Empirical_Prod.SuiteTenant st
 left join Empirical_Prod.Suite s on s.SuiteID = st.SuiteID
 left join tempsuite ts on s.SuiteNumber = ts.SuiteNumberDisplay
 left join Empirical_Tenants.ConfirmedTenants ct on ct.CompanyID = ts.BranchID
 where st.SuiteID is not null and st.PropertyID=ts.PropertyID and st.ConfirmedTenantID=ct.ConfirmedTenantID; -- 13
 
 -- ---------------------------------------------------------------------------------------------------------------------------------------------
 



