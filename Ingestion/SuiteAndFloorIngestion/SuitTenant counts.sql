DROP TEMPORARY TABLE IF EXISTS Empirical_Prod.temp;
CREATE TEMPORARY TABLE Empirical_Prod.temp AS(   
SELECT rct.Tenant_Stage_Id, rct.FloorNumber, rct.SuiteNumber, ts.State, ts.PostalCode, ts.BranchID,rct.VendorID,
    ts.PropertyID,rct.ShopNumber, rct.UnitNumber, rct.SuiteNumberDisplay,rct.TenantName
    FROM Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 rct
    INNER JOIN Empirical_DataStage.Tenants_Stage ts ON rct.VendorID = ts.VendorID where
     ts.BranchID is not null  and rct.Tenant_Stage_Id is not null); and s.PropertyID is null and s.FloorNumber is not null and s.SuiteNumber is not null LIMIT 1; and rct.VendorID=2159155633 and Tenant_Stage_ID=12658461;
   
   select t.Tenant_Stage_Id, t.<PERSON>, t.<PERSON>, t.<PERSON>, t.<PERSON>, t.<PERSON>,t.<PERSON>,
    t.PropertyID,t.<PERSON>, t.<PERSON>, t.SuiteNumberDisplay,t.Tenant<PERSON>ame from 
    Empirical_Prod.temp t
   inner join Empirical_Prod.Suite s ON s.SuiteNumber = t.SuiteNumberDisplay
   left join Empirical_Prod.Floor f on f.FloorID=s.FloorID
   where s.PropertyID is null and f.FloorNumber is not null and s.SuiteNumber is not null ;and t.PropertyID=184312; #501883 
   
   select t.Tenant_Stage_Id, t.FloorNumber, t.SuiteNumber, t.State, t.PostalCode, t.BranchID,t.VendorID,
    t.PropertyID,t.ShopNumber, t.UnitNumber, t.SuiteNumberDisplay,t.TenantName from 
    Empirical_Prod.temp t
   inner join Empirical_Prod.Suite s ON s.SuiteNumber = t.SuiteNumberDisplay
   left join Empirical_Prod.Floor f on f.FloorID=s.FloorID
   where s.PropertyID is not null and f.FloorNumber is null and s.SuiteNumber is not null ;
   
   select t.Tenant_Stage_Id, t.FloorNumber, t.SuiteNumber, t.State, t.PostalCode, t.BranchID,t.VendorID,
    t.PropertyID,t.ShopNumber, t.UnitNumber, t.SuiteNumberDisplay,t.TenantName from 
    Empirical_Prod.temp t
   inner join Empirical_Prod.Suite s ON s.SuiteNumber = t.SuiteNumberDisplay
   left join Empirical_Prod.Floor f on f.FloorID=s.FloorID
   where s.PropertyID is not null and f.FloorNumber is not null and s.SuiteNumber is null ;