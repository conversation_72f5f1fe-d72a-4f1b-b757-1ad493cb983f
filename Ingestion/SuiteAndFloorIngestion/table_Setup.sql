ALTER TABLE `Empirical_DataStage`.`Bronze_Tenants_2024_04_23` 
ADD COLUMN `IsSuiteandFloorProcessed` INT NULL DEFAULT NULL AFTER `ZI_C_RELEASE_DATE`;

ALTER TABLE `Empirical_DataStage`.`Bronze_Tenants_2024_04_23` 
ADD COLUMN `flat_type` VARCHAR(45) NULL DEFAULT NULL AFTER `IsSuiteandFloorProcessed`,
ADD COLUMN `flat_number` VARCHAR(45) NULL DEFAULT NULL AFTER `flat_type`;


    
UPDATE Empirical_DataStage.Bronze_Tenants_2024_04_23
SET SuiteNumberDisplay = 
    CASE
        WHEN SuiteNumberDisplay REGEXP '^[0-9]+$' THEN CONCAT(' ',SuiteNumberDisplay)
        ELSE SuiteNumberDisplay
    END;

UPDATE Empirical_DataStage.Bronze_Tenants_2024_04_23
SET flat_type = SUBSTRING_INDEX(SuiteNumberDisplay, ' ', 1),
    flat_number = SUBSTRING(SuiteNumberDisplay, LENGTH(SUBSTRING_INDEX(SuiteNumberDisplay, ' ', 1)) + 2);


ALTER TABLE `Empirical_DataStage`.`Raw_Confirmed_Tenants_2024_04_23` 
ADD COLUMN `IsSuiteandFloorProcessed` INT NULL DEFAULT NULL AFTER `ZI_C_RELEASE_DATE`,
ADD COLUMN `FloorID` INT NULL DEFAULT NULL AFTER `IsSuiteandFloorProcessed`;

update Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 a inner join Empirical_Prod.Floor f on a.FloorNumber=f.FloorNumber set a.FloorID=f.FloorID where a.FloorNumber is not null;
