CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Suite_And_Floor_Import_Bronze_Refresh`(P_BatchID INT)
BEGIN
    DROP TEMPORARY TABLE IF EXISTS tempTenantStageALA;
    CREATE TEMPORARY TABLE tempTenantStageALA AS
    SELECT 
        Tenant_Stage_Id, 
        VendorID,
        PropertyID,
        TenantName,
        FloorNumber, 
        SuiteNumberDisplay,
        -- flat_type,
        -- flat_number
    FROM 
        Empirical_DataStage.Bronze_Tenants_2024_04_23 rct
    WHERE 
        (IsSuiteandFloorProcessed = 0 OR IsSuiteandFloorProcessed IS NULL) AND
        rct.BatchID = P_BatchID;

    DROP TEMPORARY TABLE IF EXISTS tempTenantStage;
    CREATE TEMPORARY TABLE tempTenantStage AS
    SELECT 
        tt.*, 
        t.Address2,
        t.State,
        t.PostalCode,
        t.BranchID 
    FROM 
        Empirical_DataStage.Tenants_Stage t 
    INNER JOIN 
        tempTenantStageALA tt ON t.Tenant_Stage_Id = tt.Tenant_Stage_Id;

    UPDATE Empirical_DataStage.Tenants_Stage_ParsedAddress AS TSPA
    INNER JOIN 
        tempTenantStage t ON TSPA.Tenant_Stage_Id = t.Tenant_Stage_ID
    SET 
        TSPA.Address = t.Address2,
        TSPA.flat_number = t.SuiteNumberDisplay,
        -- TSPA.flat_type = t.flat_type,
        TSPA.level_number = t.FloorNumber;

    DROP TEMPORARY TABLE IF EXISTS tempTenantStage2;
    CREATE TEMPORARY TABLE tempTenantStage2 AS
    SELECT 
        t.* 
    FROM 
        tempTenantStage t 
    LEFT JOIN 
        Empirical_DataStage.Tenants_Stage_ParsedAddress AS TSPA ON t.Tenant_Stage_Id = TSPA.Tenant_Stage_Id
    WHERE 
        TSPA.Tenant_Stage_ID IS NULL;

    INSERT INTO `Empirical_DataStage`.`Tenants_Stage_ParsedAddress`
                (`Tenant_Stage_ID`, `level_number`, `flat_number`, `Address`, `state`, `postcode`)
    SELECT 
        Tenant_Stage_Id, 
        FloorNumber, 
        SuiteNumberDisplay, 
        Address2, 
        State, 
        PostalCode 
    FROM 
        tempTenantStage2;

    UPDATE Empirical_DataStage.Bronze_Tenants_2024_04_23 AS a
    INNER JOIN 
        tempTenantStageALA AS b ON a.Tenant_Stage_ID = b.Tenant_Stage_ID
    SET 
        a.IsSuiteandFloorProcessed = 1 
    WHERE 
        a.BatchID = P_BatchID;
END