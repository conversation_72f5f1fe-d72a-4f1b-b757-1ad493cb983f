CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Suite_And_Floor_Import_Refresh`(P_BatchID INT, P_TableName VARCHAR(200), P_Bucket VARCHAR(200))
BEGIN
    DROP TEMPORARY TABLE IF EXISTS tempTenantStageALA;
    SET @sql = CONCAT('
        CREATE TEMPORARY TABLE tempTenantStageALA AS
        SELECT 
            Tenant_Stage_Id, 
            VendorID,
            PropertyID,
            TenantName,
            FloorNumber, 
            SuiteNumberDisplay
        FROM 
            Empirical_DataStage.', P_TableName, ' rct
        WHERE 
            rct.BatchID = ', P_BatchID
    );

    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SELECT MIN(Tenant_Stage_Id) INTO @minTenantStageID FROM tempTenantStageALA;

    WHILE @minTenantStageID IS NOT NULL DO
        SELECT 
            FloorNumber, 
            SuiteNumberDisplay, 
            PropertyID 
        INTO 
            @FloorNumber, 
            @SuiteNumberDisplay, 
            @PropID
        FROM 
            tempTenantStageALA 
        WHERE 
            Tenant_Stage_Id = @minTenantStageID;
            
		SELECT 
            State, 
            PostalCode, 
            BranchID,
            PropertyID,
            Address2
        INTO 
            @State, 
            @PostalCode, 
            @BranchID,
            @PropertyID,
            @Address
        FROM 
            Empirical_DataStage.Tenants_Stage 
        WHERE 
            Tenant_Stage_Id = @minTenantStageID;

        -- Generating flat_number and flat_type from SuiteNumberDisplay
        SET @flat_number = TRIM(SUBSTRING_INDEX(@SuiteNumberDisplay, ' ', -1));
        SET @flat_type = TRIM(SUBSTRING_INDEX(@SuiteNumberDisplay, ' ', 1));

        -- TODO: Get from Property Address Table
        IF EXISTS (SELECT 1 FROM Empirical_DataStage.Tenants_Stage_ParsedAddress WHERE Tenant_Stage_ID = @minTenantStageID) THEN
            UPDATE Empirical_DataStage.Tenants_Stage_ParsedAddress 
            SET 
                level_number = @FloorNumber, 
                flat_number = @flat_number,
                Address = @Address 
            WHERE 
                Tenant_Stage_ID = @minTenantStageID;
        ELSE
            INSERT INTO `Empirical_DataStage`.`Tenants_Stage_ParsedAddress`
                (`Tenant_Stage_ID`, `level_number`, `flat_number`, `flat_type`, `Address`, `state`, `postcode`)
            VALUES 
                (@minTenantStageID, @FloorNumber, @flat_number, @flat_type, @Address, @State, @PostalCode);
        END IF;

        IF @BranchID IS NOT NULL AND (P_Bucket = 'Gold' OR P_Bucket = 'Silver') THEN
            -- Get Active SuiteTenant Record based on CompanyID
            SELECT 
                PropertyID, 
                ConfirmedTenantID,
                SuiteID
            INTO 
                @PropertyID, 
                @ConfirmedTenantID,
                @SuiteID 
            FROM 
                Empirical_Prod.SuiteTenant 
            WHERE 
                ConfirmedTenantID IN (
                    SELECT ConfirmedTenantID 
                    FROM Empirical_Tenants.ConfirmedTenants 
                    WHERE CompanyID = @BranchID AND ProviderID = 1
                ) 
                AND isActive = 1 
                AND TenantStatusID = 1;

            -- Get Floor ID
            IF @FloorNumber is not null then
            SELECT FloorID INTO @FloorID FROM Empirical_Prod.Floor WHERE FloorNumber = @FloorNumber;

            IF @FloorID IS NULL THEN
                SELECT MAX(Sequence) INTO @maxSequence FROM Empirical_Prod.Floor;
                INSERT INTO `Empirical_Prod`.`Floor` (`FloorNumber`, `IsActive`, `Sequence`) 
                VALUES 
                    (@FloorNumber, 1, @maxSequence + 1);
                SET @FloorID = LAST_INSERT_ID();
            END IF;
            else 
            set @FloorID=null;
            end if;

            SET @L_NewSuiteID = NULL;

            SET @IN_SuiteID = NULL;
            IF @SuiteID IS NULL THEN
                SET @IN_SuiteID = -1;
            ELSE
                SET @IN_SuiteID = @SuiteID;
            END IF;

            CALL Empirical_DataStage.CRE_Suite_Save_Refresh(@IN_SuiteID, @PropertyID, @FloorID, 22, @SuiteNumberDisplay, @L_NewSuiteID);

            -- Update the suite information in Merged View(CT)
            UPDATE Empirical_Tenants.ConfirmedTenants 
            SET 
                FloorNumber = @FloorNumber, 
                Address2 = @SuiteNumberDisplay, 
                ModifiedDate = CURRENT_TIMESTAMP(), 
                ModifiedBy = 22
            WHERE 
                CompanyID = @BranchID AND ProviderID = 5;

            -- Update the SuiteID in SuiteTenant of ConfirmedTenant( Empirical CRE- Provider 1)
            UPDATE Empirical_Prod.SuiteTenant 
            SET 
                SuiteID = @L_NewSuiteID,ModifiedDate=CURRENT_TIMESTAMP(),ModifiedBy=22 
            WHERE 
                PropertyID = @PropertyID AND ConfirmedTenantID = @ConfirmedTenantID and IsActive=1 and TenantStatusID=1;
        END IF;
        SELECT MIN(Tenant_Stage_Id) INTO @minTenantStageID FROM tempTenantStageALA WHERE Tenant_Stage_Id > @minTenantStageID ORDER BY Tenant_Stage_Id;  
    END WHILE;
END
