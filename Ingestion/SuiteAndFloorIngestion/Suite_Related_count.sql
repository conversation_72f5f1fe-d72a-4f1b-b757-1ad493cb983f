drop temporary table if exists temp1;
create temporary table temp1(
select VendorID,PropertyID,FloorNumber,SuiteNumberDisplay from Empirical_DataStage.Raw_Confirmed_Tenants_2024_04_23 where IsProcessed=1
);

drop temporary table if exists temp2;
create temporary table temp2(
select PropertyID,FloorNumber,SuiteNumber from Empirical_Prod.Suite s left join Empirical_Prod.Floor f on f.FloorID=s.FloorID
);

drop temporary table if exists temp3;
create temporary table temp3(
select a.* from temp1 a inner join temp2 b on a.PropertyID=b.PropertyID 
where ifnull(a.PropertyID,0)=ifnull(b.PropertyID,0) 
and ifnull(a.FloorNumber,0)=ifnull(b.FloorNumber,0) 
and ifnull(a.SuiteNumberDisplay,0)=ifnull(b.SuiteNumber,0));# group by a.PropertyID,a.FloorNumber,a.SuiteNumberDisplay;

select * from temp3;
select count(*),'PFS' from temp3 where PropertyID is not null and FloorNumber is not null and SuiteNumberDisplay is not null;
select count(*),'PS' from temp3 where PropertyID is not null and FloorNumber is null and SuiteNumberDisplay is not null;
select count(*),'PF' from temp3 where PropertyID is not null and FloorNumber is not null and SuiteNumberDisplay is null;
select count(*),'P' from temp3 where PropertyID is not null and FloorNumber is null and SuiteNumberDisplay is null;
