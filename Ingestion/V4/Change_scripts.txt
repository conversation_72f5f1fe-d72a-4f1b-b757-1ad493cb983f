INSERT INTO Empirical_Prod.Providers (ProviderID, ProviderName, IsActive, IsTenantProvider, DisplayText) VALUES ('18', 'google_maps', '1', '1', 'GoogleMaps');

ALTER TABLE Empirical_DataStage.NSW_Raw_ZoomInfo_Tenants 
 CHANGE COLUMN StateAbbr StateAbbr VARCHAR(10) NULL DEFAULT NULL ;

ALTER TABLE Empirical_DataStage.NSW_Raw_ZoomInfo_Tenants 
 CHANGE COLUMN Country Country INT NULL DEFAULT NULL ,
 CHANGE COLUMN StateAbbr StateAbbr VARCHAR(10) NULL DEFAULT NULL ;

UPDATE NSW_Raw_ZoomInfo_Tenants as a set a.StateAbbr= (select s.StateAbbr from Empirical_Prod.State as s where s.StateName=a.State);

UPDATE NSW_Raw_ZoomInfo_Tenants as a set a.Country= (select s.CountryID from Empirical_Prod.State as s where s.StateName=a.State);

-- Overture

ALTER TABLE Empirical_DataStage.NSW_Raw_Overture_Tenants 
CHANGE COLUMN Country Country INT NULL DEFAULT NULL ,
CHANGE COLUMN StateAbbr StateAbbr VARCHAR(20) NULL DEFAULT NULL ;

UPDATE NSW_Raw_Overture_Tenants as a set a.StateAbbr= (select s.StateAbbr from Empirical_Prod.State as s where s.StateAbbr=a.State);

UPDATE NSW_Raw_Overture_Tenants as a set a.Country= (select s.CountryID from Empirical_Prod.State as s where s.StateAbbr=a.StateAbbr);

-- Illion
UPDATE Empirical_DataStage.NSW_Raw_Illion_Tenants as a set a.Country= (select s.CountryID from Empirical_Prod.State as s where s.StateAbbr=a.StateAbbr);

ALTER TABLE Empirical_DataStage.NSW_Raw_Illion_Tenants 
ADD COLUMN Tenant_Stage_ID INT NULL AFTER match_name,
ADD INDEX idx_Tenant_Stage_ID (Tenant_Stage_ID ASC) INVISIBLE,
ADD INDEX idx_Vendor_ID (VendorID ASC) VISIBLE;
;


-- Google Maps
UPDATE Empirical_DataStage.NSW_Raw_GoogleMaps_Tenants as a set a.ProviderID= 18;

Update NSW_Raw_Confirmed_Tenants_Fields_Reference set WebsiteURL=18 where WebsiteURL=17;
Update NSW_Raw_Confirmed_Tenants_Fields_Reference set OfficePhone=18 where OfficePhone=17;


ALTER TABLE Empirical_DataStage.NSW_Raw_Confirmed_Tenants 
CHANGE COLUMN City City VARCHAR(100) NULL DEFAULT NULL ;

Update Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a
inner join Empirical_DataStage.NSW_Raw_Illion_Tenants as b on a.VendorID=b.VendorID
set a.City=b.City;

UPDATE Empirical_DataStage.NSW_Raw_Confirmed_Tenants SET ProviderID = '14' WHERE (NSW_Raw_Confirmed_TenantsID is not null);

UPDATE Empirical_DataStage.Tenant_Ingestion_Data as a
SET  a.CountryCode = (select CountryID from Empirical_Prod.State where StateAbbr=a.StateAbbr limit 1), 
        a.ProviderID = '14', 
    a.NationalID = CONCAT('ABN:', a.ABN, ' ACN:',a.ACN) WHERE (a.Tenant_Ingestion_Data_ID is not null);

ALTER TABLE Empirical_DataStage.NSW_Raw_Illion_Tenants 
ADD COLUMN NationalID VARCHAR(450) NULL AFTER Tenant_Stage_ID;

UPDATE Empirical_DataStage.NSW_Raw_Illion_Tenants as a
SET a.NationalID = CONCAT('ABN:', a.ABN, ' ACN:',a.ACN) ;

ALTER TABLE Empirical_DataStage.NSW_Raw_ZoomInfo_Tenants 
ADD INDEX idx_Main_id (main_ID ASC) VISIBLE;
;
ALTER TABLE Empirical_DataStage.NSW_Raw_GoogleMaps_Tenants 
ADD INDEX idx_Main_Id (main_ID ASC) VISIBLE;
;

ALTER TABLE Empirical_DataStage.NSW_Raw_Confirmed_Tenants_Fields_Reference 
ADD INDEX idx_VendorID (VendorID ASC) VISIBLE;
;

ALTER TABLE Empirical_DataStage.NSW_Raw_Overture_Tenants 
ADD INDEX idx_Main_id (main_ID ASC) VISIBLE;
;


select * from Empirical_DataStage.Tenants_Stage where ProviderID=18;-- google
select * from Empirical_DataStage.Tenants_Stage where ProviderID=15;-- zoominfo
select * from Empirical_DataStage.Tenants_Stage where ProviderID=16;-- overture

select * from Empirical_DataStage.NSW_Raw_GoogleMaps_Tenants where VendorID in (select VendorID from Empirical_DataStage.Tenants_Stage where ProviderID=18);
select * from Empirical_DataStage.NSW_Raw_ZoomInfo_Tenants where VendorID in (select VendorID from Empirical_DataStage.Tenants_Stage where ProviderID=15);
select * from Empirical_DataStage.NSW_Raw_Overture_Tenants where VendorID in (select VendorID from Empirical_DataStage.Tenants_Stage where ProviderID=16);

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_GoogleMaps_Tenants` 
ADD COLUMN `Tenant_Stage_ID` VARCHAR(450) NULL AFTER `match_name`;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_ZoomInfo_Tenants` 
ADD COLUMN `Tenant_Stage_ID` VARCHAR(450) NULL AFTER `match_name`;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Overture_Tenants` 
ADD COLUMN `Tenant_Stage_ID` VARCHAR(450) NULL AFTER `match_name`;

update Empirical_DataStage.NSW_Raw_GoogleMaps_Tenants as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=18
set a.Tenant_Stage_ID=b.Tenant_Stage_ID;

update Empirical_DataStage.NSW_Raw_ZoomInfo_Tenants as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=15
set a.Tenant_Stage_ID=b.Tenant_Stage_ID;

update Empirical_DataStage.NSW_Raw_Overture_Tenants as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=16
set a.Tenant_Stage_ID=b.Tenant_Stage_ID;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_GoogleMaps_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) VISIBLE;
;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_ZoomInfo_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) VISIBLE;
;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Overture_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) VISIBLE;
;

select distinct BatchId,count(*) from Empirical_DataStage.Tenants_Stage group by BatchId;
select distinct BatchId,count(*) from Empirical_DataStage.NSW_Raw_Confirmed_Tenants where Tenant_Stage_ID is null group by BatchId;

CALL Empirical_DataStage.CRE_Tenant_Stage_Import_V3(9013, 1);        
CALL Empirical_DataStage.CRE_Tenant_Stage_Import_V3(9113, 1);        
CALL Empirical_DataStage.CRE_Tenant_Stage_Import_V3(9213, 1);        

update Empirical_DataStage.NSW_Raw_GoogleMaps_Tenants as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=18
set a.Tenant_Stage_ID=b.Tenant_Stage_ID where b.BatchID in (9013,9113,9213) and a.Tenant_Stage_ID is null;

update Empirical_DataStage.NSW_Raw_ZoomInfo_Tenants as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=15
set a.Tenant_Stage_ID=b.Tenant_Stage_ID where b.BatchID in (9013,9113,9213) and a.Tenant_Stage_ID is null;

update Empirical_DataStage.NSW_Raw_Overture_Tenants as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=16
set a.Tenant_Stage_ID=b.Tenant_Stage_ID where b.BatchID in (9013,9113,9213) and a.Tenant_Stage_ID is null;

update Empirical_DataStage.NSW_Raw_Illion_Tenants as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=6
set a.Tenant_Stage_ID=b.Tenant_Stage_ID where b.BatchID in (9013,9113,9213) and a.Tenant_Stage_ID is null;

select * from Empirical_DataStage.NSW_Raw_Illion_Tenants where Tenant_Stage_ID=3862409;
SET SESSION group_concat_max_len = 1000000;                
CALL Empirical_DataStage.CRE_Verified_Tenant_Import_V3(9013, 57,14,14,348);        
CALL Empirical_DataStage.CRE_Verified_Tenant_Import_V3(9113, 57,14,14,348);    
CALL Empirical_DataStage.CRE_Verified_Tenant_Import_V3(9213, 57,14,14,348);

