CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Tenant_Stage_Import_Of_Matched_DataSets_V3`(IN P_BatchIDs varchar(10000))
BEGIN

	
		DROP TEMPORARY TABLE IF EXISTS RawIDs_T;
		CREATE TEMPORARY TABLE RawIDs_T( RawIDs text );
		INSERT INTO RawIDs_T values(P_BatchIDs); 

		DROP TEMPORARY TABLE IF EXISTS BatchIDs_T;
		CREATE temporary TABLE BatchIDs_T (Var INT Primary Key);
			IF P_BatchIDs IS NOT NULL THEN
				SET @sql = concat("INSERT INTO BatchIDs_T (Var) VALUES ('", replace(( select group_concat(distinct RawIDs) as data from RawIDs_T), ",", "'),('"),"');");
				PREPARE stmt1 from @sql;
				EXECUTE stmt1;
			END IF;
		-- select * from BatchIDs_T;
		drop temporary table if exists tempNSW_Raw_Confirmed_Tenants;
		create temporary table tempNSW_Raw_Confirmed_Tenants(
			select * from Empirical_DataStage.NSW_Raw_Confirmed_Tenants 
            where (IsProcessed=0 or IsProcessed is null ) 
				and Tenant_Stage_ID is  null and BatchID in (select * from BatchIDs_T)
		);
		start transaction;
		-- NSW Confirmed Tenant Ingestion -- Insert into Tenant Stage 
        
		insert into Empirical_DataStage.Tenants_Stage (
			VendorID,TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,NationalID, OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount, LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,ProviderID, Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status, GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID, HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry, DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName, GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc, PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode,IsProcessed,CreatedDate
        )        
        select VendorID,TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,ProviderID,
			Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode, 0 as IsProcessed,current_time() as CreatedDate from tempNSW_Raw_Confirmed_Tenants;

		update Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a 
		inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=14 
		set a.Tenant_Stage_ID=b.Tenant_Stage_ID, a.IsProcessed=1 ;
        
        -- Google Maps Tenant Ingestion
        
        drop temporary table if exists temp1;
		create temporary table temp1(select a.*,b.BatchID from Empirical_DataStage.NSW_Raw_GoogleMaps_Tenants as a
		inner join tempNSW_Raw_Confirmed_Tenants as b on a.main_ID=b.VendorID
		where BatchID in (select * from BatchIDs_T));

		insert into Empirical_DataStage.Tenants_Stage (
			VendorID,TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,#NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,
            ProviderID,
			Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode,IsProcessed,CreatedDate
        )        
        select VendorID,TenantName,Address1,Address2,City,State,StateAbbr,Country,PostalCode,#NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,
            ProviderID,
			Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode, 0 as IsProcessed,current_time() as CreatedDate from temp1;
            
		drop temporary table if exists temp1;
		create temporary table temp1(select VendorID,Tenant_Stage_ID from Empirical_DataStage.Tenants_Stage where ProviderID=18 and BatchID in (select * from BatchIDs_T));

		update Empirical_DataStage.NSW_Raw_GoogleMaps_Tenants as a
		inner join temp1 as b on a.VendorID=b.VendorID
		set a.Tenant_Stage_ID=b.Tenant_Stage_ID ;#where a.Tenant_Stage_ID is null;
        
        -- ZoomInfo data ingestion into Tenants_Stage table
        
        drop temporary table if exists temp1;
		create temporary table temp1(select a.*,b.BatchID from Empirical_DataStage.NSW_Raw_ZoomInfo_Tenants as a
		inner join tempNSW_Raw_Confirmed_Tenants as b on a.main_ID=b.VendorID
		where BatchID in (select * from BatchIDs_T));

		insert into Empirical_DataStage.Tenants_Stage (
			VendorID,TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,#NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,
            ProviderID,
			Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode,IsProcessed,CreatedDate
        )        
        select VendorID,TenantName,Address1,Address2,City,State,StateAbbr,Country,PostalCode,#NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,
            ProviderID,
			Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode, 0 as IsProcessed,current_time() as CreatedDate from temp1;
            
		drop temporary table if exists temp1;
		create temporary table temp1(select VendorID,Tenant_Stage_ID from Empirical_DataStage.Tenants_Stage where ProviderID=15 and BatchID in (select * from BatchIDs_T));

		update Empirical_DataStage.NSW_Raw_ZoomInfo_Tenants as a
		inner join temp1 as b on a.VendorID=b.VendorID
		set a.Tenant_Stage_ID=b.Tenant_Stage_ID;# where a.Tenant_Stage_ID is null;
        
        -- Overture data ingestion into Tenants_Stage table

		drop temporary table if exists temp1;
		create temporary table temp1(select a.*,b.BatchID from Empirical_DataStage.NSW_Raw_Overture_Tenants as a
		inner join tempNSW_Raw_Confirmed_Tenants as b on a.main_ID=b.VendorID
		where BatchID in (select * from BatchIDs_T));


		insert into Empirical_DataStage.Tenants_Stage (
			VendorID,TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,#NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,
            ProviderID,
			Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode,IsProcessed,CreatedDate
        )        
        select VendorID,TenantName,Address1,Address2,City,State,StateAbbr,Country,PostalCode,#NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,
            ProviderID,
			Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode, 0 as IsProcessed,current_time() as CreatedDate from temp1;
            
        drop temporary table if exists temp1;
		create temporary table temp1(select VendorID,Tenant_Stage_ID from Empirical_DataStage.Tenants_Stage 
			where ProviderID=16 and BatchID in (select * from BatchIDs_T));

        update Empirical_DataStage.NSW_Raw_Overture_Tenants as a
		inner join temp1 as b on a.VendorID=b.VendorID
		set a.Tenant_Stage_ID=b.Tenant_Stage_ID;# where a.Tenant_Stage_ID is null;
        commit;

		-- CompanyWebsites Maps Tenant Ingestion
        
        drop temporary table if exists tempCompanyWebsitesTenants;
		create temporary table tempCompanyWebsitesTenants(select svwrct.*,svrct.BatchID from Empirical_DataStage.NSW_Raw_CompanyWebsites_Tenants as svwrct
		inner join tempNSW_Raw_Confirmed_Tenants as svrct on svwrct.main_ID=svrct.VendorID
		where BatchID in (select * from BatchIDs_T));

		insert into Empirical_DataStage.Tenants_Stage (
			VendorID,TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,#NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,
            ProviderID,
			Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode,IsProcessed,CreatedDate
        )        
        select VendorID,TenantName,Address1,Address2,City,State,StateAbbr,Country,PostalCode,#NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,
            ProviderID,
			Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode, 0 as IsProcessed,current_time() as CreatedDate from tempCompanyWebsitesTenants;
            
		drop temporary table if exists tempTS;
		create temporary table tempTS(select VendorID,Tenant_Stage_ID from Empirical_DataStage.Tenants_Stage where ProviderID=19 and BatchID in (select * from BatchIDs_T));

		update Empirical_DataStage.NSW_Raw_CompanyWebsites_Tenants as svrct
		inner join tempTS as ts on svrct.VendorID=ts.VendorID
		set svrct.Tenant_Stage_ID=ts.Tenant_Stage_ID ;#where svrct.Tenant_Stage_ID is null;
END