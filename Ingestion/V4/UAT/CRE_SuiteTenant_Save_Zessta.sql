CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_SuiteTenant_Save_Zessta`(
IN P_ConfirmedTenantID int(11),
IN P_PropertyID int(11),
IN P_LoginEntityID int(11),
IN P_SuiteID int(11),
IN P_ExtVendorID varchar(45),
IN P_Tenant_Stage_ID INT
)
BEGIN
-- select "CRE_SuiteTenant_Save_Zessta";
	-- Check whether the tenant is already in the building space or in building 
    -- if the Suite ID is not available skip it, and just check if the tenant is in the building. Below logic will be work only when Tenant is not in the building/Space.
	if NOT EXISTS ( 
		Select 1 
		from SuiteTenant ST  
		LEFT JOIN  Suite S ON S.SuiteID = ST.SuiteID  
		LEFT JOIN Suite NS ON NS.SuiteID=P_SuiteID 
		WHERE 
			ST.ConfirmedTenantID=P_ConfirmedTenantID 
			and ST.IsActive=1 
			and ST.PropertyID = P_PropertyID 
			AND 1= CASE WHEN P_SuiteID IS NULL THEN 1 When P_SuiteID IS NOT NULL 
			AND S.FloorID=NS.FloorID and S.SuiteNumber=NS.SuiteNumber THEN 1 ELSE 0 END 
            AND ST.TenantStatusID=1) 
		and P_PropertyID IS NOT NULL THEN

		-- Find if Move In Tenant is already Active in any other building. 
        -- if found, Mark it as "Moved Out" if its tenant status is "Moved In" So that the tenant will display in Tenant Tab in the Portal as "Moved Out"
        -- if it is already in "Moved Out" Status, mark it as InActive and remove from the property and this tenant will no longer show in the Portal  
		Update SuiteTenant 
        Set TenantStatusID=CASE 
						WHEN TenantStatusID=1 THEN 2 ELSE TenantStatusID END
		, IsActive=CASE 
						WHEN TenantStatusID=1 THEN 1 
                        WHEN TenantStatusID=2 THEN 0                        
					  END
		,ModifiedBy=P_LoginEntityID,ModifiedDate=current_timestamp()
		Where 1= CASE   -- WHEN P_SuiteID IS NULL AND SuiteID=P_SuiteID THEN 1 
						WHEN ConfirmedTenantID=P_ConfirmedTenantID THEN 1 ELSE 0 END and IsActive=1 ; -- Currently IN
		
		-- Find if any tenants in the current space to which Move In Tenant is moving.
        -- If found, Mark the tenant as "Moved Out".
        -- Developer NOTE: Update only works when SuiteID is available and it has both Floor and Suite Number fields populated.
		 UPDATE SuiteTenant ST
		 INNER JOIN Suite S ON ST.SuiteID = S.SuiteID 
         INNER JOIN Suite NS ON NS.SuiteID = P_SuiteID       
         SET ST.IsActive=1,ST.TenantStatusID=2,  ST.ModifiedBy=P_LoginEntityID,ST.ModifiedDate=current_timestamp()            
		 Where 1= CASE WHEN P_SuiteID IS NOT NULL AND S.FloorID=NS.FloorID and S.SuiteNumber=NS.SuiteNumber THEN 1 
					  ELSE 0 END and ST.IsActive=1 ;
                      
        -- Add the Move in Tenant to the building/Space
 		INSERT INTO SuiteTenant( ConfirmedTenantID,PropertyID,CreatedBy,ModifiedBy,ModifiedDate,IsActive, TenantStatusID,SuiteID)
		VALUES(P_ConfirmedTenantID,P_PropertyID,P_LoginEntityID,P_LoginEntityID,current_timestamp(),1, 1,P_SuiteID);	   
		
        
        -- Update all likned Provider Tenant Stage records with new Property ID 
		Update Empirical_DataStage.Tenants_Stage TS 
        JOIN  Empirical_Tenants.ConfirmedTenants CT ON CT.CompanyID=TS.BranchID 
        Set TS.PropertyID=P_PropertyID,TS.ModifiedDate=current_timestamp(),TS.ModifiedBy=P_LoginEntityID 
        Where CT.ConfirmedTenantID=P_ConfirmedTenantID;
        
        -- Create Merged Tenant Record when link tenant to property for the first time
        SET @CompanyID=0;
        
        -- Get the Company ID of the tenant and see if merged tenant record already exists. if not, create one on the fly with empirical data
        Select CompanyID INTO @CompanyID from Empirical_Tenants.ConfirmedTenants CT Where ConfirmedTenantID=P_ConfirmedTenantID;
        
        IF NOT EXISTS (SELECT 1 FROM Empirical_Tenants.ConfirmedTenants Where CompanyID=@CompanyID and ProviderID=5) THEN
			INSERT INTO Empirical_Tenants.ConfirmedTenants
            (
				   TenantName,
				   CompanyName,
				   AlternateCompanyName,
				   Address1,
				   Address2,
				   AddressStreetNumber,
				   AddressStreetName,
				   CityID,
				   StateID,
				   ZipCode,
				   CountyID,
				   CountryID,
				   AddressText,
				   ExtVendorID,
				   ProviderID,
				   CreatedBy,
				   CreatedDate,
				   ModifiedBy,
				   ModifiedDate,
				   IsActive,
				   MetroID,
				   OfficePhone,
				   CompanyID,
				   SaleComp_Stage_ID,
				   Fax,
				   CEOName,
				   CEOTitle,
				   LineOfBusiness,
				   SICCode,
				   Revenue,
				   EmployeesAtLocation,
				   EmployeeCount,
				   LegalStatus,
				   StatusCode,
				   SubsidiaryCode,
				   NAICSCode,
				   NACECode,
				   NationalID,
				   Email,
				   WebsiteURL,
                   FloorNumber,
				   RevenueIndicator,
                   RegistrationOrIncorporationDate,
                   ANZSICCode
            )
            SELECT 
				   TenantName,
				   CompanyName,
				   AlternateCompanyName,
				   Address1,
				   Address2,
				   AddressStreetNumber,
				   AddressStreetName,
				   CityID,
				   StateID,
				   ZipCode,
				   CountyID,
				   CountryID,
				   AddressText,
				   ExtVendorID,
				   5,
				   P_LoginEntityID,
				   current_timestamp(),
				   P_LoginEntityID,
				   current_timestamp(),
				   IsActive,
				   MetroID,
				   OfficePhone,
				   CompanyID,
				   SaleComp_Stage_ID,
				   Fax,
				   CEOName,
				   CEOTitle,
				   LineOfBusiness,
				   SICCode,
				   Revenue,
				   EmployeesAtLocation,
				   EmployeeCount,
				   LegalStatus,
				   StatusCode,
				   SubsidiaryCode,
				   NAICSCode,
				   NACECode,
				   NationalID,
				   Email,
				   WebsiteURL,
                   FloorNumber,
                   RevenueIndicator,
                   RegistrationOrIncorporationDate,
                   ANZSICCode
            From 
            Empirical_Tenants.ConfirmedTenants
            Where ConfirmedTenantID=P_ConfirmedTenantID and ProviderID=1;
        END IF;
        
	-- Copy New and Old valuse before updating the table	
		DROP TEMPORARY TABLE IF EXISTS Address_T ;	
		CREATE TEMPORARY TABLE Address_T AS
        (
		SELECT
			CASE WHEN A.Address1 IS NULL THEN A.AddressText ELSE A.Address1 END PTAddress1,
			A.Address2 as PTAddress2,
			A.AddressStreetNumber as PTStreetNumber,
			A.AddressStreetName as PTStreetName,
			A.CityID as PTCityID,
			PTC.CityName as PTCityName,
			A.StateID as PTStateID,
			PTS.StateName as PTStateName,
			A.ZipCode as PTZipCode,
			A.CountyID as PTCountyID,
			PTCTY.CountyName as PTCountyName,
			A.CountryID as PTCountryID,
			PTCT.CountryName as PTCountryName,
			A.AddressText as PTAddressText,
			
			-- Get Current Values of Merged View
			CTM.Address1 as MVAddress1,
			CTM.Address2 as MVAddress2,
			CTM.AddressStreetNumber as MVStreetNumber,
			CTM.AddressStreetName as MVStreetName,
			CTM.CityID as MVCityID,
			CTM.StateID as MVStateID,
			CTM.ZipCode as MVZipCode,
			CTM.CountyID as MVCountyID,
			CTM.CountryID as MVCountryID,
			CTM.AddressText as MVAddressText,		
		
			-- Update Empirical Branch Record
			BA.AddressID as EBAddressID,
			BA.Address1 as EBAddress1 ,
			BA.Address2 as EBAddress2,
			BA.AddressStreetNumber as EBStreetNumber,
			BA.AddressStreetName as EBStreetName ,
			BA.CityID as EBCityID,
			BAC.CityName as EBCityName,
			BA.StateID as EBStateID,
			BAS.StateName as EBStateName,
			BA.ZipCode as EBZipCode,
			BA.CountyID as EBCountyID,
			BACTY.CountyName as EBCountyName,
			BA.CountryID as EBCountryID,
			 BACT.CountryName as EBCountryName,
			BA.AddressText as EBAddressText,
			P_LoginEntityID as LoginEntityID,
			CT.CompanyID as EBCompanyID,
			CT.ConfirmedTenantID as EBTenantID,
			CTM.ConfirmedTenantID as MVTenantID
		FROM
			SuiteTenant ST
				JOIN
			PropertySummary PS ON PS.PropertyID = ST.PropertyID
				JOIN
			Address A on A.AddressID=PS.AddressID
				JOIN
			Empirical_Tenants.ConfirmedTenants CT ON CT.ConfirmedTenantID = ST.ConfirmedTenantID 
				JOIN
			Empirical_Tenants.ConfirmedTenants CTM ON CTM.CompanyID=CT.CompanyID and CTM.ProviderID=5
				JOIN
			Address BA on BA.ParentID=CT.CompanyID and BA.ParentTableID=6 and BA.IsActive=1 AND BA.Sequence=1
				JOIN
			City BAC on BAC.CityID=BA.CityID
				JOIN
			State BAS on BAS.StateID=BA.StateID
				JOIN
			Country BACT ON BACT.CountryID=BA.CountryID 
				LEFT JOIN
			County BACTY on BACTY.CountyID=BA.CountyID
				JOIN
			City PTC on PTC.CityID=A.CityID
				JOIN
			State PTS on PTS.StateID=A.StateID
				JOIN
			Country PTCT ON PTCT.CountryID=A.CountryID
				LEFT JOIN
			County PTCTY on PTCTY.CountyID=A.CountyID
			
		WHERE
			ST.PropertyID=P_PropertyID
			AND
			ST.ConfirmedTenantID=P_ConfirmedTenantID
			AND 
			ST.IsActive=1 and IFNULL(ST.TenantStatusID,1)=1
		);
        		
		-- Prepare Changelog for Branch Address Fields
		SET @EBChangeLog=null,@EBCompanyID=null,@EBTenantID=null,@MVTenantID=null; 
        
        
	
		SELECT JSON_ARRAY(
		JSON_OBJECT('Field','Address1','CurrentValue',PTAddress1,'PreviousValue',EBAddress1,'LoginEntityID',LoginEntityID,'DateTime',current_timestamp()),
		JSON_OBJECT('Field','Address2','CurrentValue',PTAddress2,'PreviousValue',EBAddress2,'LoginEntityID',LoginEntityID,'DateTime',current_timestamp()),
		JSON_OBJECT('Field','CityID','CurrentValue',PTCityName, 'PreviousValue',EBCityName,'LoginEntityID',LoginEntityID,'DateTime',current_timestamp()),
		JSON_OBJECT('Field','StateID','CurrentValue',PTStateName,'PreviousValue',EBStateName,'LoginEntityID',LoginEntityID,'DateTime',current_timestamp()),
		JSON_OBJECT('Field','CountryID','CurrentValue',PTCountryName,'PreviousValue',EBCountryName,'LoginEntityID',LoginEntityID,'DateTime',current_timestamp()),
		JSON_OBJECT('Field','CountyID','CurrentValue',PTCountyName,'PreviousValue',EBCountyName,'LoginEntityID',LoginEntityID,'DateTime',current_timestamp()),
		JSON_OBJECT('Field','ZipCode','CurrentValue',PTZipCode,'PreviousValue',EBZipCode,'LoginEntityID',LoginEntityID,'DateTime',current_timestamp())
		) INTO @EBChangeLog 
        FROM
        Address_T;
		
		Select EBCompanyID,EBTenantID,MVTenantID INTO @EBCompanyID,@EBTenantID,@MVTenantID From Address_T;
        -- Select *  From Address_T;
		-- Save change log for Branch Address
		CALL CRE_Changelog_Company_Save(@EBCompanyID,@EBChangeLog,P_LoginEntityID,1,2);
        
                SELECT 
        TenantName,Address1,Address2,City,State,Country,OfficePhone,Fax,Email,WebsiteURL,EmployeeCount,Revenue,CEOName,CEOTitle,
        LineOfBusiness,SICCode,EmployeesAtLocation,LegalStatus,SubsidiaryCode,ABN,ACN ,
        PrimarySICDivisionDesc,PrimarySIC2DigitDesc,PrimarySIC3DigitDesc,RegistrationOrIncorporationDate,RevenueIndicator,ANZSICCode,PostalCode,StateAbbr
        INTO
		@ref_TenantName,@ref_Address1,@ref_Address2,@ref_CityID,@ref_StateID,@ref_CountryID,@ref_OfficePhone,@ref_Fax,@ref_Email,@ref_WebsiteURL,
        @ref_EmployeeCount,@ref_Revenue,@ref_CEOName,@ref_CEOTitle,@ref_LineOfBusiness,@ref_SICCode,@ref_EmployeesAtLocation,@ref_LegalStatus,
        @ref_SubsidiaryCode,@ref_ABN,@ref_ACN ,@ref_PrimarySICDivisionDesc,@ref_PrimarySIC2DigitDesc,@ref_PrimarySIC3DigitDesc,
        @ref_RegistrationOrIncorporationDate,@ref_RevenueIndicator,@ref_ANZSICCode,@ref_PostalCode,@ref_StateAbbr
        from Empirical_DataStage.NSW_Raw_Confirmed_Tenants_Fields_Reference Where VendorID=P_ExtVendorID;
		
		
		-- Prepare changelog for Merged View 
		SET @MVChangeLog=null; 
        
     --   select @MVTenantID;
     -- SET @MVTenantID="testtest";
	-- Initialize an empty JSON array
		SET @jsonArray =  JSON_ARRAY();
		
	
		if @ref_Address1 is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_Address1,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',5));
        end if;

		/* if @ref_Address2 is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_Address2,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',6));
        end if;*/

		 if @ref_CityID is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_CityID,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',7));
        SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_CityID,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',8));
        end if;

        SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',1,'Source_Tenant_Record_ID',@EBTenantID,'FieldID',11));
        SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',1,'Source_Tenant_Record_ID',@EBTenantID,'FieldID',12));
        SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',1,'Source_Tenant_Record_ID',@EBTenantID,'FieldID',13));
        
		 if @ref_OfficePhone is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_OfficePhone,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',14));
        end if;

		 if @ref_Fax is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_Fax,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',15));
        end if;

		 if @ref_Email is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_Email,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',28));
        end if;

		 if @ref_WebsiteURL is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_WebsiteURL,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',29));
        end if;

         if @ref_FloorNumber is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_FloorNumber,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',30));
        end if;

		 if @ref_ABN is not null or  @ref_ACN is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',IFNULL( @ref_ACN,@ref_ABN) ,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',32));
        end if;

		 if @ref_ABN is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_ABN,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',44));
        end if;

		 if @ref_ACN is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_ACN,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',43));
        end if;

		 if @ref_EmployeeCount is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_EmployeeCount,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',24));
        end if;

       SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',6,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',38));
       
		
		 if @ref_Revenue is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_Revenue,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',22));
        end if;

		 if @ref_CEOName is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_CEOName,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',16));
        end if;

		 if @ref_CEOTitle is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_CEOTitle,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',17));
        end if;

		 if @ref_LineOfBusiness is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_LineOfBusiness,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',18));
        end if;

         if @ref_SICCode is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_SICCode,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',19));
        end if;

		 if @ref_EmployeesAtLocation is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_EmployeesAtLocation,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',23));
        end if;

		 if @ref_LegalStatus is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_LegalStatus,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',25));
        end if;

		 if @ref_StatusCode is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_StatusCode,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',26));
        end if;

		 if @ref_SubsidiaryCode is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_SubsidiaryCode,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',27));
        end if;

         if @ref_NAICSCode is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_NAICSCode,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',20));
        end if;

		
		 if @ref_NACECode is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_NACECode,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',21));
        end if;

		 if @ref_PrimarySICDivisionDesc is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_PrimarySICDivisionDesc,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',39));
        end if;

		 if @ref_PrimarySIC2DigitDesc is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_PrimarySIC2DigitDesc,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',40));
        end if;

		 if @ref_PrimarySIC3DigitDesc is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_PrimarySIC3DigitDesc,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',41));
        end if;

         if @ref_RegistrationOrIncorporationDate is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_RegistrationOrIncorporationDate,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',42));
        end if;

		 if @ref_RevenueIndicator is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_RevenueIndicator,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',49));
        end if;

		 if @ref_ANZSICCode is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_ANZSICCode,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',45));
        SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_ANZSICCode,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',46));
        SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_ANZSICCode,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',47));
        SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',@ref_ANZSICCode,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',48));
	      end if;
	

        

		-- Add a JSON_OBJECT dynamically to the JSON array
        -- if @ref_StateAbbr is not null then
		SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',1,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',9));
        SET @jsonArray = JSON_ARRAY_APPEND(@jsonArray,'$',JSON_OBJECT('ConfirmedTenantID',@MVTenantID,'ProviderID',1,'Source_Tenant_Record_ID',P_Tenant_Stage_ID,'FieldID',10));
        -- end if;

       -- select @jsonArray ;
		SELECT @jsonArray INTO @MVChangeLog 
        
        FROM
        Address_T;
        -- select  @MVChangeLog ;
         -- Save Change log of Merged View
		Call CRE_Property_Tenant_Matrix_Save_MergedView_Changelog_1(@MVChangeLog,P_LoginEntityID);
		-- select "ERWR";
		UPDATE 	
			SuiteTenant ST
				JOIN
			PropertySummary PS ON PS.PropertyID = ST.PropertyID
				JOIN
			Address A on A.AddressID=PS.AddressID
				JOIN
			Empirical_Tenants.ConfirmedTenants CT ON CT.ConfirmedTenantID = ST.ConfirmedTenantID 
				JOIN
			Empirical_Tenants.ConfirmedTenants CTM ON CTM.CompanyID=CT.CompanyID and CTM.ProviderID=5
				JOIN
			Address BA on BA.ParentID=CT.CompanyID and BA.ParentTableID=6 and BA.IsActive=1 AND BA.Sequence=1
		SET 
			-- Update Merged record
			CTM.Address1 = CASE WHEN A.Address1 IS NULL THEN A.AddressText ELSE A.Address1 END,
			-- CTM.Address2=CASE WHENA.Address2,
			CTM.AddressStreetNumber=A.AddressStreetNumber,
			CTM.AddressStreetName = A.AddressStreetName,
			CTM.CityID=A.CityID,
			CTM.StateID=A.StateID,
			CTM.ZipCode=A.ZipCode,
			CTM.CountyID=A.CountyID,
			CTM.CountryID=A.CountryID,
			CTM.AddressText= A.AddressText,
			
			-- Update Empirical Tenant record
			CT.Address1 = CASE WHEN A.Address1 IS NULL THEN A.AddressText ELSE A.Address1 END,
			-- CT.Address2=A.Address2,
			CT.AddressStreetNumber=A.AddressStreetNumber,
			CT.AddressStreetName = A.AddressStreetName,
			CT.CityID=A.CityID,
			CT.StateID=A.StateID,
			CT.ZipCode=A.ZipCode,
			CT.CountyID=A.CountyID,
			CT.CountryID=A.CountryID,
			CT.AddressText= A.AddressText,
			
			-- Update Empirical Branch Record
			BA.Address1 = CASE WHEN A.Address1 IS NULL THEN A.AddressText ELSE A.Address1 END,
			-- BA.Address2=A.Address2,
			BA.AddressStreetNumber=A.AddressStreetNumber,
			BA.AddressStreetName = A.AddressStreetName,
			BA.CityID=A.CityID,
			BA.StateID=A.StateID,
			BA.ZipCode=A.ZipCode,
			BA.CountyID=A.CountyID,
			BA.CountryID=A.CountryID,
			BA.AddressText= A.AddressText
			
		WHERE
			ST.PropertyID=P_PropertyID
			AND
			ST.ConfirmedTenantID=P_ConfirmedTenantID
			AND 
			ST.IsActive=1 and IFNULL(ST.TenantStatusID,1)=1;
			
			
	END IF;
    
    -- Remove/Unlink Tenant from a building. Used when we make an empirical branch/tenant orphan from Branch Edit Screen or Tenant Mapping Tool ( ERC)
	IF P_ConfirmedTenantID IS NOT NULL and P_PropertyID is null THEN
    
		-- Remove Teanant from the Property by inactivating the active link
		Update SuiteTenant Set IsActive=0, ModifiedBy=P_LoginEntityID,ModifiedDate=current_timestamp()
		Where  ConfirmedTenantID=P_ConfirmedTenantID and IsActive=1 ;
        
        -- Remove PropertyID from all linked Tenant Stage records  
		Update Empirical_DataStage.Tenants_Stage TS JOIN  Empirical_Tenants.ConfirmedTenants CT 
        ON CT.CompanyID=TS.BranchID Set TS.PropertyID=null,TS.ModifiedDate=current_timestamp(),TS.ModifiedBy=P_LoginEntityID Where CT.ConfirmedTenantID=P_ConfirmedTenantID;
        
    END IF;
       
    DROP TEMPORARY TABLE IF EXISTS Address_T ;	    
	
END