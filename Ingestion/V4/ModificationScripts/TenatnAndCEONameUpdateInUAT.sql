drop temporary table if exists temp1;
create temporary table temp1(
select distinct PropertyID from Empirical_DataStage.NSW_Raw_Confirmed_Tenants where <PERSON><PERSON><PERSON> not in (9900,9800,9700) and <PERSON>ch<PERSON> is not null
);

drop temporary table if exists temp2;
create temporary table temp2(select Tenant_Stage_ID from Empirical_DataStage.NSW_Raw_Confirmed_Tenants 
where <PERSON><PERSON> in (select * from temp1) and BatchID not in (9900,9800,9700)  and Batch<PERSON> is not null);

drop temporary table if exists temp3;
create temporary table temp3(select * from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select * from temp2) and BatchID not in (9700));

drop temporary table if exists temp3;
create temporary table temp3(select PropertyID,Tenant_Stage_Id,ConfirmedTenantID,BranchID,TenantName,VendorID 
from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select * from temp2));

drop temporary table if exists tempFormattedNames;
create temporary table tempFormattedNames(
SELECT b.<PERSON><PERSON><PERSON>,a.FormattedCEOName,b.<PERSON>,a.FormattedTenantName,b.Tenant_Stage_ID,b.NSW_Raw_Confirmed_TenantsID FROM Empirical_DataStage.NSW_Raw_Confirmed_Tenants_Name_Formatted as a
inner join Empirical_DataStage.NSW_Raw_Confirmed_Tenants as b on a.NSW_Raw_Confirmed_TenantsID=b.NSW_Raw_Confirmed_TenantsID where b.Tenant_Stage_ID is not null);

select count(*) from tempFormattedNames;
select * from temp3;

update Empirical_DataStage.Tenants_Stage as TS
inner join tempFormattedNames as tfn on tfn.Tenant_Stage_ID=TS.Tenant_Stage_ID
set TS.CEOName=tfn.FormattedCEOName,TS.TenantName=tfn.FormattedTenantName;


update Empirical_DataStage.Tenants_Stage as TS
inner join tempFormattedNames as tfn on tfn.Tenant_Stage_ID=TS.Tenant_Stage_ID
inner join Empirical_Prod.CompanyRelationship as CR on TS.BranchID=CR.ChildCompanyID
inner join Empirical_Prod.Company as CC on CC.CompanyID=CR.ChildCompanyID
set CC.AltCompanyName=tfn.FormattedTenantName;


update Empirical_DataStage.Tenants_Stage as TS
inner join tempFormattedNames as tfn on tfn.Tenant_Stage_ID=TS.Tenant_Stage_ID
inner join Empirical_Prod.CompanyRelationship as CR on TS.BranchID=CR.ChildCompanyID
inner join Empirical_Prod.Company as PC on PC.CompanyID=CR.ParentCompanyID
set PC.AltCompanyName=tfn.FormattedTenantName;

update Empirical_DataStage.Tenants_Stage as TS
inner join tempFormattedNames as tfn on tfn.Tenant_Stage_ID=TS.Tenant_Stage_ID
inner join Empirical_Prod.CompanyRelationship as CR on TS.BranchID=CR.ChildCompanyID
inner join Empirical_Prod.Company as CC on CC.CompanyID=CR.ChildCompanyID
inner join Empirical_Tenants.ConfirmedTenants as CT1 on CT1.CompanyID=CC.CompanyID and CT1.ProviderID=1
set CT1.TenantName=tfn.FormattedTenantName,
CT1.CompanyName=tfn.FormattedTenantName,CT1.CEOName=tfn.FormattedCEOName;


update Empirical_DataStage.Tenants_Stage as TS
inner join tempFormattedNames as tfn on tfn.Tenant_Stage_ID=TS.Tenant_Stage_ID
inner join Empirical_Prod.CompanyRelationship as CR on TS.BranchID=CR.ChildCompanyID
inner join Empirical_Prod.Company as CC on CC.CompanyID=CR.ChildCompanyID
inner join Empirical_Tenants.ConfirmedTenants as CTM on CTM.CompanyID=CC.CompanyID and CTM.ProviderID=5
set CTM.TenantName=tfn.FormattedTenantName,CTM.CompanyName=tfn.FormattedTenantName,CTM.CEOName=tfn.FormattedCEOName;


