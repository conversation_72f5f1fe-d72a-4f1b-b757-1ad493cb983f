drop temporary table if exists Tenant_Stage_Info;
create temporary table Tenant_Stage_Info(
select Tenant_Stage_Id,ConfirmedTenantID,BranchID,TenantName,PropertyID,VendorID from Empirical_DataStage.Tenants_Stage where IsProcessed=1 and ProviderID=14
);
drop temporary table if exists Illion_Info;
create temporary table Illion_Info(
select VendorID,BranchID from Empirical_DataStage.Tenants_Stage where IsProcessed=1 and ProviderID=6 and BranchID in (select BranchID from Tenant_Stage_Info)
);
drop temporary table if exists Overture_Info;
create temporary table Overture_Info(
select VendorID,BranchID from Empirical_DataStage.Tenants_Stage where IsProcessed=1 and ProviderID=16 and BranchID in (select BranchID from Tenant_Stage_Info)
);
drop temporary table if exists ZoomInfo_Info;
create temporary table ZoomInfo_Info(
select VendorID,BranchID from Empirical_DataStage.Tenants_Stage where IsProcessed=1 and ProviderID=15 and BranchID in (select BranchID from Tenant_Stage_Info)
);
drop temporary table if exists Google_Info;
create temporary table Google_Info(
select VendorID,BranchID from Empirical_DataStage.Tenants_Stage where IsProcessed=1 and ProviderID=18 and Branch<PERSON> in (select BranchID from Tenant_Stage_Info)
);
drop temporary table if exists Company_Info;
create temporary table Company_Info(
select * from Empirical_Prod.Company where CompanyID in (select BranchID from Tenant_Stage_Info)
);
drop temporary table if exists Branch_Address;
create temporary table Branch_Address(
select ParentID,Address1,Address2,AddressStreetNumber,AddressStreetName,CityID,StateID,ZipCode,CountyID,CountryID,AddressText from Empirical_Prod.Address where ParentID in (select BranchID from Tenant_Stage_Info) and ParentTableID=6 and Sequence=1 and IsActive=1
);
drop temporary table if exists Property_Info;
create temporary table Property_Info(
select PropertyID,PropertyName from Empirical_Prod.Property where PropertyID in (select PropertyID from Tenant_Stage_Info)
);
drop temporary table if exists Property_Address;
create temporary table Property_Address(
select ParentID,Address1,Address2,ZipCode from Empirical_Prod.Address where ParentID in (select PropertyID from Property_Info) and ParentTableID=1 and Sequence=1 and IsActive=1
);
drop temporary table if exists PropertySummary_Info;
create temporary table PropertySummary_Info(
select PropertyID,UseTypeID,UseTypeName,SpecificUseID,SpecificUseName,BuildingSizeSF from Empirical_Prod.PropertySummary where PropertyID in (select PropertyID from Property_Info)
);
drop temporary table if exists ConfirmedTenants_Info;
create temporary table ConfirmedTenants_Info(
select ProviderID,CompanyName,CompanyID,NAICSCode,SICCode,OfficePhone,MetroID,Revenue,EmployeesAtLocation,EmployeeCount,SubsidiaryCode,LineOfBusiness,CEOName,CEOTitle,Email,WebsiteUrl,LegalStatus,StatusCode,Fax,CreatedDate,CreatedBy,ModifiedDate,ModifiedBy from Empirical_Tenants.ConfirmedTenants where CompanyID in (select BranchID from Tenant_Stage_Info) and ProviderID=5
);
drop temporary table if exists City_Info;
create temporary table City_Info(
select * from Empirical_Prod.City where CityID in (select CityID from Branch_Address)
);
drop temporary table if exists State_Info;
create temporary table State_Info(
select * from Empirical_Prod.State where StateID in (select StateID from Branch_Address)
);
drop temporary table if exists County_Info;
create temporary table County_Info(
select * from Empirical_Prod.County where CountyID in (select CountyID from Branch_Address)
);
drop temporary table if exists Metro_Info;
create temporary table Metro_Info(
select * from Empirical_Prod.Metro where MetroID in (select MetroID from ConfirmedTenants_Info)
);

drop temporary table if exists Ingested_Info;
create temporary table Ingested_Info(
select
    TS.Tenant_Stage_Id as Tenant_Stage_Id, TS.ConfirmedTenantID as Confirmed_Tenant_ID, TS.BranchID as BranchID, TS.TenantName as TenantName, C.CompanyName as CompanyName, C.AltCompanyName as AlternateCompanyName, BA.Address1 as Address1, BA.Address2 as Address2, BA.AddressStreetNumber as AddressStreetNumber, BA.AddressStreetName as AddressStreetName, BA.CityID as CityID, CN.CityName as CityName, BA.StateID as StateID, SN.StateAbbr as StateAbbr, BA.ZipCode as ZipCode, BA.CountyID as CountyID, CTN.CountyName as CountyName, BA.CountryID as CountryID, BA.AddressText as AddressText, CI.ProviderID as ProviderID, 'Tenant Merged View' as ProviderName, CI.CompanyName as LinkedCompanyName, CI.CompanyID as LinkedBranchID, CI.NAICSCode as NAICSCode, CI.SICCode as ISIC, CI.OfficePhone as OfficePhone, CI.MetroID as MetroID, MN.MetroName as MetroName, PI.PropertyID as PropertyID, PA.Address1 as Property_Address1, PA.Address2 as Property_Address2, PA.ZipCode as Property_ZipCode, PSI.UseTypeID as UseTypeID, PSI.UseTypeName as PropertyType, PSI.SpecificUseID as SpecificUseID, PSI.SpecificUseName as SpecificUseName, CI.Revenue as Revenue, CI.EmployeeCount as PeopleCount, CI.WebsiteUrl as Website, CI.LineOfBusiness as LineOfBusiness, CI.CEOName as CEOName, CI.CEOTitle as CEOTitle, CI.EmployeesAtLocation as EmployeesAtLocation, CI.LegalStatus as LegalStatus, CI.StatusCode as StatusCode, CI.SubsidiaryCode as SubsidiaryCode, CI.Fax as Fax, PSI.BuildingSizeSF as BuildingSizeSF, CI.Email as Email, C.CompanyID as UltimateCompanyID, C.CompanyName as UltimateCompanyName, C.CreatedBy as CreatedBy, C.CreatedDate as CreatedDate, C.ModifiedBy as ModifiedBy, C.ModifiedDate as ModifiedDate, C.IsHidden as IsHidden,C.HidedBy as HidedBy,C.HidedDate as HidedDate, TS.VendorID as VendorID, II.VendorID as Illion, ZI.VendorID as Zoominfo, OI.VendorID as Overture, GI.VendorID as GoogleMaps
from Tenant_Stage_Info TS
join Company_Info C on C.CompanyID=TS.BranchID
join Branch_Address BA on BA.ParentID=TS.BranchID
join Property_Info PI on PI.PropertyID=TS.PropertyID
join Property_Address PA on PA.ParentID=PI.PropertyID
join PropertySummary_Info PSI on PSI.PropertyID=PI.PropertyID
join ConfirmedTenants_Info CI on CI.CompanyID=TS.BranchID
left outer join City_Info CN on CN.CityID=BA.CityID
left outer join State_Info SN on SN.StateID=BA.StateID
left outer join Metro_Info MN on MN.MetroID=CI.MetroID
left outer join County_Info CTN on CTN.CountyID=BA.CountyID
left outer join Illion_Info II on II.BranchID=TS.BranchID
left outer join ZoomInfo_Info ZI on ZI.BranchID=TS.BranchID
left outer join Overture_Info OI on OI.BranchID=TS.BranchID
left outer join Google_Info GI on GI.BranchID=TS.BranchID);

select count(*) from Ingested_Info;


-- ingested data
select b.PropertyID as PropertyID,b.Tenant_Stage_Id as TenantID, b.TenantName as TenantName, b.Address1 as Address1, b.Address2 as Address2, b.City as CityID, b.State as StateID, b.StateAbbr as StateAbbr, b.PostalCode as ZipCode, b.CountryCode as CountryCode, b.OfficePhone as OfficePhone, b.Fax as Fax, b.CEOName as CEOName, b.CEOTitle as CEOTitle, b.LineOfBusiness as LineOfBusiness, b.SICCode as SICCode, b.Revenue as Revenue, b.EmployeesAtLocation as EmployeesAtLocation, b.EmployeeCount as EmployeeCount, b.LegalStatus as LegalStatus, b.StatusCode as StatusCode, b.SubsidiaryCode as SubsidiaryCode, b.NAICSCode as NAICSCode, b.NACECode as NACECode, b.Email as Email, b.WebsiteURL as WebsiteURL, b.NationalID as NationalID, b.ANZSICCode as ANZSICCode, b.VendorID as ExtVendorID, b.HideReasonID as HideReasonID, b.HideReasonComments as HideReasonComments, b.RegistrationOrIncorporationDate as RegistrationOrIncorporationDate, b.NationalID AS NationalID, b.RevenueIndicator as RevenueIndicator
 from Empirical_DataStage.NSW_Raw_Confirmed_Tenants a
left join Empirical_DataStage.Tenants_Stage b on a.VendorID=b.VendorID
where a.BatchID not in (9900,9800) and a.BatchID is not null;
