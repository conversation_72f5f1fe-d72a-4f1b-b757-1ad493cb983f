drop temporary table if exists TenantsMovingFromOnePropertyToOtherPropertyWithBatchID;
create temporary table TenantsMovingFromOnePropertyToOtherPropertyWithBatchID(
select a.NSW_Raw_Confirmed_TenantsID, a.Batch<PERSON> ,a.Vendor<PERSON>,b.Tenant_Stage_ID,b.<PERSON>ant<PERSON><PERSON> as TenantStageName,a.Tenant<PERSON>ame as ConfirmedTenantName,a.PropertyID as NewProperty,b.PropertyID as OldProperty
 from Empirical_DataStage.NSW_Raw_Confirmed_Tenants a inner join Empirical_DataStage.Tenants_Stage b on a.VendorID=b.VendorID
where a.PropertyID!=b.PropertyID  and b.ProviderID=6
);

create table    Empirical_DataStage.TenantsMovingFromOnePropertyToOtherPropertyWithBatchID as 
select * from TenantsMovingFromOnePropertyToOtherPropertyWithBatchID;


select Tenant_Stage_ID from Empirical_DataStage.TenantsMovingFromOnePropertyToOtherProperty ;

select * from  Empirical_DataStage.Tenants_Stage_ParsedAddress where Tenant_Stage_ID in (
select Tenant_Stage_ID from Empirical_DataStage.TenantsMovingFromOnePropertyToOtherProperty 
);

drop temporary table if exists tempforMakingAddressNULL;
create temporary table tempforMakingAddressNULL(
select distinct VendorID from Empirical_DataStage.NSW_Raw_Confirmed_Tenants 
 
where BatchID in (
8016, 8000, 8001, 9001, 9002, 9003, 9010, 9110, 9200, 79010, 79110, 79200, 9103, 9104, 9105, 9106, 9107, 9108, 9109, 9111, 9112, 9113, 9114, 9115, 9116, 9117, 9118, 9119, 9120, 9121, 9122, 79100, 79101, 79102, 79103, 79104, 79105, 79106, 79107, 79108, 79109, 79111, 79112, 79113, 79114, 79115, 79116, 9100, 9101, 9102, 9000, 9006, 92001, 92002, 92003, 79000, 79001, 79002, 79003, 79117, 79118, 79119, 79120, 79121, 79122, 79123, 79124, 79125, 79126
)
);

drop temporary table if exists tempforMakingAddressNULLTSID;
create temporary table tempforMakingAddressNULLTSID(
select Tenant_Stage_Id from Tenants_Stage where VendorID in ( select * from tempforMakingAddressNULL) and ProviderID =6
);

drop temporary table if exists tempforMakingAddressNULLTSIDIngestedOne;
create temporary table tempforMakingAddressNULLTSIDIngestedOne(
select Tenant_Stage_ID from  Empirical_DataStage.TenantsMovingFromOnePropertyToOtherProperty where Tenant_Stage_ID in (
select * from tempforMakingAddressNULLTSID 
));
-- 6108 - chnaged 

select count(*) from  Empirical_DataStage.Tenants_Stage_ParsedAddress where Tenant_Stage_ID in (
select * from tempforMakingAddressNULLTSIDIngestedOne 
);

-- 5846 for only we  have Parsed address

select * from  Empirical_DataStage.Tenants_Stage_ParsedAddress where Tenant_Stage_ID in (
select * from tempforMakingAddressNULLTSIDIngestedOne 
) and level_number is not NULL and flat_number is not NULL;


select * from  Empirical_DataStage.Tenants_Stage_ParsedAddress   where Tenant_Stage_ID in (
select * from tempforMakingAddressNULLTSIDIngestedOne 
)  and Tenant_Stage_ID=64965 ;

update  Empirical_DataStage.Tenants_Stage_ParsedAddress set flat_number=NULL , level_number=NULL   where Tenant_Stage_ID in (
select * from tempforMakingAddressNULLTSIDIngestedOne 
);


-- 64965
select PropertyID, BranchID, TenantName from Empirical_DataStage.Tenants_Stage where Tenant_Stage_Id in ( 64965);

