
ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Confirmed_Tenants` 
ADD COLUMN `Address1_Old` VARCHAR(45) NULL AFTER `Address2`,
ADD COLUMN `Address2_Old` VARCHAR(45) NULL AFTER `Address1_Old`;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Confirmed_Tenants` 
CHANGE COLUMN `Address2_Old` `Address2_Old` TEXT NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Confirmed_Tenants` 
CHANGE COLUMN `Address1_Old` `Address1_Old` TEXT NULL DEFAULT NULL ;
update  `Empirical_DataStage`.`NSW_Raw_Confirmed_Tenants` set Address1_Old=Address1,Address2_Old=Address2;
ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Confirmed_Tenants` 
CHANGE COLUMN `Address1` `Address1` TEXT NULL DEFAULT NULL ;

update  `Empirical_DataStage`.`NSW_Raw_Confirmed_Tenants` set Address1=Address2_Old,Address2=Address1_Old;


ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Illion_Tenants` 
ADD COLUMN `Address1_Old` VARCHAR(45) NULL AFTER `Address2`,
ADD COLUMN `Address2_Old` VARCHAR(45) NULL AFTER `Address1_Old`;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Overture_Tenants` 
ADD COLUMN `Address1_Old` VARCHAR(45) NULL AFTER `Address2`,
ADD COLUMN `Address2_Old` VARCHAR(45) NULL AFTER `Address1_Old`;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_ZoomInfo_Tenants` 
ADD COLUMN `Address1_Old` VARCHAR(45) NULL AFTER `Address2`,
ADD COLUMN `Address2_Old` VARCHAR(45) NULL AFTER `Address1_Old`;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_GoogleMaps_Tenants` 
ADD COLUMN `Address1_Old` VARCHAR(45) NULL AFTER `Address2`,
ADD COLUMN `Address2_Old` VARCHAR(45) NULL AFTER `Address1_Old`;


ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Illion_Tenants` 
CHANGE COLUMN `Address2_Old` `Address2_Old` TEXT NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Overture_Tenants` 
CHANGE COLUMN `Address2_Old` `Address2_Old` TEXT NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_ZoomInfo_Tenants` 
CHANGE COLUMN `Address2_Old` `Address2_Old` TEXT NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_GoogleMaps_Tenants` 
CHANGE COLUMN `Address2_Old` `Address2_Old` TEXT NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Illion_Tenants` 
CHANGE COLUMN `Address1_Old` `Address1_Old` TEXT NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Overture_Tenants` 
CHANGE COLUMN `Address1_Old` `Address1_Old` TEXT NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_ZoomInfo_Tenants` 
CHANGE COLUMN `Address1_Old` `Address1_Old` TEXT NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_GoogleMaps_Tenants` 
CHANGE COLUMN `Address1_Old` `Address1_Old` TEXT NULL DEFAULT NULL ;


update  `Empirical_DataStage`.`NSW_Raw_Illion_Tenants` set Address1_Old=Address1,Address2_Old=Address2;
update  `Empirical_DataStage`.`NSW_Raw_Overture_Tenants` set Address1_Old=Address1,Address2_Old=Address2;
update  `Empirical_DataStage`.`NSW_Raw_ZoomInfo_Tenants` set Address1_Old=Address1,Address2_Old=Address2;
update  `Empirical_DataStage`.`NSW_Raw_GoogleMaps_Tenants` set Address1_Old=Address1,Address2_Old=Address2;



ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Illion_Tenants` 
CHANGE COLUMN `Address1` `Address1` TEXT NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Overture_Tenants` 
CHANGE COLUMN `Address1` `Address1` TEXT NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_ZoomInfo_Tenants` 
CHANGE COLUMN `Address1` `Address1` TEXT NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_GoogleMaps_Tenants` 
CHANGE COLUMN `Address1` `Address1` TEXT NULL DEFAULT NULL ;

update  `Empirical_DataStage`.`NSW_Raw_Illion_Tenants` set Address1=Address2_Old,Address2=Address1_Old;
update  `Empirical_DataStage`.`NSW_Raw_Overture_Tenants` set  Address1=Address2_Old,Address2=Address1_Old;
update  `Empirical_DataStage`.`NSW_Raw_ZoomInfo_Tenants` set  Address1=Address2_Old,Address2=Address1_Old;
update  `Empirical_DataStage`.`NSW_Raw_GoogleMaps_Tenants` set  Address1=Address2_Old,Address2=Address1_Old;