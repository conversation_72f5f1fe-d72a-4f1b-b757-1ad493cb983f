drop temporary table if exists IngestedCT;
 create temporary table IngestedCT(
select ts.PropertyID, ts.TenantName, ts.BranchID, c.<PERSON>, Hided<PERSON>y, HidedDat,ve from Empirical_DataStage.Tenants_Stage as  ts

inner   join Empirical_Prod.Company  as c on  ts.BranchID=c.CompanyID

where ProviderID =14);

select * from IngestedCT where IsHidden=1;

drop temporary table if exists IngestedCT;
 create temporary table IngestedCT(
select 
ts.Tenant_Stage_Id,
ts.PropertyID, ts.TenantName, ts.BranchID, c.IsH<PERSON>den, c.HidedBy, c.HidedDate,c.HideReasonID,c.SubHideReasonID,c.HideReasonComments
 from Empirical_DataStage.Tenants_Stage as  ts
inner   join Empirical_Prod.Company  as c on  ts.BranchID=c.CompanyID

where ProviderID =14);

 select BranchID from IngestedCT where IsHidden=1 and Tenant_Stage_Id=7029625;


-- select * from Empirical_Prod.Company;
/**
UPDATE Empirical_Prod.Company 
SET 
   ModifiedBy=22,
   ModifiedDate= CURRENT_TIMESTAMP(),
    IsHidden = NULL,
    HidedBy = NULL,
    HidedDate = NULL,
    HideReasonID = NULL,
    SubHideReasonID = NULL,
    HideReasonComments = 'Updating the record via the AL tenant automation gold bucket data.'
WHERE
    CompanyID IN (select BranchID from IngestedCT where IsHidden=1 );

**/



