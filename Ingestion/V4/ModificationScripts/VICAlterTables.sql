ALTER TABLE `Empirical_DataStage`.`VIC_Raw_Confirmed_Tenants` 
ADD COLUMN `VIC_Raw_Confirmed_TenantsID` INT NOT NULL AUTO_INCREMENT AFTER `FormattedTenantName`,
ADD PRIMARY KEY (`VIC_Raw_Confirmed_TenantsID`);

ALTER TABLE `Empirical_DataStage`.`VIC_Raw_Confirmed_Tenants` 
ADD COLUMN `IsProcessed` INT NULL DEFAULT NULL AFTER `VIC_Raw_Confirmed_TenantsID`,
ADD COLUMN `Tenant_Stage_ID` INT NULL DEFAULT NULL AFTER `IsProcessed`;

ALTER TABLE `Empirical_DataStage`.`VIC_Illion_Confirmed_Tenants` 
ADD COLUMN `Tenant_Stage_ID` INT NULL DEFAULT NULL AFTER `StreetNoMax`;

ALTER TABLE `Empirical_DataStage`.`VIC_Overture_Confirmed_Tenants` 
ADD COLUMN `Tenant_Stage_ID` INT NULL DEFAULT NULL AFTER `StreetNoMax`;

ALTER TABLE `Empirical_DataStage`.`VIC_ZoomInfo_Confirmed_Tenants` 
ADD COLUMN `Tenant_Stage_ID` INT NULL DEFAULT NULL AFTER `StreetNoMax`;

ALTER TABLE `Empirical_DataStage`.`VIC_GoogleMaps_Confirmed_Tenants` 
ADD COLUMN `Tenant_Stage_ID` INT NULL DEFAULT NULL AFTER `StreetNoMax`;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Illion_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) VISIBLE;
ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Illion_Tenants` RENAME INDEX `VendorID` TO `idx_VendorID`;
ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Illion_Tenants` ALTER INDEX `idx_VendorID` VISIBLE;

ALTER TABLE `Empirical_DataStage`.`NSW_Raw_Confirmed_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) INVISIBLE,
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;
;

ALTER TABLE `Empirical_DataStage`.`VIC_Raw_Confirmed_Tenants_Fields_Reference` 
CHANGE COLUMN `VendorID` `VendorID` VARCHAR(100) NULL DEFAULT NULL ;

ALTER TABLE `Empirical_DataStage`.`VIC_Raw_Confirmed_Tenants_Fields_Reference`  
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;
 
ALTER TABLE `Empirical_DataStage`.`VIC_Raw_Confirmed_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) INVISIBLE,
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;

ALTER TABLE `Empirical_DataStage`.`VIC_Raw_Confirmed_Tenants` 
ADD COLUMN `BatchID` INT NULL DEFAULT NULL AFTER `Tenant_Stage_ID`;

ALTER TABLE `Empirical_DataStage`.`VIC_Raw_Overture_Tenants` 
CHANGE COLUMN `VendorID` `VendorID` VARCHAR(100) NULL DEFAULT NULL ;


ALTER TABLE `Empirical_DataStage`.`VIC_Raw_Illion_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) VISIBLE,
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;

ALTER TABLE `Empirical_DataStage`.`VIC_Raw_ZoomInfo_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) VISIBLE,
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE,
ADD INDEX `idx_main_ID` (`main_ID` ASC) VISIBLE;

ALTER TABLE `Empirical_DataStage`.`VIC_Raw_Overture_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) VISIBLE,
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE,
ADD INDEX `idx_main_ID` (`main_ID` ASC) VISIBLE;

ALTER TABLE `Empirical_DataStage`.`VIC_Raw_GoogleMaps_Tenants` 
ADD INDEX `idx_Tenant_Stage_ID` (`Tenant_Stage_ID` ASC) VISIBLE,
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE,
ADD INDEX `idx_main_ID` (`main_ID` ASC) VISIBLE;
