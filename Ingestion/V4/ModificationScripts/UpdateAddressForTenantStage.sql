
drop temporary table if exists temp1;
create temporary table temp1 (select * from Empirical_DataStage.Tenants_Stage  where BatchID in (9230,9231,9030,9031) or VendorID in (select VendorID from  Empirical_DataStage.NSW_Raw_Confirmed_Tenants  where BatchID in (9230,9231,9030,9031))
);


update Empirical_DataStage.Tenants_Stage as a
inner join temp1 as b on a.Tenant_Stage_ID=b.Tenant_Stage_ID
set a.Address1=b.Address2,a.Address2=b.Address1;


update Empirical_Prod.Address as a
inner join temp1 as b on b.BranchID=a.ParentID and a.ParentTableID=6
set a.Address1=b.Address2,a.Address2=b.Address1;


update Empirical_Prod.Address as a
inner join temp1 as b on b.ParentCompanyID=a.ParentID and a.ParentTableID=6
set a.Address1=b.Address2,a.Address2=b.Address1;

update Empirical_Tenants.ConfirmedTenants as a
inner join temp1 as b on b.BranchID=a.CompanyID 
set a.Address1=b.Address2,a.Address2=b.Address1;