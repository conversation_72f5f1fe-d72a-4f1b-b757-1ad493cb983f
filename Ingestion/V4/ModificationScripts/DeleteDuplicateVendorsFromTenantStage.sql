drop temporary table if exists temp1;
create temporary table temp1(
select * from Empirical_DataStage.Tenants_Stage where ProviderID=14 ) ;#and Branch<PERSON> is null and IsProcessed=0;#group by VendorID having count(*)=4;
CREATE INDEX tidx_VendorID ON temp1 (VendorID);
CREATE INDEX tidx_Tenant_Stage_Id ON temp1 (Tenant_Stage_Id);
drop temporary table if exists temp2;
create temporary table temp2(
select VendorID from temp1 group by VendorID having count(*)>1);

drop temporary table if exists temp3;
create temporary table temp3(
select * from temp1 where VendorID in (select * from temp2)
);

select VendorID,count(*) from temp3 group by VendorID;

drop temporary table if exists temp4;
create temporary table temp4(
select VendorID from temp3 group by VendorID having count(*)=2);
drop temporary table if exists temp5;
create temporary table temp5(
select VendorID from temp3 group by VendorID having count(*)=3
);
drop temporary table if exists temp6;
create temporary table temp6(
select Vendor<PERSON> from temp3 group by VendorID having count(*)=4);


select * from temp3 where Vendor<PERSON> in (select * from temp5);
select * from temp3 where Vendor<PERSON> in (select * from temp6);
select * from temp3 where VendorID in (select * from temp4);

drop temporary table if exists temp7;
create temporary table temp7(
select Tenant_Stage_Id from temp3 where VendorID in (select * from temp5 )  group by VendorID);

insert into temp7 select Tenant_Stage_Id from temp3 where VendorID in (select * from temp6)  group by VendorID;

insert into temp7 select Tenant_Stage_Id from temp3 where IsProcessed=1 and VendorID in (select * from temp4) group by VendorID;

insert into temp7 select Tenant_Stage_Id from temp3 where IsProcessed=0 and VendorID in (select * from temp4) group by VendorID having count(*)>1 order by CreatedDate desc;

drop temporary table if exists temp8;
create temporary table temp8(
select Tenant_Stage_Id from temp3);
select count(*) from temp8;
select count(*) from temp7;

drop temporary table if exists temp9;
create temporary table temp9(
select Tenant_Stage_Id from temp8 where Tenant_Stage_Id not in (select * from temp7));

delete FROM Empirical_Prod.NationalIdentifiers where Tenant_Stage_ID in (select * from temp9);

select * from Empirical_DataStage.Tenants_Stage where Tenant_Stage_Id=7098782;

delete FROM `Empirical_DataStage`.`Tenants_Stage`
WHERE Tenant_Stage_Id in (select * from temp9);
