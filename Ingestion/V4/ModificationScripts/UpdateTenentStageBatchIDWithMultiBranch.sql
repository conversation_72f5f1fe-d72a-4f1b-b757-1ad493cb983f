select Vendor<PERSON> from Empirical_DataStage.Tenants_Stage where ProviderID=6 group by VendorID ;
drop temporary table if exists temp1;
create temporary table temp1(select Vendor<PERSON> from Empirical_DataStage.Tenants_Stage where ProviderID=6 group by VendorID having count(*)>1);
drop temporary table if exists temp2;
create temporary table temp2(
select Vendor<PERSON> from Empirical_DataStage.NSW_Raw_Confirmed_Tenants where Batch<PERSON> is not null and BatchID not in (9900,9800)and  VendorID in (select * from temp1));

drop temporary table if exists temp3;
create temporary table temp3(select Tenant_Stage_ID from Empirical_DataStage.Tenants_Stage where `Vendor<PERSON>` in (select * from temp2) and ProviderID=14);
UPDATE `Empirical_DataStage`.`Tenants_Stage` SET `BatchID` = '9700' WHERE Tenant_Stage_ID in (select * from temp3);
