
select distinct BatchID,count(*) from Empirical_DataStage.NSW_Raw_Confirmed_Tenants group by BatchID;

update Empirical_DataStage.NSW_Raw_Confirmed_Tenants set BatchID=9600 where BatchID is null;

drop temporary table if exists temp1;
create temporary table temp1
(
select b.<PERSON>endor<PERSON> from Empirical_DataStage.Tenants_Stage as a
inner join Empirical_DataStage.NSW_Raw_Confirmed_Tenants  as b on a.VendorID=b.VendorID and a.ProviderID=6  group by a.VendorID having count(*)>1
);

drop temporary table if exists temp2;
create temporary table temp2(
select * from Empirical_DataStage.NSW_Raw_Confirmed_Tenants where V<PERSON><PERSON><PERSON> in (select * from temp1)
);

select distinct BatchID,count(*) from temp2 group by BatchID;

update Empirical_DataStage.NSW_Raw_Confirmed_Tenants set BatchID=9700 where Batch<PERSON> in (9600) and VendorID in (select * from temp1);

drop temporary table if exists temp3;
create temporary table temp3(
select * from Empirical_DataStage.NSW_Raw_Confirmed_Tenants where BatchID =9600);

drop temporary table if exists temp4;
create temporary table temp4(
select a.<PERSON><PERSON>or<PERSON> from temp3 as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID and b.ProviderID=6 group by b.BranchID having count(*)>1 )
;

update Empirical_DataStage.NSW_Raw_Confirmed_Tenants set BatchID=9800 where BatchID in (9600) and VendorID in (select * from temp4);
select b.* from Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID where a.BatchID =9600;# group by b.BranchID having count(*)>1;


select b.* from Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID where a.BatchID =9600 and b.BranchID is not null;


select b.* from Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID where a.BatchID =9600 and b.BranchID is null;

update Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID
set a.BatchID=9050 where a.BatchID =9600 and b.BranchID is not null;

update Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID
set a.BatchID=9192 where a.BatchID =9600 and b.BranchID is null;

select * from Empirical_DataStage.NSW_Raw_Confirmed_Tenants where BatchID =9600;