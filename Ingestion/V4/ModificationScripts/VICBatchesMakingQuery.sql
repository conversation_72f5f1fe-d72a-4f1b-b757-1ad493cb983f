drop temporary table if exists tmpVICRCT;
create temporary table tmpVICRCT(
select Vendor<PERSON> from Empirical_DataStage.VIC_Raw_Confirmed_Tenants group by VendorID having count(*)>1); 

drop temporary table if exists tmpVICRCTSingle;
create temporary table tmpVICRCTSingle(
select Vendor<PERSON> from Empirical_DataStage.VIC_Raw_Confirmed_Tenants group by VendorID having count(*)=1); 

select count(*) from Empirical_DataStage.VIC_Raw_Confirmed_Tenants where V<PERSON><PERSON><PERSON> in (select * from tmpVICRCT); -- Vendor ID to be avoided

drop temporary table if exists tempTSMultiVendorID;
create temporary table tempTSMultiVendorID(
SELECT b.VendorID from tmpVICRCTSingle as a
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID group by b.VendorID having count(*)>1);

select count(*) from Empirical_DataStage.VIC_Raw_Confirmed_Tenants where Vend<PERSON><PERSON> in (select * from tempTSMultiVendorID); -- VendorID to be avoided

drop temporary table if exists tmpVICRCTFinal;
create temporary table tmpVICRCTFinal(
select Vendor<PERSON> from tmpVICRCTSingle where <PERSON><PERSON><PERSON><PERSON> not in (select * from tempTSMultiVendorID) ); 

drop temporary table if exists tempTS;
create temporary table tempTS (select a.VendorID,a.BranchID from Empirical_DataStage.Tenants_Stage as a
inner join tmpVICRCTFinal as b on a.VendorID=b.VendorID);

drop temporary table if exists temp1;
create temporary table temp1(
select VendorID from tempTS where BranchID is not null group by BranchID having count(*)>1);

drop temporary table if exists tmpVICRCTFinal_1;
create temporary table tmpVICRCTFinal_1( select VendorID from tmpVICRCTFinal where VendorID not in (select VendorID from temp1));

drop temporary table if exists tempTS;
create temporary table tempTS (select a.VendorID,a.BranchID from Empirical_DataStage.Tenants_Stage as a
inner join tmpVICRCTFinal_1 as b on a.VendorID=b.VendorID);

drop temporary table if exists tempTS_1;
create temporary table tempTS_1 (select VendorID from tempTS);

/*drop temporary table if exists tmpVICRCTNewVendors;
create temporary table tmpVICRCTNewVendors (select a.VendorID  from tmpVICRCTFinal_1 as a where a.VendorID not in (select VendorID from tempTS_1));
*/
drop temporary table if exists tempBranchNullVendor;
create temporary table tempBranchNullVendor(
select VendorID from tempTS where BranchID is null);

drop temporary table if exists tempBranchNotNullVendor;
create temporary table tempBranchNotNullVendor(
select VendorID from tempTS where BranchID is not null);

update Empirical_DataStage.VIC_Raw_Confirmed_Tenants set BatchID=3300 where VendorID in (select * from tmpVICRCT); -- Multi VendorID in VIC_Raw_Confirmed_Tenants
update Empirical_DataStage.VIC_Raw_Confirmed_Tenants set BatchID=3700 where VendorID in (select * from tempTSMultiVendorID); -- Multi VendorID in Tenants Stage
update Empirical_DataStage.VIC_Raw_Confirmed_Tenants set BatchID=3800 where VendorID in (select VendorID from temp1); -- Multi BranchID in Tenants Stage


update Empirical_DataStage.VIC_Raw_Confirmed_Tenants set BatchID=3100 where VendorID in (select VendorID from tempBranchNullVendor); -- Null BranchID in Tenants Stage

update Empirical_DataStage.VIC_Raw_Confirmed_Tenants set BatchID=3000 where VendorID in (select VendorID from tempBranchNotNullVendor); -- Not Null BranchID in Tenants Stage

update Empirical_DataStage.VIC_Raw_Confirmed_Tenants set BatchID=3200 where BatchID is  null; -- Not Null BatchId in Tenants Stage


