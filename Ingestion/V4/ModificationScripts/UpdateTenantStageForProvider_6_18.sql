
drop temporary table if exists temp1;
create temporary table temp1(
select ConfirmedTenantID,IsProcessed,ModifiedDate,ParentCompanyID,BranchID,VendorID from Empirical_DataStage.Tenants_Stage where BatchID=9230 and ProviderID in (14));

select * from temp1;

update Empirical_DataStage.Tenants_Stage as a
inner join temp1 as b on a.VendorID=b.VendorID
set a.ConfirmedTenantID=b.ConfirmedTenantID,a.IsProcessed=b.IsProcessed,a.ModifiedDate=b.ModifiedDate,a.ParentCompanyID=b.ParentCompanyID,a.BranchID=b.BranchID
 where a.BatchID=9230 and a.ProviderID=6;
 
 update Empirical_DataStage.Tenants_Stage as a
 inner join Empirical_DataStage.NSW_Raw_GoogleMaps_Tenants as c on a.VendorID=c.VendorID
inner join temp1 as b on c.main_ID=b.VendorID
set a.ConfirmedTenantID=b.ConfirmedTenantID,a.IsProcessed=b.IsProcessed,a.ModifiedDate=b.ModifiedDate,a.ParentCompanyID=b.ParentCompanyID,a.BranchID=b.BranchID
 where a.BatchID=9230 and a.ProviderID=18;