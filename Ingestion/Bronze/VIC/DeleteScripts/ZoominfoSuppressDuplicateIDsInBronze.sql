drop temporary table if exists zoominfoDuplicatewithcompanyandpropertyID;
create temporary table zoominfoDuplicatewithcompanyandpropertyID(

SELECT *
FROM Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID
WHERE (CompanyID, PropertyID) IN (
    SELECT CompanyID, PropertyID
    FROM Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID
    WHERE ProviderID = 15
    GROUP BY CompanyID, PropertyID
    HAVING COUNT(*) > 1
) AND ProviderID = 15
-- ORDER BY ZI_C_LAST_UPDATED_DATE ASC
-- LIMIT 1
);

desc zoominfoDuplicatewithcompanyandpropertyID ;

select count(*), CompanyID,PropertyID# VendorID,  CompanyID, PropertyID,TenantName, ZI_C_LAST_UPDATED_DATE 
 from zoominfoDuplicatewithcompanyandpropertyID group by CompanyID,PropertyID;


drop temporary table if exists zoominfoDuplicatewithcompanyandpropertyIDlRAEq1;
create temporary table zoominfoDuplicatewithcompanyandpropertyIDlRAEq1(
SELECT VendorID, CompanyID, PropertyID, TenantName, ZI_C_LAST_UPDATED_DATE
FROM (
    SELECT VendorID, CompanyID, PropertyID, TenantName, ZI_C_LAST_UPDATED_DATE,
           ROW_NUMBER() OVER (PARTITION BY CompanyID, PropertyID ORDER BY ZI_C_LAST_UPDATED_DATE) AS rn
    FROM zoominfoDuplicatewithcompanyandpropertyID
) AS ranked
WHERE rn =1
);


drop temporary table if exists zoominfoDuplicatewithcompanyandpropertyIDlRAGt1;
create temporary table zoominfoDuplicatewithcompanyandpropertyIDlRAGt1(
SELECT VendorID, CompanyID, PropertyID, TenantName, ZI_C_LAST_UPDATED_DATE
FROM (
    SELECT VendorID, CompanyID, PropertyID, TenantName, ZI_C_LAST_UPDATED_DATE,
           ROW_NUMBER() OVER (PARTITION BY CompanyID, PropertyID ORDER BY ZI_C_LAST_UPDATED_DATE) AS rn
    FROM zoominfoDuplicatewithcompanyandpropertyID
) AS ranked
WHERE rn >1
);

drop temporary table if exists zoominfoDuplicatewithcompanyandpropertyIDinTSEq1;
create temporary table zoominfoDuplicatewithcompanyandpropertyIDinTSEq1(
 select ts.* from Empirical_DataStage.Tenants_Stage  as ts 
  inner join  zoominfoDuplicatewithcompanyandpropertyIDlRAEq1 as zi on zi.VendorID = ts.VendorID
);

drop temporary table if exists zoominfoDuplicatewithcompanyandpropertyIDinTSGt1;
create temporary table zoominfoDuplicatewithcompanyandpropertyIDinTSGt1(
 select ts.* from Empirical_DataStage.Tenants_Stage  as ts 
  inner join  zoominfoDuplicatewithcompanyandpropertyIDlRAGt1 as zi on zi.VendorID = ts.VendorID where ts.BranchID is null
);

select count(*)  from zoominfoDuplicatewithcompanyandpropertyIDinTSEq1;
select count(*)  from zoominfoDuplicatewithcompanyandpropertyIDinTSGt1;


-- select * from Empirical_DataStage.Tenants_Stage where VendorID=
/*
Update Empirical_DataStage.Tenants_Stage 
set IsHidden = 1, 
HidedBy = 22, 
HidedDate = current_timestamp(), 
HideReasonID = 3,
SubHideReasonID =8,
HideReasonComments = "Dupicate records From Zoom info source with the same company, property ID"
Where Tenant_Stage_ID IN (select Tenant_Stage_Id from zoominfoDuplicatewithcompanyandpropertyIDinTS);
*/

-- delete from Empirical_DataStage.Tenants_Stage Where Tenant_Stage_ID IN (select Tenant_Stage_Id from zoominfoDuplicatewithcompanyandpropertyIDinTSGt1); 