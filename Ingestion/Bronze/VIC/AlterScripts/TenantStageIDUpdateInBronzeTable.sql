DROP TABLE IF EXISTS `Empirical_DataStage`.`TenantIllionVendorBronze`;

create table Empirical_DataStage.TenantIllionVendorBronze (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=6);

update Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID as a
inner join Empirical_DataStage.TenantIllionVendor as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID where a.BatchID >=4100 and a.BatchID <4300;
