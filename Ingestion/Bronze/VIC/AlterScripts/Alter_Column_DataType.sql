ALTER TABLE Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 
CHANGE COLUMN StateAbbr StateAbbr VARCHAR(100) NULL DEFAULT NULL ;

ALTER TABLE Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 
CHANGE COLUMN NationalID NationalID VARCHAR(200) NULL DEFAULT NULL ;

ALTER TABLE Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 
CHANGE COLUMN VendorID VendorID VARCHAR(300) NULL DEFAULT NULL ;

ALTER TABLE Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID;
ALTER TABLE Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID ALTER INDEX Tenant_Stage_ID_UNIQUE VISIBLE;
ALTER TABLE Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID ALTER INDEX idx_ProviderID VISIBLE;

