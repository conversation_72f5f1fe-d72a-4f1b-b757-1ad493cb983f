select count(*) from Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID as VICbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=VICbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
    -- set VICbronze.City=c.CityName 
    where VICbronze.City is  null and VICbronze.BatchID in (21000, 22000) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    
/*
  update Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID as VICbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=VICbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
     set VICbronze.City=c.CityName 
    where VICbronze.City is  null and VICbronze.BatchID in (21000, 22000) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    */
    
     
    select VICbronze.StateAbbr, s.StateAbbr from Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID as VICbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=VICbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
      inner join Empirical_Prod.State as s on c.StateID= s.StateID
    where VICbronze.StateAbbr is  null and VICbronze.BatchID in (21000, 22000) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    
    /*
    update Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID as VICbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=VICbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
      inner join Empirical_Prod.State as s on c.StateID= s.StateID
    set VICbronze.StateAbbr=s.StateAbbr, VICbronze.State=s.StateID
    where VICbronze.StateAbbr is  null and VICbronze.BatchID in (21000, 22000) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    */
    
    
         select VICbronze.CountryCode, s.CountryID from Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID as VICbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=VICbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
      inner join Empirical_Prod.State as s on c.StateID= s.StateID
    where VICbronze.CountryCode is  null and VICbronze.BatchID in (21000, 22000) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    
    /*
    update Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID as VICbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=VICbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
      inner join Empirical_Prod.State as s on c.StateID= s.StateID
    set VICbronze.CountryCode=s.CountryID
    where VICbronze.CountryCode is  null and VICbronze.BatchID in (21000, 22000) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    */