
drop  temporary table if exists  VICduplicateVendors;
create temporary table VICduplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID  group by VendorID having count(*)>1);


update  Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID  set BatchID=49000  where Vendor<PERSON> in (
select * from VICduplicateVendors 
);

drop temporary table if exists VICuniqueVendors;
 create temporary table VICuniqueVendors(
 SELECT VendorID FROM Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID  group by VendorID having count(*)=1)
;

 update  Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID  set BatchID=49100  where Vendor<PERSON> in (
select * from VICuniqueVendors 
) ; 
 
 drop  temporary table if exists VICuniqueVendorsWithPropertyID;
  create temporary table VICuniqueVendorsWithPropertyID(
 SELECT VendorID FROM Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID  where  BatchID =49100 and PropertyID is not null)
;


 select count(*), BatchID from Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID group by BatchID;
 
 
 select count(*), State from Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID group by State;
 



 
  drop  temporary table if exists VICuniqueVendorsWithPropertyIDInExistingTS;
   create temporary table VICuniqueVendorsWithPropertyIDInExistingTS(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where VendorID in (
 select * from VICuniqueVendorsWithPropertyID)
 );
 
 select count(*), ProviderID from VICuniqueVendorsWithPropertyIDInExistingTS group by ProviderID;
  update  Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID  set BatchID=49200  where VendorID in (
select VendorID from VICuniqueVendorsWithPropertyIDInExistingTS 
);


 
  drop  temporary table if exists VICuniqueVendorsWithPropertyIDInExistingTSWIthBranchID;
   create temporary table VICuniqueVendorsWithPropertyIDInExistingTSWIthBranchID(
  select * from  Empirical_DataStage.Tenants_Stage 
  where  VendorID in (select VendorID from VICuniqueVendorsWithPropertyIDInExistingTS )
   and BranchID is not null
  );
  
  
  select * from Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID 
  where BatchID =49200
  and VendorID in (select VendorID from VICuniqueVendorsWithPropertyIDInExistingTSWIthBranchID);
  
update Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID  set BatchID=49300
  where BatchID =49200
  and VendorID in (select VendorID from VICuniqueVendorsWithPropertyIDInExistingTSWIthBranchID);