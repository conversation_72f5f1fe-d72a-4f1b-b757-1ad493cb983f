drop temporary table if exists temp1;
create temporary table temp1(
select VendorID from  Empirical_DataStage.VIC_Raw_Confirmed_Tenants #where (BatchID >= 80000 and BatchID<93000 )or(BatchID>=790000 and BatchID<=792000)
);

select count(*) from Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID 
where ProviderID=6 and VendorID in (select * from temp1) and BatchID >=21000 and BatchID <23000;

update Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID set BatchID=24000 
where ProviderID=6 and VendorID in (select * from temp1) and BatchID >=21000 and BatchID <23000;

-- select * from Empirical_DataStage.VIC_Raw_ZoomInfo_Tenants;

drop temporary table if exists temp1;
create temporary table temp1(
select VendorID from Empirical_DataStage.VIC_Raw_ZoomInfo_Tenants);

drop temporary table if exists temp2;
create temporary table temp2(
select VIC_Bronze_Tenants_With_ProviderID from Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID 
where ProviderID=15 and Vendor<PERSON> in (select * from temp1) and BatchID >=21000 and BatchID <23000);


update Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID set BatchID=24000 
where ProviderID=15 and VendorID in (select * from temp1) and VIC_Bronze_Tenants_With_ProviderID in (select * from temp2) and BatchID >=21000 and BatchID <23000;

#select * from Empirical_DataStage.VIC_Raw_Overture_Tenants;
drop temporary table if exists temp1;
create temporary table temp1(
select VendorID from Empirical_DataStage.VIC_Raw_Overture_Tenants );
-- select count(*) from Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID where ProviderID=16 and BatchID >=41000 and BatchID <43000;
update Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID set BatchID=24000 
where ProviderID=16 
	and VendorID in (select * from temp1) 
    and BatchID >=21000 and BatchID <23000;

-- select * from Empirical_DataStage.VIC_Raw_GoogleMaps_Tenants;
drop temporary table if exists temp1;
create temporary table temp1(
select distinct VendorID from Empirical_DataStage.VIC_Raw_GoogleMaps_Tenants);

drop temporary table if exists temp3;
create temporary table temp3(
select VendorID,VIC_Bronze_Tenants_With_ProviderID from Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID as a
where a.ProviderID=18 
and a.BatchID >=21000 and a.BatchID <23000
);

select a.VendorID from temp3 as a where a.VendorID in (select VendorID from temp1)    ;
 
drop temporary table if exists temp2;
create temporary table temp2(
select VIC_Bronze_Tenants_With_ProviderID from Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID 
where ProviderID=18 and VendorID in (select distinct a.VendorID from temp3 as a where a.VendorID in (select VendorID from temp1) ) and BatchID >=21000 and BatchID <23000);

update Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID set BatchID=24000 
where ProviderID=18 and VIC_Bronze_Tenants_With_ProviderID in (select * from temp2)  and BatchID >=21000 and BatchID <23000;