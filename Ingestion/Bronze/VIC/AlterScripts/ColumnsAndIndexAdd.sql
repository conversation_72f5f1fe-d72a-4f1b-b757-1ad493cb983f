ALTER TABLE `Empirical_DataStage`.`VIC_Bronze_Tenants_With_ProviderID` 
ADD COLUMN `VIC_Bronze_Tenants_With_ProviderID` BIGINT NOT NULL AUTO_INCREMENT AFTER `ZI_C_LAST_UPDATED_DATE`,
ADD COLUMN `Tenant_Stage_ID` BIGINT NULL AFTER `VIC_Bronze_Tenants_With_ProviderID`,
ADD PRIMARY KEY (`VIC_Bronze_Tenants_With_ProviderID`),
ADD UNIQUE INDEX `Tenant_Stage_ID_UNIQUE` (`Tenant_Stage_ID` ASC) VISIBLE;
;


ALTER TABLE `Empirical_DataStage`.`VIC_Bronze_Tenants_With_ProviderID` 
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;
ALTER TABLE `Empirical_DataStage`.`VIC_Bronze_Tenants_With_ProviderID` ALTER INDEX `Tenant_Stage_ID_UNIQUE` INVISIBLE;