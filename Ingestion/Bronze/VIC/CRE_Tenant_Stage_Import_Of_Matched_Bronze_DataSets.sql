CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Tenant_Stage_Import_Of_Matched_Bronze_DataSets`(IN P_BatchIDs varchar(10000))
BEGIN

	
		DROP TEMPORARY TABLE IF EXISTS RawIDs_T;
		CREATE TEMPORARY TABLE RawIDs_T( RawIDs text );
		INSERT INTO RawIDs_T values(P_BatchIDs); 

		DROP TEMPORARY TABLE IF EXISTS BatchIDs_T;
		CREATE temporary TABLE BatchIDs_T (Var INT Primary Key);
			IF P_BatchIDs IS NOT NULL THEN
				SET @sql = concat("INSERT INTO BatchIDs_T (Var) VALUES ('", replace(( select group_concat(distinct RawIDs) as data from RawIDs_T), ",", "'),('"),"');");
				PREPARE stmt1 from @sql;
				EXECUTE stmt1;
			END IF;
        
        drop temporary table if exists temp_Bronze_Tenants_With_ProviderID;
		create temporary table temp_Bronze_Tenants_With_ProviderID(
			select * from Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID
            where ProviderID in (15,16,18) and BatchID in (select * from BatchIDs_T) and (IsProcessed is null or IsProcessed =0)
		);
-- Updating tenant stage and mmarking isProcessed to 1 if exists begin
		drop temporary table if exists temp_Tenants_Stage;
		create temporary table temp_Tenants_Stage(
			select b.*,a.Tenant_Stage_ID as Processed_Tenant_Stage_ID from Empirical_DataStage.Tenants_Stage as a
			inner join temp_Bronze_Tenants_With_ProviderID  as b on a.VendorID=b.VendorID 
            where a.ProviderID in (15,16,18) and (b.IsProcessed is null or b.IsProcessed =0)
		);
		
		select count(*) into @existingCount from temp_Tenants_Stage;

		if @existingCount >0 then
			update Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID as a
			inner join temp_Tenants_Stage as b on a.VendorID=b.VendorID
			set a.Tenant_Stage_ID=b.Processed_Tenant_Stage_ID,a.IsProcessed=1 
			where a.ProviderID in (15,16,18) and a.BatchID in (select * from BatchIDs_T);
		
			drop temporary table if exists temp_Bronze_Tenants_With_ProviderID;
			create temporary table temp_Bronze_Tenants_With_ProviderID(
				select * from Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID
				where ProviderID in (15,16,18) and BatchID in (select * from BatchIDs_T) and (IsProcessed is null or IsProcessed =0)
			);
		end if;

		
		insert into Empirical_DataStage.Tenants_Stage (
			VendorID,TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,#NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,
            ProviderID,
			Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode,IsProcessed,CreatedDate
        )        
        select VendorID,TenantName,Address1,Address2,City,State,StateAbbr,Country,PostalCode,#NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,
            ProviderID,
			Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode, 0 as IsProcessed,current_time() as CreatedDate 
		from temp_Bronze_Tenants_With_ProviderID;
            
		drop temporary table if exists temp1;
		create temporary table temp1(select VendorID,Tenant_Stage_ID from Empirical_DataStage.Tenants_Stage
		 where ProviderID in (15,16,18) and BatchID in (select * from BatchIDs_T));

		update Empirical_DataStage.VIC_Bronze_Tenants_With_ProviderID as a
		inner join temp1 as b on a.VendorID=b.VendorID
		set a.Tenant_Stage_ID=b.Tenant_Stage_ID,a.IsProcessed=1 where a.ProviderID in (15,16,18) and a.BatchID in (select * from BatchIDs_T);
		#where a.Tenant_Stage_ID is null;
        
       
        commit;
END