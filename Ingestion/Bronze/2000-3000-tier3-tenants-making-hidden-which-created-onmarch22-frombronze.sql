drop temporary table if exists tier3recordstenants;
create temporary table tier3recordstenants(
select * from Empirical_DataStage.Tenants_Stage where BranchID is  null and PropertyID is not null
);

drop temporary table if exists tier3recordstenantsPropertiesUnder2000and3000;
create temporary table tier3recordstenantsPropertiesUnder2000and3000(
select tempts.*  from
tier3recordstenants  as tempts
inner join  Empirical_Prod.Address as ad on ad.ParentID=tempts.PropertyID
where ad.Sequence=1 and ad.IsActive=1 and ad.ParentTableID=1 and ad.ZipCode in (2000,3000) 
);

select count(*), ProviderID from tier3recordstenantsPropertiesUnder2000and3000 group by ProviderID;

select count(*) from tier3recordstenantsPropertiesUnder2000and3000 where CreatedDate >='2024-03-21' and CreatedDate <='2024-03-23' ;
select count(*),ProviderID from tier3recordstenantsPropertiesUnder2000and3000 where CreatedDate >='2024-03-21' and CreatedDate <='2024-03-23' group by ProviderID;

select PropertyID, TenantName from tier3recordstenantsPropertiesUnder2000and3000 where PropertyID= 169713 and Branch<PERSON> is null;




select count (*) from Empirical_DataStage.Tenants_Stage 
Where 
Tenant_Stage_ID IN (select Tenant_Stage_ID from tier3recordstenantsPropertiesUnder2000and3000)
CreatedDate >='2024-03-21' and CreatedDate <='2024-03-23';

/**
    
Update Empirical_DataStage.Tenants_Stage 
set IsHidden = 1, 
HidedBy = 22, 
HidedDate = current_timestamp(), 
HideReasonID = 3,
SubHideReasonID = NULL,
HideReasonComments = "Making 2000 and 3000 postal code tier3 tenants are hidden which are created from bronze source"
Where 
Tenant_Stage_ID IN (select Tenant_Stage_ID from tier3recordstenantsPropertiesUnder2000and3000)
CreatedDate >='2024-03-21' and CreatedDate <='2024-03-23'
;

**/


2000-3000-tier3-tenants-making-hidden-which-created-onmarch22-frombronze