
drop  temporary table if exists  QLDduplicateVendors;
create temporary table QLDduplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.QLD_Bronze_Tenants  group by VendorID having count(*)>1);


update  Empirical_DataStage.QLD_Bronze_Tenants  set BatchID=49000  where Vend<PERSON><PERSON> in (
select * from QLDduplicateVendors 
);

drop temporary table if exists QLDuniqueVendors;
 create temporary table QLDuniqueVendors(
 SELECT VendorID FROM Empirical_DataStage.QLD_Bronze_Tenants  group by VendorID having count(*)=1)
;

 update  Empirical_DataStage.QLD_Bronze_Tenants  set BatchID=49100  where VendorID in (
select * from QLDuniqueVendors 
) ; 
 
 drop  temporary table if exists QLDuniqueVendorsWithPropertyID;
  create temporary table QLDuniqueVendorsWithPropertyID(
 SELECT VendorID FROM Empirical_DataStage.QLD_Bronze_Tenants  where  BatchID =49100 and PropertyID is not null)
;

 select count(*), BatchID from Empirical_DataStage.QLD_Bronze_Tenants group by BatchID;
 
 select count(*), State from Empirical_DataStage.QLD_Bronze_Tenants group by State;
 
  drop  temporary table if exists QLDuniqueVendorsWithPropertyIDInExistingTS;
   create temporary table QLDuniqueVendorsWithPropertyIDInExistingTS(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where VendorID in (
 select * from QLDuniqueVendorsWithPropertyID)
 );
 
 select count(*), ProviderID from QLDuniqueVendorsWithPropertyIDInExistingTS group by ProviderID;
  update  Empirical_DataStage.QLD_Bronze_Tenants  set BatchID=49200  where VendorID in (
select VendorID from QLDuniqueVendorsWithPropertyIDInExistingTS 
);

  drop  temporary table if exists QLDuniqueVendorsWithPropertyIDInExistingTSWIthBranchID;
   create temporary table QLDuniqueVendorsWithPropertyIDInExistingTSWIthBranchID(
  select * from  Empirical_DataStage.Tenants_Stage 
  where  VendorID in (select VendorID from QLDuniqueVendorsWithPropertyIDInExistingTS )
   and BranchID is not null
  );
  
  
  select * from Empirical_DataStage.QLD_Bronze_Tenants 
  where BatchID =49200
  and VendorID in (select VendorID from QLDuniqueVendorsWithPropertyIDInExistingTSWIthBranchID);
  
update Empirical_DataStage.QLD_Bronze_Tenants  set BatchID=49300
  where BatchID =49200
  and VendorID in (select VendorID from QLDuniqueVendorsWithPropertyIDInExistingTSWIthBranchID);
  
select State from Empirical_DataStage.QLD_Bronze_Tenants group by State;
  
