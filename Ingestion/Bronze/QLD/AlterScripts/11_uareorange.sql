select  count(*) from Empirical_DataStage.QLD_Bronze_Tenants;
select count(*), BatchID from  Empirical_DataStage.QLD_Bronze_Tenants  group by BatchID;


 
 drop  temporary table if exists QLDuniqueVendorsWithPropertyID;
  create temporary table QLDuniqueVendorsWithPropertyID(
 SELECT VendorID FROM Empirical_DataStage.QLD_Bronze_Tenants  where  BatchID in (49100, 49200, 49300) and PropertyID is not null)
;


update  Empirical_DataStage.QLD_Bronze_Tenants  set BatchID=49100  where VendorID in (
select VendorID from QLDuniqueVendorsWithPropertyID 
);

  drop  temporary table if exists QLDuniqueVendorsWithPropertyIDInExistingTS;
   create temporary table QLDuniqueVendorsWithPropertyIDInExistingTS(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where VendorID in (
 select * from QLDuniqueVendorsWithPropertyID)
 );
 

update  Empirical_DataStage.QLD_Bronze_Tenants  set BatchID=49200  where VendorID in (
select Vendor<PERSON> from QLDuniqueVendorsWithPropertyIDInExistingTS 
);