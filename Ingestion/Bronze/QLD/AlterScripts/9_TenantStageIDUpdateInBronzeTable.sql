
DROP TABLE IF EXISTS `Empirical_DataStage`.`TenantIllionVendorBronze`;

create table Empirical_DataStage.TenantIllionVendorBronze (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=6);

update Empirical_DataStage.QLD_Bronze_Tenants as a
inner join Empirical_DataStage.TenantIllionVendorBronze as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID where a.BatchID >=49100 and a.BatchID <49300;