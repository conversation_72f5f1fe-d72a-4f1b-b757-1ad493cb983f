select count(*) from Empirical_DataStage.QLD_Bronze_Tenants as QLDbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=QLDbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
    -- set QLDbronze.City=c.CityName 
    where QLDbronze.City is  null and QLDbronze.BatchID in (49100, 49200, 49000, 49300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    
/*
  update Empirical_DataStage.QLD_Bronze_Tenants as QLDbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=QLDbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
     set QLDbronze.City=c.CityName 
    where QLDbronze.City is  null and QLDbronze.BatchID in (49100, 49200, 49000, 49300) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    */
    
         drop temporary table if exists QLDbronzeStatedata;
          create  temporary table QLDbronzeStatedata(
    select QLDbronze.QLD_Bronze_Tenants, QLDbronze.StateAbbr,s.StateID,  s.StateAbbr as PropertyStateAbbr from Empirical_DataStage.QLD_Bronze_Tenants as QLDbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=QLDbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
      inner join Empirical_Prod.State as s on c.StateID= s.StateID
    where QLDbronze.StateAbbr is  null and QLDbronze.BatchID in (49100, 49200, 49000, 49300) 
    and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1
    );
    
    select * from QLDbronzeStatedata;
    
  
    update Empirical_DataStage.QLD_Bronze_Tenants as QLDbronze
     inner join QLDbronzeStatedata on QLDbronzeStatedata.QLD_Bronze_Tenants= QLDbronze.QLD_Bronze_Tenants
    set QLDbronze.StateAbbr=QLDbronzeStatedata.PropertyStateAbbr, QLDbronze.State=QLDbronzeStatedata.StateID;
   
    
      drop temporary table if exists QLDbronzeCountrydata;
          create  temporary table QLDbronzeCountrydata(
         select QLDbronze.QLD_Bronze_Tenants,QLDbronze.CountryCode, s.CountryID as PropertyCountry from Empirical_DataStage.QLD_Bronze_Tenants as QLDbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=QLDbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
      inner join Empirical_Prod.State as s on c.StateID= s.StateID
    where QLDbronze.CountryCode is  null and QLDbronze.BatchID in (49100, 49200, 49000, 49300) and 
    ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1
    );
    
      update Empirical_DataStage.QLD_Bronze_Tenants as QLDbronze
     inner join QLDbronzeCountrydata on QLDbronzeCountrydata.QLD_Bronze_Tenants= QLDbronze.QLD_Bronze_Tenants
    set QLDbronze.CountryCode=QLDbronzeCountrydata.PropertyCountry;
    