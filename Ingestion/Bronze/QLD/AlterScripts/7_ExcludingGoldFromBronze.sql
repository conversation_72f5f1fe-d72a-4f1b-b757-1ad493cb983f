drop temporary table if exists temp1;
create temporary table temp1(
select Vendor<PERSON> from  Empirical_DataStage.QLD_Raw_Confirmed_Tenants #where (BatchID >= 80000 and BatchID<93000 )or(BatchID>=790000 and BatchID<=792000)
);

select count(*) from Empirical_DataStage.QLD_Bronze_Tenants 
where ProviderID=6 and Vendor<PERSON> in (select * from temp1) and BatchID >=49100 and BatchID <49300;

update Empirical_DataStage.QLD_Bronze_Tenants set BatchID=49400 
where ProviderID=6 and VendorID in (select * from temp1) and BatchID >=49100 and BatchID <49300;

-- select * from Empirical_DataStage.QLD_Raw_ZoomInfo_Tenants;

drop temporary table if exists temp1;
create temporary table temp1(
select VendorID from Empirical_DataStage.QLD_Raw_ZoomInfo_Tenants);

drop temporary table if exists temp2;
create temporary table temp2(
select QLD_Bronze_Tenants from Empirical_DataStage.QLD_Bronze_Tenants 
where ProviderID=15 and Vendor<PERSON> in (select * from temp1) and BatchID >=49100 and BatchID <49300);


update Empirical_DataStage.QLD_Bronze_Tenants set BatchID=49400 
where ProviderID=15 and VendorID in (select * from temp1) and QLD_Bronze_Tenants in (select * from temp2) and BatchID >=49100 and BatchID <49300;

#select * from Empirical_DataStage.QLD_Raw_Overture_Tenants;
drop temporary table if exists temp1;
create temporary table temp1(
select VendorID from Empirical_DataStage.QLD_Raw_Overture_Tenants );
-- select count(*) from Empirical_DataStage.QLD_Bronze_Tenants where ProviderID=16 and BatchID >=41000 and BatchID <43000;
update Empirical_DataStage.QLD_Bronze_Tenants set BatchID=49400 
where ProviderID=16 
	and VendorID in (select * from temp1) 
    and BatchID >=49100 and BatchID <49300;

-- select * from Empirical_DataStage.QLD_Raw_GoogleMaps_Tenants;
drop temporary table if exists temp1;
create temporary table temp1(
select distinct VendorID from Empirical_DataStage.QLD_Raw_GoogleMaps_Tenants);

drop temporary table if exists temp3;
create temporary table temp3(
select VendorID,QLD_Bronze_Tenants from Empirical_DataStage.QLD_Bronze_Tenants as a
where a.ProviderID=18 
and a.BatchID >=49100 and a.BatchID <49300
);

select a.VendorID from temp3 as a where a.VendorID in (select VendorID from temp1)    ;
 
drop temporary table if exists temp2;
create temporary table temp2(
select QLD_Bronze_Tenants from Empirical_DataStage.QLD_Bronze_Tenants 
where ProviderID=18 and VendorID in (select distinct a.VendorID from temp3 as a where a.VendorID in (select VendorID from temp1) ) and BatchID >=49100 and BatchID <49300);

update Empirical_DataStage.QLD_Bronze_Tenants set BatchID=49400 
where ProviderID=18 and QLD_Bronze_Tenants in (select * from temp2)  and BatchID >=49100 and BatchID <49300;


DROP TABLE IF EXISTS `Empirical_DataStage`.`TenantIllionVendorBronze`;

create table Empirical_DataStage.TenantIllionVendorBronze (
select Tenant_Stage_Id,VendorID,PropertyID from Empirical_DataStage.Tenants_Stage where ProviderID=6);

update Empirical_DataStage.QLD_Bronze_Tenants as a
inner join Empirical_DataStage.TenantIllionVendorBronze as b on a.VendorID=b.VendorID
set a.Tenant_Stage_ID=b.Tenant_Stage_ID where a.BatchID >=49100 and a.BatchID <49300;
