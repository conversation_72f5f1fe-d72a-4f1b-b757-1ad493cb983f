ALTER TABLE `Empirical_DataStage`.`QLD_Bronze_Tenants` 
ADD INDEX `idx_ProviderID` (`ProviderID` ASC) INVISIBLE,
ADD INDEX `idx_BatchID` (`BatchID` ASC) VISIBLE;
;


ALTER TABLE `Empirical_DataStage`.`QLD_Bronze_Tenants` 
ADD COLUMN `QLD_Bronze_Tenants` BIGINT NOT NULL AUTO_INCREMENT AFTER `ZI_C_LAST_UPDATED_DATE`,
ADD COLUMN `Tenant_Stage_ID` BIGINT NULL AFTER `QLD_Bronze_Tenants`,
ADD PRIMARY KEY (`QLD_Bronze_Tenants`),
ADD UNIQUE INDEX `Tenant_Stage_ID_UNIQUE` (`Tenant_Stage_ID` ASC) VISIBLE;
;


ALTER TABLE `Empirical_DataStage`.`QLD_Bronze_Tenants` 
CHANGE COLUMN `VendorID` `VendorID` VARCHAR(300) NULL DEFAULT NULL ;


ALTER TABLE `Empirical_DataStage`.`QLD_Bronze_Tenants` 
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;
ALTER TABLE `Empirical_DataStage`.`QLD_Bronze_Tenants` ALTER INDEX `Tenant_Stage_ID_UNIQUE` VISIBLE;