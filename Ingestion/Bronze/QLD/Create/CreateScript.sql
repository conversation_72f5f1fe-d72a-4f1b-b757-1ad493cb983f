CREATE TABLE `QLD_Bronze_Tenants` (
  `ABN` bigint DEFAULT NULL,
  `ACN` bigint DEFAULT NULL,
  `VendorID` text,
  `TenantName` text,
  `Address1` text,
  `Address2` text,
  `City` text,
  `State` double DEFAULT NULL,
  `StateAbbr` text,
  `Country` text,
  `CountryCode` double DEFAULT NULL,
  `PostalCode` text,
  `NationalID` text,
  `OfficePhone` text,
  `Fax` text,
  `CEOName` text,
  `CEOTitle` text,
  `LineOfBusiness` text,
  `SICCode` double DEFAULT NULL,
  `Revenue` double DEFAULT NULL,
  `EmployeesAtLocation` double DEFAULT NULL,
  `EmployeeCount` double DEFAULT NULL,
  `LegalStatus` text,
  `StatusCode` text,
  `SubsidiaryCode` text,
  `IsProcessed` double DEFAULT NULL,
  `ConfirmedTenantID` double DEFAULT NULL,
  `PropertyID` bigint DEFAULT NULL,
  `MatchingScore` double DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `ModifiedDate` datetime DEFAULT NULL,
  `Latitude` double DEFAULT NULL,
  `Longitude` double DEFAULT NULL,
  `ParentCompanyID` double DEFAULT NULL,
  `BranchID` double DEFAULT NULL,
  `BatchID` double DEFAULT NULL,
  `ProviderID` bigint DEFAULT NULL,
  `Provider` text,
  `IsDefault` double DEFAULT NULL,
  `NAICSCode` text,
  `NACECode` text,
  `Email` text,
  `WebsiteURL` text,
  `ModifiedBy` double DEFAULT NULL,
  `IsHidden` double DEFAULT NULL,
  `IsDeleted` double DEFAULT NULL,
  `HidedBy` double DEFAULT NULL,
  `HidedDate` double DEFAULT NULL,
  `HideReasonID` double DEFAULT NULL,
  `HideReasonComments` text,
  `ASICEntityStatus` text,
  `ASICEntityType` text,
  `ASICEntityClass` text,
  `ABNStatus` text,
  `ABN_StatusFromDate` text,
  `GST_Status` text,
  `GST_StatusFromDate` text,
  `RegistrationOrIncorporationDate` text,
  `EntityAge` double DEFAULT NULL,
  `EmployeeIndicator` text,
  `RevenueIndicator` text,
  `HQ_ID` double DEFAULT NULL,
  `HQ_CompanyName` double DEFAULT NULL,
  `NumberofMembersinHierarchy` double DEFAULT NULL,
  `ImmediateParentDUNS` double DEFAULT NULL,
  `ImmediateParentName` text,
  `ImmediateParentCountry` text,
  `DomesticParentDUNS` double DEFAULT NULL,
  `DomesticParentName` text,
  `DomesticParentCountry` text,
  `GlobalUltimateParentDUNS` double DEFAULT NULL,
  `GlobalUltimateParentName` text,
  `GlobalUltimateParentCountry` text,
  `PrimarySICDesc` text,
  `PrimarySIC3Digit` text,
  `PrimarySIC3DigitDesc` text,
  `PrimarySIC2Digit` text,
  `PrimarySIC2DigitDesc` text,
  `PrimarySICDivision` text,
  `PrimarySICDivisionDesc` text,
  `SubHideReasonID` double DEFAULT NULL,
  `ANZSICCode` text,
  `DisplayName` text,
  `CompanyID` double DEFAULT NULL,
  `MarketableFlag` bigint DEFAULT NULL,
  `TradingNames` text,
  `ZI_C_RELEASE_DATE` bigint DEFAULT NULL,
  `ZI_C_LAST_UPDATED_DATE` text,
  `CleanedAddress` text,
  `StreetNoMin` bigint DEFAULT NULL,
  `OriginalAddress` text,
  `SuiteNumber` text,
  `StreetName` text,
  `Suffix` text,
  `FloorNumber` text,
  `StreetNoMax` double DEFAULT NULL,
  `StreetNoSet` text,
  `AddressID` bigint DEFAULT NULL,
  `StreetNo` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
