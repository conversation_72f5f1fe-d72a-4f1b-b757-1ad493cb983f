drop temporary table if exists tier3recordstenants;
create temporary table tier3recordstenants(
select * from Empirical_DataStage.Tenants_Stage where BranchID is  null and PropertyID is not null
);

drop temporary table if exists tier3recordstenantsPropertiesnot2000and3000;
create temporary table tier3recordstenantsPropertiesnot2000and3000(
select tempts.*  from
tier3recordstenants  as tempts
inner join  Empirical_Prod.Address as ad on ad.ParentID=tempts.PropertyID
where ad.Sequence=1 and ad.IsActive=1 and ad.ParentTableID=1 and ad.ZipCode not in (2000,3000) 
);

select count(*), ProviderID from tier3recordstenantsPropertiesnot2000and3000 group by ProviderID;

select count(*) from tier3recordstenantsPropertiesnot2000and3000 where ModifiedDate < '2024-03-22' ;

select count(*),ProviderID from tier3recordstenantsPropertiesnot2000and3000 where ModifiedDate < '2024-03-22' group by ProviderID;



    
select count(*) Empirical_DataStage.Tenants_Stage 
Where 
Tenant_Stage_ID IN (select Tenant_Stage_ID from tier3recordstenantsPropertiesnot2000and3000);
ModifiedDate < '2024-03-22';

/**
    
Update Empirical_DataStage.Tenants_Stage 
set IsHidden = 1, 
HidedBy = 22, 
HidedDate = current_timestamp(), 
HideReasonID = 3,
SubHideReasonID = NULL,
HideReasonComments = "Making tier3 tenants are hidden which are not modified from bronze bucket and not in 2000 and 3000 postal codes"
Where 
Tenant_Stage_ID IN (select Tenant_Stage_ID from tier3recordstenantsPropertiesnot2000and3000);
ModifiedDate < '2024-03-22'

**/


-- cross checking 
select count(*) from tier3recordstenantsPropertiesnot2000and3000 where ModifiedDate < '2024-03-22' ;


select count(*) from tier3recordstenantsPropertiesnot2000and3000  as tempTier3
inner join NSW_Bronze_Tenants_With_ProviderID_V2 as nswbronze on nswbronze.VendorID = tempTier3.VendorID
where tempTier3.ModifiedDate < '2024-03-22' 
 union 
 
 select count(*) from tier3recordstenantsPropertiesnot2000and3000  as tempTier3
inner join VIC_Bronze_Tenants_With_ProviderID as bronze on bronze.VendorID = tempTier3.VendorID
where tempTier3.ModifiedDate < '2024-03-22' ;

select count(*) from tier3recordstenantsPropertiesnot2000and3000  as tempTier3
inner join QLD_Bronze_Tenants as bronze on bronze.VendorID = tempTier3.VendorID
where tempTier3.ModifiedDate < '2024-03-22' ;

