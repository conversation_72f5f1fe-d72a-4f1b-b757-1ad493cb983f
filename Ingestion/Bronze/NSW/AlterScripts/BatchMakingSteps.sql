drop  temporary table if exists  NSWduplicateVendors;
create temporary table NSWduplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2  group by VendorID having count(*)>1);


update  Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2  set BatchID=4000  where Vendor<PERSON> in (
select * from NSWduplicateVendors 
);

drop temporary table if exists NSWuniqueVendors;
 create temporary table NSWuniqueVendors(
 SELECT VendorID FROM Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2  group by VendorID having count(*)=1)
;

 update  Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2  set BatchID=4100  where VendorID in (
select * from NSWuniqueVendors 
) ; 

 drop  temporary table if exists NSWuniqueVendorsWithPropertyID;
  create temporary table NSWuniqueVendorsWithPropertyID(
 SELECT VendorID FROM Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2  where  BatchID =4100 and PropertyID is not null)
;


 select count(*), BatchID from Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 group by BatchID;








  drop  temporary table if exists NSWuniqueVendorsWithPropertyIDInExistingTS;
   create temporary table NSWuniqueVendorsWithPropertyIDInExistingTS(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where VendorID in (
 select * from NSWuniqueVendorsWithPropertyID)
 );

 select count(*), ProviderID from NSWuniqueVendorsWithPropertyIDInExistingTS group by ProviderID;
  update  Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2  set BatchID=4200  where VendorID in (
select VendorID from NSWuniqueVendorsWithPropertyIDInExistingTS 
);



  drop  temporary table if exists NSWuniqueVendorsWithPropertyIDInExistingTSWIthBranchID;
   create temporary table NSWuniqueVendorsWithPropertyIDInExistingTSWIthBranchID(
  select * from  Empirical_DataStage.Tenants_Stage 
  where  VendorID in (select VendorID from NSWuniqueVendorsWithPropertyIDInExistingTS )
   and BranchID is not null
  );


  select * from Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 
  where BatchID =4200
  and VendorID in (select VendorID from NSWuniqueVendorsWithPropertyIDInExistingTSWIthBranchID);

update Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2  set BatchID=4300
  where BatchID =4200
  and VendorID in (select VendorID from NSWuniqueVendorsWithPropertyIDInExistingTSWIthBranchID);