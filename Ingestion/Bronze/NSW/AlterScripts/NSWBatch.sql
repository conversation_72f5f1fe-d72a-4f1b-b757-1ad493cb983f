/**drop  temporary table if exists  duplicateVendors;
create temporary table duplicateVendors(
SELECT  VendorID FROM Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID  group by VendorID having count(*)>1);


select count* from duplicateVendors

update  Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID  set BatchID=4000  where Vendor<PERSON> in (
select * from duplicateVendors 
) ;

drop  temporary table if exists uniqueVendors;

 create temporary table uniqueVendors(
 SELECT VendorID FROM Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID  group by VendorID having count(*)=1)
;

 update  Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID  set BatchID=4100  where VendorID in (
select * from uniqueVendors 
) ; 
 
 drop  temporary table if exists uniqueVendorsWithPropertyID;
  create temporary table uniqueVendorsWithPropertyID(
 SELECT VendorID FROM Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID  where  BatchID =4100 and PropertyID is not null)
;


 update  Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID  set BatchID=4200  where VendorID in (
select * from uniqueVendorsWithPropertyID 
) ; 
 

 select count(*) from Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID  as bt
  inner join Empirical_DataStage.Tenants_Stage as ts  on ts.VendorID = bt.VendorID
 where  bt.BatchID = 4200;
 
  drop  temporary table if exists uniqueVendorsWithPropertyIDInExistingTS;
   create temporary table uniqueVendorsWithPropertyIDInExistingTS(
 select VendorID, ProviderID from Empirical_DataStage.Tenants_Stage where VendorID in (
 select * from uniqueVendorsWithPropertyID)
 );
 
 select count(*), ProviderID from uniqueVendorsWithPropertyIDInExistingTS group by ProviderID;
  update  Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID  set BatchID=4300  where VendorID in (
select VendorID from uniqueVendorsWithPropertyIDInExistingTS 
);


-- update  Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID  set BatchID=4300 where BatchID=4220;
 

 -- Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID
 
  drop temporary table if exists uniqueVendorsWithPropertyIDInExistingTSWIthBranchID;
   create temporary table uniqueVendorsWithPropertyIDInExistingTSWIthBranchID(
  select * from  Empirical_DataStage.Tenants_Stage 
  where  VendorID in (select VendorID from uniqueVendorsWithPropertyIDInExistingTS )
   and BranchID is not null
  );
  
  
  select * from Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID 
  where BatchID =4300
  and VendorID in (select VendorID from uniqueVendorsWithPropertyIDInExistingTSWIthBranchID);
  
update Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID  set BatchID=4400
  where BatchID =4300
  and VendorID in (select VendorID from uniqueVendorsWithPropertyIDInExistingTSWIthBranchID);
  
  
  **/
  
  select * from Empirical_DataStage.NSW_Br;
  
    select count(*), ProviderID from Empirical_DataStage.NSW_Br 
  where BatchID =4200
  group by ProviderID;
  
  
    SELECT  count(*) FROM Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID   where ProviderID =6;
  ;

  SELECT  VendorID, ProviderID FROM Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID   where PropertyID is null
  and 
  BatchID=4100
  ;

'