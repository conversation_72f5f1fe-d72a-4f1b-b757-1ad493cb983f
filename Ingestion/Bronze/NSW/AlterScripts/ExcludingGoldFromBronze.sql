drop temporary table if exists temp1;
create temporary table temp1(
select Vendor<PERSON> from  Empirical_DataStage.NSW_Raw_Confirmed_Tenants #where (BatchID >= 8000 and BatchID<9300 )or(BatchID>=79000 and BatchID<=79200)
);

select count(*) from Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 
where ProviderID=6 and VendorID in (select * from temp1) and BatchID >=4100 and BatchID <4300;

update Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 set BatchID=4400 
where ProviderID=6 and VendorID in (select * from temp1) and BatchID >=4100 and BatchID <4300;

-- select * from Empirical_DataStage.NSW_Raw_ZoomInfo_Tenants;

drop temporary table if exists temp1;
create temporary table temp1(
select VendorID from Empirical_DataStage.NSW_Raw_ZoomInfo_Tenants);

select count(*) from Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 
where ProviderID=15 and Vendor<PERSON> in (select * from temp1) and BatchID >=4100 and BatchID <4300;


update Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 set BatchID=4400 
where ProviderID=15 and VendorID in (select * from temp1) and BatchID >=4100 and BatchID <4300;

#select * from Empirical_DataStage.NSW_Raw_Overture_Tenants;
drop temporary table if exists temp1;
create temporary table temp1(
select VendorID from Empirical_DataStage.NSW_Raw_Overture_Tenants );
-- select count(*) from Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 where ProviderID=16 and BatchID >=4100 and BatchID <4300;
update Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 set BatchID=4400 
where ProviderID=16 and VendorID in (select * from temp1) and BatchID >=4100 and BatchID <4300;

-- select * from Empirical_DataStage.NSW_Raw_GoogleMaps_Tenants;
drop temporary table if exists temp1;
create temporary table temp1(
select VendorID from Empirical_DataStage.NSW_Raw_GoogleMaps_Tenants);

update Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 set BatchID=4400 
where ProviderID=18 and VendorID in (select * from temp1) and BatchID >=4100 and BatchID <4300;