ALTER TABLE `Empirical_DataStage`.`NSW_Bronze_Tenants_With_ProviderID_V2` 
ADD COLUMN `NSW_Bronze_Tenants_With_ProviderID_V2` BIGINT NOT NULL AUTO_INCREMENT AFTER `ZI_C_LAST_UPDATED_DATE`,
ADD COLUMN `Tenant_Stage_ID` BIGINT NULL AFTER `NSW_Bronze_Tenants_With_ProviderID_V2`,
ADD PRIMARY KEY (`NSW_Bronze_Tenants_With_ProviderID_V2`),
ADD UNIQUE INDEX `Tenant_Stage_ID_UNIQUE` (`Tenant_Stage_ID` ASC) VISIBLE;
;


ALTER TABLE `Empirical_DataStage`.`NSW_Bronze_Tenants_With_ProviderID_V2` 
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;
ALTER TABLE `Empirical_DataStage`.`NSW_Bronze_Tenants_With_ProviderID_V2` ALTER INDEX `Tenant_Stage_ID_UNIQUE` INVISIBLE;