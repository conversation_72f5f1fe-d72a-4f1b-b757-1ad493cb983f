  -- Retrieve City, State, StateAbbr, and CountryCode from the property if they are NULL
Retrieve_City_State_StateAbbr_CountryCode_from_the_property_if_they_are_NULL

    select count(*) from Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 as nswbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=nswbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
    -- set nswbronze.City=c.CityName 
    where nswbronze.City is  null and nswbronze.BatchID in (4100, 4200) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    

  update Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 as nswbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=nswbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
     set nswbronze.City=c.CityName 
    where nswbronze.City is  null and nswbronze.BatchID in (4100, 4200) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    
     
    select nswbronze.StateAbbr, s.StateAbbr from Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 as nswbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=nswbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
      inner join Empirical_Prod.State as s on c.StateID= s.StateID
    where nswbronze.StateAbbr is  null and nswbronze.BatchID in (4100, 4200) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    
    
    update Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 as nswbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=nswbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
      inner join Empirical_Prod.State as s on c.StateID= s.StateID
    set nswbronze.StateAbbr=s.StateAbbr, nswbronze.State=s.StateID
    where nswbronze.StateAbbr is  null and nswbronze.BatchID in (4100, 4200) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    
    
         select nswbronze.CountryCode, s.CountryID from Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 as nswbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=nswbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
      inner join Empirical_Prod.State as s on c.StateID= s.StateID
    where nswbronze.CountryCode is  null and nswbronze.BatchID in (4100, 4200) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    
    
    update Empirical_DataStage.NSW_Bronze_Tenants_With_ProviderID_V2 as nswbronze
     inner join Empirical_Prod.Address as ad on ad.ParentID=nswbronze.PropertyID
     inner join Empirical_Prod.City as c on c.CityID= ad.CityID
      inner join Empirical_Prod.State as s on c.StateID= s.StateID
    set nswbronze.CountryCode=s.CountryID
    where nswbronze.CountryCode is  null and nswbronze.BatchID in (4100, 4200) and ad.ParentTableID=1 and ad.Sequence=1 and ad.IsActive=1;
    
    