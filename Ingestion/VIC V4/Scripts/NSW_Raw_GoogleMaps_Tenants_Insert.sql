ALTER TABLE `Empirical_DataStage`.`NSW_Raw_GoogleMaps_Tenants` 
ADD INDEX `idx_VendorID` (`VendorID` ASC) VISIBLE;
;


drop temporary table if exists temp1;
create temporary table temp1(select a.*,b.BatchID from Empirical_DataStage.NSW_Raw_GoogleMaps_Tenants as a
inner join Empirical_DataStage.NSW_Raw_Confirmed_Tenants as b on a.main_ID=b.VendorID
where b.BatchID not in (9900) and b.Batch<PERSON> is not null);


insert into Empirical_DataStage.Tenants_Stage (
			VendorID,TenantName,Address1,Address2,City,State,StateAbbr,CountryCode,PostalCode,#NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,
            ProviderID,
			Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode,IsProcessed,CreatedDate
        )        
        select VendorID,TenantName,Address1,Address2,City,State,StateAbbr,Country,PostalCode,#NationalID,
			OfficePhone,Fax,CEOName,CEOTitle,LineOfBusiness,SICCode,Revenue,EmployeesAtLocation,EmployeeCount,
			LegalStatus,SubsidiaryCode,PropertyID,Latitude,Longitude,BatchID,
            ProviderID,
			Email,WebsiteURL,ASICEntityStatus,ASICEntityType,ASICEntityClass,ABNStatus,ABN_StatusFromDate,GST_Status,
			GST_StatusFromDate,RegistrationOrIncorporationDate,EntityAge,EmployeeIndicator,RevenueIndicator,HQ_ID,
			HQ_CompanyName,NumberofMembersinHierarchy,ImmediateParentDUNS,ImmediateParentName,ImmediateParentCountry,
			DomesticParentDUNS,DomesticParentName,DomesticParentCountry,GlobalUltimateParentDUNS,GlobalUltimateParentName,
			GlobalUltimateParentCountry,PrimarySICDesc,PrimarySIC3Digit,PrimarySIC3DigitDesc,PrimarySIC2Digit,PrimarySIC2DigitDesc,
			PrimarySICDivision,PrimarySICDivisionDesc,ANZSICCode, 0 as IsProcessed,current_time() as CreatedDate from temp1;
            
            drop temporary table if exists temp1;
create temporary table temp1(select VendorID,Tenant_Stage_ID from Empirical_DataStage.Tenants_Stage where ProviderID=18);

            update Empirical_DataStage.NSW_Raw_GoogleMaps_Tenants as a
		inner join temp1 as b on a.VendorID=b.VendorID
		set a.Tenant_Stage_ID=b.Tenant_Stage_ID where a.Tenant_Stage_ID is null;