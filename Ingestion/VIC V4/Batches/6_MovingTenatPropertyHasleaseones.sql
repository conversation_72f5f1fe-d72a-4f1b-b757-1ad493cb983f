
drop temporary table if exists temp1;
create temporary table temp1(
select distinct BranchID from Empirical_DataStage.VIC_Raw_Confirmed_Tenants a inner join Empirical_DataStage.Tenants_Stage b on a.VendorID=b.VendorID
);

drop temporary table if exists temp2;
create temporary table temp2(
select distinct BranchID from temp1 a inner join Empirical_Tenants.ConfirmedTenants b on a.BranchID=b.CompanyID group by b.CompanyID having count(*)=1
);

drop temporary table if exists temp3;
create temporary table temp3(
select distinct a.VendorID from Empirical_DataStage.VIC_Raw_Confirmed_Tenants a inner join Empirical_DataStage.Tenants_Stage b on a.VendorID=b.VendorID
where BranchID in (select BranchID from temp2)
);

select * from Empirical_DataStage.VIC_Raw_Confirmed_Tenants where VendorID in (select * from temp3) and 
BatchID in (3000,3100,3200,3299) ;

 update Empirical_DataStage.VIC_Raw_Confirmed_Tenants set BatchID= 3900 where Vend<PERSON><PERSON> in (select * from temp3) and  Batch<PERSON> in (3000,3100,3200,3299);