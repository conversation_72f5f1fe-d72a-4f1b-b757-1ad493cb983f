drop temporary table if exists temp3000;
create temporary table temp3000(
select distinct a.* from Empirical_DataStage.VIC_Raw_Confirmed_Tenants a
inner join Empirical_Prod.Address b on a.PropertyID=b.ParentID and b.ParentTableID=1 and b.Sequence=1 and b.IsActive=1
where b.ZipCode=3000 and  a.BatchID in (3000,3100,3200)
);

update Empirical_DataStage.VIC_Raw_Confirmed_Tenants set BatchID=3500
where VIC_Raw_Confirmed_TenantsID in (select VIC_Raw_Confirmed_TenantsID from temp3000);


drop temporary table if exists tempLeaseCT;
create temporary table tempLeaseCT(
select distinct a.*,b.ConfirmedTenantID from Empirical_DataStage.VIC_Raw_Confirmed_Tenants a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and  a.BatchID in (3000,3100,3200)
);

drop temporary table if exists tempLeaseProp;
create temporary table tempLeaseProp(
select distinct a.PropertyID from Empirical_DataStage.VIC_Raw_Confirmed_Tenants a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and  a.BatchID in (3000,3100,3200)
);

drop temporary table if exists tempLeaseRawCT;
create temporary table tempLeaseRawCT(
select distinct a.* from Empirical_DataStage.VIC_Raw_Confirmed_Tenants a
inner join tempLeaseProp b on a.PropertyID=b.PropertyID and  a.BatchID in (3000,3100,3200)
);


drop temporary table if exists tempLeaseTenants;
create temporary table tempLeaseTenants(
select b.* from Empirical_DataStage.Tenants_Stage as a
inner join Empirical_DataStage.VIC_Raw_Confirmed_Tenants b on a.VendorID=b.VendorID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where a.BranchID in (select CompanyID from Empirical_Tenants.ConfirmedTenants 
    where ConfirmedTenantID in (select distinct ConfirmedTenantID from tempLeaseCT)) and a.ProviderID=6 and b.BatchID in (3000,3100,3200)
);

/*

update Empirical_DataStage.VIC_Raw_Confirmed_Tenants set BatchID=3600 -- Existing Tenants with Lease
where VIC_Raw_Confirmed_TenantsID in (select VIC_Raw_Confirmed_TenantsID from tempLeaseTenants);*/



--- Seems not required ---

drop temporary table if exists temp3600;
create temporary table temp3600(
select VendorID from Empirical_DataStage.VIC_Raw_Confirmed_Tenants b  where b.BatchID=3600
);

drop temporary table if exists tempNonLeaseTenants;
create temporary table tempNonLeaseTenants(
select a.* from tempLeaseRawCT as a where VendorID not in (select * from temp3600 b)# on a.VendorID!=b.VendorID where b.BatchID=9600
);


/*
update Empirical_DataStage.VIC_Raw_Confirmed_Tenants  as a-- Tenants without lease
inner join tempNonLeaseTenants as b on a.VIC_Raw_Confirmed_TenantsID=b.VIC_Raw_Confirmed_TenantsID and a.BatchID>=3000 and a.BatchID<3100
set a.BatchID=73000;

update Empirical_DataStage.VIC_Raw_Confirmed_Tenants as a
inner join tempNonLeaseTenants as b on a.VIC_Raw_Confirmed_TenantsID=b.VIC_Raw_Confirmed_TenantsID and a.BatchID>=3100 and a.BatchID<3200
set a.BatchID=73100 ;

update Empirical_DataStage.VIC_Raw_Confirmed_Tenants as a 
inner join tempNonLeaseTenants as b on a.VIC_Raw_Confirmed_TenantsID=b.VIC_Raw_Confirmed_TenantsID and a.BatchID>=3200 and a.BatchID<3300
set a.BatchID=73200;
*/

drop temporary table if exists tempMoveTenants;
create temporary table tempMoveTenants(
select a.* from Empirical_DataStage.VIC_Raw_Confirmed_Tenants as a 
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID
 where b.ProviderID=6 and a.BatchID not in (9900,9800,9700,9600,9400,9398,9397,9392,9391,9390) and a.PropertyID!=b.PropertyID
);