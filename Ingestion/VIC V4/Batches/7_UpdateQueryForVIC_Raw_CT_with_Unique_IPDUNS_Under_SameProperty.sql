drop temporary table if exists temp;
create temporary table temp(
select a.VIC_Raw_Confirmed_TenantsID from Empirical_DataStage.VIC_Raw_Confirmed_Tenants as a 
left outer join Empirical_DataStage.VIC_Raw_CT_with_Unique_IPDUNS_Under_SameProperty as b on a.VendorID=b.VendorID 
where  b.VendorID is null and a.BatchID in (3000, 3100, 3200, 3299, 3400) #group by a.BatchID#,9800,9700
);

update Empirical_DataStage.VIC_Raw_Confirmed_Tenants set BatchID=3400 where VIC_Raw_Confirmed_TenantsID in (select * from temp);