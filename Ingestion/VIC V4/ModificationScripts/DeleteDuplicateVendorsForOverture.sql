drop temporary table if exists temp1;
create temporary table temp1(
select <PERSON>endor<PERSON> from Empirical_DataStage.Tenants_Stage where ProviderID=16 group by VendorID having count(*)>1);

drop temporary table if exists temp2;
create temporary table temp2(
select * from Empirical_DataStage.Tenants_Stage where <PERSON><PERSON><PERSON><PERSON> in (select * from temp1)
);

drop temporary table if exists temp3;
create temporary table temp3(
select Tenant_Stage_Id from temp2 where IsProcessed=0
);
select * from temp2;
select * from Empirical_DataStage.Tenants_Stage where Tenant_Stage_Id in (select * from temp3);

#delete from Empirical_DataStage.Tenants_Stage where Tenant_Stage_Id in (select * from temp3);