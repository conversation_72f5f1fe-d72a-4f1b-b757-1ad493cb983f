select distinct a.BatchID,count(*) from Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a 
left outer join Empirical_DataStage.NSW_Raw_CT_with_Unique_IPDUNS_Under_SameProperty as b on a.VendorID=b.VendorID where a.BatchID not in (9900) and b.Vendor<PERSON> is null group by a.BatchID#,9800,9700)
;
select distinct a.BatchID,count(*) from Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a group by a.<PERSON>ch<PERSON>;
drop temporary table if exists temp;
create temporary table temp(
select a.NSW_Raw_Confirmed_TenantsID from Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a 
left outer join Empirical_DataStage.NSW_Raw_CT_with_Unique_IPDUNS_Under_SameProperty as b on a.VendorID=b.VendorID 
where a.BatchID not in (9900) and b.VendorID is null and a.BatchID>=9000 and a.BatchID<9100 #group by a.BatchID#,9800,9700)
);
update Empirical_DataStage.NSW_Raw_Confirmed_Tenants set BatchID=9390 where NSW_Raw_Confirmed_TenantsID in (select * from temp);

drop temporary table if exists temp;
create temporary table temp(select a.NSW_Raw_Confirmed_TenantsID  from Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a 
left outer join Empirical_DataStage.NSW_Raw_CT_with_Unique_IPDUNS_Under_SameProperty as b on a.VendorID=b.VendorID 
where a.BatchID not in (9900) and b.VendorID is null and a.BatchID>=9100 and a.BatchID<9200 # group by a.BatchID#,9800,9700)
);
update Empirical_DataStage.NSW_Raw_Confirmed_Tenants set BatchID=9391 where NSW_Raw_Confirmed_TenantsID in (select * from temp);


drop temporary table if exists temp;
create temporary table temp(select a.NSW_Raw_Confirmed_TenantsID  from Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a 
left outer join Empirical_DataStage.NSW_Raw_CT_with_Unique_IPDUNS_Under_SameProperty as b on a.VendorID=b.VendorID 
where a.BatchID not in (9900) and b.VendorID is null and a.BatchID>=9200 and a.BatchID<9300 # group by a.BatchID#,9800,9700)
);
update Empirical_DataStage.NSW_Raw_Confirmed_Tenants set BatchID=9392 where NSW_Raw_Confirmed_TenantsID in (select * from temp);


drop temporary table if exists temp;
create temporary table temp(select a.NSW_Raw_Confirmed_TenantsID  from Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a 
left outer join Empirical_DataStage.NSW_Raw_CT_with_Unique_IPDUNS_Under_SameProperty as b on a.VendorID=b.VendorID 
where a.BatchID not in (9900) and b.VendorID is null and a.BatchID=9700 # group by a.BatchID#,9800,9700)
);
update Empirical_DataStage.NSW_Raw_Confirmed_Tenants set BatchID=9397 where NSW_Raw_Confirmed_TenantsID in (select * from temp);


drop temporary table if exists temp;
create temporary table temp(select a.NSW_Raw_Confirmed_TenantsID  from Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a 
left outer join Empirical_DataStage.NSW_Raw_CT_with_Unique_IPDUNS_Under_SameProperty as b on a.VendorID=b.VendorID 
where a.BatchID not in (9900) and b.VendorID is null and a.BatchID=9800 # group by a.BatchID#,9800,9700)
);
update Empirical_DataStage.NSW_Raw_Confirmed_Tenants set BatchID=9398 where NSW_Raw_Confirmed_TenantsID in (select * from temp);

