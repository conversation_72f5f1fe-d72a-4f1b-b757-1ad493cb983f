drop temporary table if exists temp1;
create temporary table temp1(
select <PERSON><PERSON><PERSON><PERSON>,Tenant_Stage_Id from Empirical_DataStage.Tenants_Stage where ProviderID=14 ) ;

select a.Vendor<PERSON>,b.VendorID,a.Tenant_Stage_ID,b.Tenant_Stage_ID from Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a
inner join temp1 as b on a.VendorID =b.VendorID ;

update Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a
inner join temp1 as b on a.VendorID =b.VendorID set a.Tenant_Stage_ID=b.Tenant_Stage_ID ;
