drop temporary table if exists temp2000;
create temporary table temp2000(
select distinct a.* from Empirical_DataStage.NSW_Raw_Confirmed_Tenants a
inner join Empirical_Prod.Address b on a.PropertyID=b.ParentID and b.ParentTableID=1 and b.Sequence=1 and b.IsActive=1
where b.ZipCode=2000 and a.BatchID <9300
);

drop temporary table if exists temp3000;
create temporary table temp3000(
select distinct a.* from Empirical_DataStage.NSW_Raw_Confirmed_Tenants a
inner join Empirical_Prod.Address b on a.PropertyID=b.ParentID and b.ParentTableID=1 and b.Sequence=1 and b.IsActive=1
where b.ZipCode=3000 and a.BatchID <9300
);

drop temporary table if exists tempLeaseCT;
create temporary table tempLeaseCT(
select distinct a.*,b.ConfirmedTenantID from Empirical_DataStage.NSW_Raw_Confirmed_Tenants a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and c.<PERSON>ip<PERSON>ode not in (3000 ,2000) and a.<PERSON>ch<PERSON> <9300
);

drop temporary table if exists tempLeaseProp;
create temporary table tempLeaseProp(
select distinct a.PropertyID from Empirical_DataStage.NSW_Raw_Confirmed_Tenants a
inner join Empirical_Prod.Lease b on a.PropertyID=b.PropertyID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where  b.IsActive=1 and c.ZipCode not in (3000 ,2000) and a.BatchID <9300
);

drop temporary table if exists tempLeaseRawCT;
create temporary table tempLeaseRawCT(
select distinct a.* from Empirical_DataStage.NSW_Raw_Confirmed_Tenants a
inner join tempLeaseProp b on a.PropertyID=b.PropertyID and a.BatchID <9300 
);


drop temporary table if exists tempLeaseTenants;
create temporary table tempLeaseTenants(
select b.* from Empirical_DataStage.Tenants_Stage as a
inner join Empirical_DataStage.NSW_Raw_Confirmed_Tenants b on a.VendorID=b.VendorID
inner join Empirical_Prod.Address c on a.PropertyID=c.ParentID and c.ParentTableID=1 and c.Sequence=1 and c.IsActive=1
where a.BranchID in (select CompanyID from Empirical_Tenants.ConfirmedTenants 
	where ConfirmedTenantID in (select distinct ConfirmedTenantID from tempLeaseCT)) and a.ProviderID=6 and b.BatchID <9300 and c.ZipCode not in (3000 ,2000)
);

/*
update Empirical_DataStage.NSW_Raw_Confirmed_Tenants set BatchID=9400 -- 2000 ZipCode Batches
where NSW_Raw_Confirmed_TenantsID in (select NSW_Raw_Confirmed_TenantsID from temp2000);
update Empirical_DataStage.NSW_Raw_Confirmed_Tenants set BatchID=9500 -- 3000 ZipCode Batches
where NSW_Raw_Confirmed_TenantsID in (select NSW_Raw_Confirmed_TenantsID from temp3000);
update Empirical_DataStage.NSW_Raw_Confirmed_Tenants set BatchID=9600 -- Existing Tenants with Lease
where NSW_Raw_Confirmed_TenantsID in (select NSW_Raw_Confirmed_TenantsID from tempLeaseTenants);*/

drop temporary table if exists temp9600;
create temporary table temp9600(
select VendorID from Empirical_DataStage.NSW_Raw_Confirmed_Tenants b  where b.BatchID=9600
);

drop temporary table if exists tempNonLeaseTenants;
create temporary table tempNonLeaseTenants(
select a.* from tempLeaseRawCT as a where VendorID not in (select * from temp9600 b)# on a.VendorID!=b.VendorID where b.BatchID=9600
);
/*
update Empirical_DataStage.NSW_Raw_Confirmed_Tenants  as a-- Tenants without lease
inner join tempNonLeaseTenants as b on a.NSW_Raw_Confirmed_TenantsID=b.NSW_Raw_Confirmed_TenantsID and a.BatchID>=9000 and a.BatchID<9100
set a.BatchID=79000;

update Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a
inner join tempNonLeaseTenants as b on a.NSW_Raw_Confirmed_TenantsID=b.NSW_Raw_Confirmed_TenantsID and a.BatchID>=9100 and a.BatchID<9200
set a.BatchID=79100 ;

update Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a 
inner join tempNonLeaseTenants as b on a.NSW_Raw_Confirmed_TenantsID=b.NSW_Raw_Confirmed_TenantsID and a.BatchID>=9200 and a.BatchID<9300
set a.BatchID=79200;
*/

drop temporary table if exists tempMoveTenants;
create temporary table tempMoveTenants(
select a.* from Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a 
inner join Empirical_DataStage.Tenants_Stage as b on a.VendorID=b.VendorID
 where b.ProviderID=6 and a.BatchID not in (9900,9800,9700,9600,9400,9398,9397,9392,9391,9390) and a.PropertyID!=b.PropertyID
);


-- For Tenants having Lease with new property before ingestion


drop temporary table if exists temp1;
create temporary table temp1(
select a.*,b.BranchID from Empirical_DataStage.NSW_Raw_Confirmed_Tenants as a 
 inner join Empirical_DataStage.Tenants_Stage b on a.VendorID=b.VendorID
 where b.ProviderID=6 and a.BatchID not in (9900,9800,9700,9600,9400,9398,9397,9392,9391,9390) and a.PropertyID!=b.PropertyID and b.BranchID is not null
 );
 
 
drop temporary table if exists temp2;
create temporary table temp2(
 select a.* from temp1 a inner join Empirical_Tenants.ConfirmedTenants b on a.BranchID=b.CompanyID and b.ProviderID=1 and b.IsActive=1
 inner join Empirical_Prod.Lease c on c.ConfirmedTenantID=b.ConfirmedTenantID and a.PropertyID=c.PropertyID and c.TransactionOriginationTypeID=1 and c.IsActive=1
 );
 
 -- update Empirical_DataStage.NSW_Raw_Confirmed_Tenants set BatchID=89600 where VendorID in (Select VendorID from temp2);
 
 