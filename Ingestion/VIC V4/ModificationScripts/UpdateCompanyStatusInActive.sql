drop temporary table if exists temp1;
create temporary table temp1(
select distinct PropertyID from Empirical_DataStage.NSW_Raw_Confirmed_Tenants where <PERSON>ch<PERSON> not in (9900,9800,9700) and <PERSON>ch<PERSON> is not null
);

drop temporary table if exists temp2;
create temporary table temp2(select Tenant_Stage_ID from Empirical_DataStage.NSW_Raw_Confirmed_Tenants 
where <PERSON><PERSON> in (select * from temp1) and BatchID not in (9900,9800,9700)  and Batch<PERSON> is not null);

drop temporary table if exists temp3;
create temporary table temp3(select * from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select * from temp2) and BatchID not in (9700));

drop temporary table if exists temp3;
create temporary table temp3(select PropertyID,Tenant_Stage_Id,ConfirmedTenantID,BranchID,TenantName,VendorID 
from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select * from temp2));

drop temporary table if exists temp4; -- distinct propertyid's in tenant stage
create temporary table temp4(select distinct PropertyID from temp3);

drop temporary table if exists temp5; -- suite tenant details of those properties
create temporary table temp5(select * from Empirical_Prod.SuiteTenant where PropertyID in (select * from temp4) and CreatedDate >'2024-02-26');

drop temporary table if exists temp6; -- companies mapped to those confirmed tenant which in the above suite tenant
create temporary table temp6(select CompanyID from Empirical_Tenants.ConfirmedTenants where ConfirmedTenantID in (  select ConfirmedTenantID from temp5));

drop temporary table if exists temp7;-- getting all the confirmed tenants(both Provider 1,5) for those companies
create temporary table temp7(select * from Empirical_Tenants.ConfirmedTenants where CompanyID in (  select CompanyID from temp6));

drop temporary table if exists temp8; -- getting all the company details
create temporary table temp8(select * from Empirical_Prod.Company where CompanyID in (  select CompanyID from temp6));

drop temporary table if exists temp9;-- getting duplicate Alt Company Names
create temporary table temp9(select AltCompanyName from temp8 group by AltCompanyName having count(*)>1);

drop temporary table if exists temp10; -- getting Companies having duplicate Alt Company Name
create temporary table temp10(select * from Empirical_Prod.Company where AltCompanyName in (select * from temp9) and ModifiedDate >'2024-02-26');

drop temporary table if exists temp11; -- getting companies which are in tenant stage
create temporary table temp11(select * from Empirical_Prod.Company where CompanyID in (  select BranchID from temp3));

drop temporary table if exists temp12; -- getting 3 times repeated companies which are in tenant stage
create temporary table temp12(select AltCompanyName from temp10 group by AltCompanyName having count(*)>2);

drop temporary table if exists temp13;
create temporary table temp13(
select CompanyID from temp11 where CompanyId in (
select CompanyID from temp10  where AltCompanyName in (select * from temp12)));

drop temporary table if exists CompanyIDNeedToBeDeleted;
create temporary table CompanyIDNeedToBeDeleted(
select CompanyID from temp10  where AltCompanyName in (select * from temp12) and CompanyID not in (select * from temp13) and CompanyID !=128213);
;

drop temporary table if exists temp12; -- getting 3 times repeated companies which are in tenant stage
create temporary table temp12(select AltCompanyName from temp10 group by AltCompanyName having count(*)=2);

drop temporary table if exists temp13;
create temporary table temp13(
select CompanyID from temp11 where CompanyId in (
select CompanyID from temp10  where AltCompanyName in (select * from temp12)));

insert into CompanyIDNeedToBeDeleted(
select CompanyID from temp10  where AltCompanyName in (select * from temp12) and CompanyID not in (select * from temp13) and CompanyID !=128213);

select * from Empirical_DataStage.Tenants_Stage where ProviderID=14 and BranchID in (select * from CompanyIDNeedToBeDeleted);

update Empirical_Prod.Company set IsActive=0 where (
 CompanyID in (select * from CompanyIDNeedToBeDeleted) and CompanyID !=128213);

drop temporary table if exists tempConfirmedTenantID;
create temporary table tempConfirmedTenantID(select ConfirmedTenantID from Empirical_Tenants.ConfirmedTenants where CompanyID in  (select * from CompanyIDNeedToBeDeleted));

-- Marking Company records inactive and also in dependent tables
update Empirical_Tenants.ConfirmedTenants  set IsActive=0 where ConfirmedTenantID in (select ConfirmedTenantID from tempConfirmedTenantID);

update Empirical_Tenants.ConfirmedTenantsFieldAudit  set IsActive=0 where ConfirmedTenantID in (select ConfirmedTenantID from tempConfirmedTenantID);

update Empirical_Prod.CompanyContact set IsActive=0 where  CompanyID in (select * from CompanyIDNeedToBeDeleted);

#update Empirical_Prod.NationalIdentifiers set IsActive=0 where CompanyID in (select * from CompanyIDNeedToBeDeleted);
select * from Empirical_Prod.NationalIdentifiers where CompanyID in (select * from CompanyIDNeedToBeDeleted);

select * from Empirical_Prod.NationalIdentifiers where ConfirmedTenantID in (select ConfirmedTenantID from Empirical_Tenants.ConfirmedTenants  where CompanyID in  (select * from CompanyIDNeedToBeDeleted));

update Empirical_Prod.Address set IsActive=0 where ParentID in (select * from CompanyIDNeedToBeDeleted) and ParentTableID=6 ;

update Empirical_Prod.SuiteTenant set IsActive=0 where ConfirmedTenantID in (select ConfirmedTenantID from Empirical_Tenants.ConfirmedTenants  where CompanyID in  (select * from CompanyIDNeedToBeDeleted));

update Empirical_Prod.CompanyRelationship  set IsActive=0 where ChildCompanyID in (select * from CompanyIDNeedToBeDeleted) and ChildCompanyID !=128213;

update Empirical_Prod.Company set IsActive=0 where CompanyID in (select ParentCompanyID from Empirical_Prod.CompanyRelationship where ChildCompanyID in (select * from CompanyIDNeedToBeDeleted) and ChildCompanyID !=128213);
