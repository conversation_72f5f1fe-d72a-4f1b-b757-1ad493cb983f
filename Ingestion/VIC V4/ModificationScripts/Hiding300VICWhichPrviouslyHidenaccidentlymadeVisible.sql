
SELECT 
    *
FROM
    Empirical_Prod.Company
WHERE
    ModifiedBy = 22
        AND ModifiedDate IN ('2024-04-02 12:07:03')
        AND IsHidden = 0
        AND HidedBy = 22
        AND HidedDate IN ('2024-04-02 12:07:03')
        AND HideReasonID IS NULL
        AND SubHideReasonID IS NULL
        AND HideReasonComments IN ('Not updating the record via the AL tenant automation gold bucket data.' , 'Making 2000 and 3000 postal code tenants are visible')
        AND CompanyID IN (SELECT 
            CompanyID
        FROM
            Empirical_DataStage.hidedcompanyDataVIC);
     
   /**
 UPDATE Empirical_Prod.Company a
        INNER JOIN
    Empirical_DataStage.hidedcompanyDataVIC b ON a.CompanyID = b.CompanyID 
SET 
    a.HidedBy = b.HidedBy,
    a.HidedDate = b.HidedDate,
    a.HideReasonID = b.HideReasonID,
    a.SubHideReasonID = b.SubHideReasonID,
    a.HideReasonComments = b.HideReasonComments,
    a.ModifiedBy = 22,
    a.<PERSON>H<PERSON><PERSON> = b.<PERSON>H<PERSON><PERSON>,
    a.ModifiedDate = CURRENT_TIMESTAMP()
WHERE
    a.ModifiedBy = 22
        AND a.ModifiedDate IN ('2024-04-02 12:07:03')
        AND a.IsHidden = 0
        AND a.HidedBy = 22
        AND a.HidedDate IN ('2024-04-02 12:07:03')
        AND a.HideReasonID IS NULL
        AND a.SubHideReasonID IS NULL
        AND a.HideReasonComments IN ('Not updating the record via the AL tenant automation gold bucket data.' , 'Making 2000 and 3000 postal code tenants are visible');
   
    **/
  select 
  a.CompanyID,b.CompanyID,
  a.HidedBy,b.HidedBy,
  a.HidedDate,b.HidedDate,
  a.HideReasonID,b.HideReasonID,
  a.HideReasonComments,
  b.HideReasonComments,
  a.IsHidden,b.IsHidden,
 a.SubHideReasonID, b.SubHideReasonID,
  a.ModifiedBy, b.ModifiedBy,
   a.ModifiedDate, b.ModifiedDate
 from Empirical_Prod.Company a inner join Empirical_DataStage.hidedcompanyDataVIC b on a.CompanyID=b.CompanyID;