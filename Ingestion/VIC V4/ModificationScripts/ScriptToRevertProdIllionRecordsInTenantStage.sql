#create table Empirical_DataStage.tempTenant_Stage(select * from Empirical_DataStage.Tenants_Stage);
-- Queries to take backup from old dump
drop temporary table if exists temp1;
create temporary table temp1(
SELECT b.* FROM Empirical_DataStage.NSW_Illoion_Tenants_Address_Revert as a
inner join Empirical_DataStage.Tenants_Stage as b on a.Tenant_Stage_ID=b.Tenant_Stage_Id where ModifiedDate);

create table Empirical_DataStage.Tenants_Stage_Address_Revert (
select * from temp1);

select * from Empirical_DataStage.Tenants_Stage_Address_Revert where ModifiedDate>'2024-03-05 23:59:59';

-- Updated query to revert

update Empirical_DataStage.Tenants_Stage as a
inner join Empirical_DataStage.Tenants_Stage_Address_Revert as b on a.Tenant_Stage_Id=b.Tenant_Stage_Id and a.VendorID=b.VendorID
set 
a.TenantName=b.TenantName,
a.Address1=b.Address1,
a.Address2=b.Address2,
a.City=b.City,
a.State=b.State,
a.StateAbbr=b.StateAbbr,
a.CountryCode=b.CountryCode,
a.PostalCode=b.PostalCode,
a.NationalID=b.NationalID,
a.OfficePhone=b.OfficePhone,
a.Fax=b.Fax,
a.CEOName=b.CEOName,
a.CEOTitle=b.CEOTitle,
a.LineOfBusiness=b.LineOfBusiness,
a.SICCode=b.SICCode,
a.Revenue=b.Revenue,
a.EmployeesAtLocation=b.EmployeesAtLocation,
a.EmployeeCount=b.EmployeeCount,
a.LegalStatus=b.LegalStatus,
a.StatusCode=b.StatusCode,
a.SubsidiaryCode=b.SubsidiaryCode,
a.IsProcessed=b.IsProcessed,
a.ConfirmedTenantID=b.ConfirmedTenantID,
a.PropertyID=b.PropertyID,
a.MatchingScore=b.MatchingScore,
a.CreatedDate=b.CreatedDate,
a.ModifiedDate=b.ModifiedDate,
a.Tenant_Stage_Id=b.Tenant_Stage_Id,
a.Latitude=b.Latitude,
a.Longitude=b.Longitude,
a.ParentCompanyID=b.ParentCompanyID,
a.BranchID=b.BranchID,
a.BatchID=b.BatchID,
a.ProviderID=b.ProviderID,
a.IsDefault=b.IsDefault,
a.NAICSCode=b.NAICSCode,
a.NACECode=b.NACECode,
a.Email=b.Email,
a.WebsiteURL=b.WebsiteURL,
a.ModifiedBy=b.ModifiedBy,
a.IsHidden=b.IsHidden,
a.IsDeleted=b.IsDeleted,
a.HidedBy=b.HidedBy,
a.HidedDate=b.HidedDate,
a.HideReasonID=b.HideReasonID,
a.HideReasonComments=b.HideReasonComments,
a.ASICEntityStatus=b.ASICEntityStatus,
a.ASICEntityType=b.ASICEntityType,
a.ASICEntityClass=b.ASICEntityClass,
a.ABNStatus=b.ABNStatus,
a.ABN_StatusFromDate=b.ABN_StatusFromDate,
a.GST_Status=b.GST_Status,
a.GST_StatusFromDate=b.GST_StatusFromDate,
a.RegistrationOrIncorporationDate=b.RegistrationOrIncorporationDate,
a.EntityAge=b.EntityAge,
a.EmployeeIndicator=b.EmployeeIndicator,
a.RevenueIndicator=b.RevenueIndicator,
a.HQ_ID=b.HQ_ID,
a.HQ_CompanyName=b.HQ_CompanyName,
a.NumberofMembersinHierarchy=b.NumberofMembersinHierarchy,
a.ImmediateParentDUNS=b.ImmediateParentDUNS,
a.ImmediateParentName=b.ImmediateParentName,
a.ImmediateParentCountry=b.ImmediateParentCountry,
a.DomesticParentDUNS=b.DomesticParentDUNS,
a.DomesticParentName=b.DomesticParentName,
a.DomesticParentCountry=b.DomesticParentCountry,
a.GlobalUltimateParentDUNS=b.GlobalUltimateParentDUNS,
a.GlobalUltimateParentName=b.GlobalUltimateParentName,
a.GlobalUltimateParentCountry=b.GlobalUltimateParentCountry,
a.PrimarySICDesc=b.PrimarySICDesc,
a.PrimarySIC3Digit=b.PrimarySIC3Digit,
a.PrimarySIC3DigitDesc=b.PrimarySIC3DigitDesc,
a.PrimarySIC2Digit=b.PrimarySIC2Digit,
a.PrimarySIC2DigitDesc=b.PrimarySIC2DigitDesc,
a.PrimarySICDivision=b.PrimarySICDivision,
a.PrimarySICDivisionDesc=b.PrimarySICDivisionDesc,
a.SubHideReasonID=b.SubHideReasonID,
a.ANZSICCode=b.ANZSICCode

where  ModifiedDate > '2024-03-05 23:59:59' and ModifiedBy=22;


