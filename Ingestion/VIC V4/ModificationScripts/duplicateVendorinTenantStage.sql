create temporary table duplicateVendorinTenantStage(
select t.<PERSON><PERSON><PERSON><PERSON> from Empirical_DataStage.NSW_Raw_Confirmed_Tenants  as ct
inner join Empirical_DataStage.Tenants_Stage as t on t.VendorID=ct.VendorID
where ct.BatchID not in (9800,
9700,
9900,
9390,
9391,
9392,
9397,
9398,
9400,
9500,
9600,
89600) and    t.Provider<PERSON>=6 group by t.Vendor<PERSON>, ct.BatchID having count(*)>1);

select * from duplicateVendorinTenantStage;

select * from NSW_Raw_Confirmed_Tenants where VendorID in (select * from duplicateVendorinTenantStage);

update NSW_Raw_Confirmed_Tenants set BatchID=9700 where Vendor<PERSON> in (select * from duplicateVendorinTenantStage);

