drop temporary table if exists temp1;
create temporary table temp1(
select distinct PropertyID from Empirical_DataStage.NSW_Raw_Confirmed_Tenants where <PERSON>ch<PERSON> not in (9900,9800,9700) and <PERSON><PERSON><PERSON> is not null
);

drop temporary table if exists temp2;
create temporary table temp2(select Tenant_Stage_ID from Empirical_DataStage.NSW_Raw_Confirmed_Tenants 
where <PERSON><PERSON> in (select * from temp1) and BatchID not in (9900,9800,9700)  and Batch<PERSON> is not null);

drop temporary table if exists temp3;
create temporary table temp3(select * from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select * from temp2));

drop temporary table if exists temp3;
create temporary table temp3(select PropertyID,Tenant_Stage_Id,ConfirmedTenantID,BranchID,TenantName,VendorID 
from Empirical_DataStage.Tenants_Stage where Tenant_Stage_ID in (select * from temp2));

drop temporary table if exists temp4;
create temporary table temp4(select distinct PropertyID from temp3);

drop temporary table if exists temp5;
create temporary table temp5(select * from Empirical_Prod.SuiteTenant where PropertyID in (select * from temp4) and CreatedDate >'2024-02-26');

drop temporary table if exists temp6;
create temporary table temp6(select CompanyID from Empirical_Tenants.ConfirmedTenants where ConfirmedTenantID in (  select ConfirmedTenantID from temp5));

drop temporary table if exists temp7;
create temporary table temp7(select * from Empirical_Tenants.ConfirmedTenants where CompanyID in (  select CompanyID from temp6));

drop temporary table if exists temp8;
create temporary table temp8(select * from Empirical_Prod.Company where CompanyID in (  select CompanyID from temp6));

drop temporary table if exists temp9;
create temporary table temp9(select AltCompanyName from temp8 group by AltCompanyName having count(*)>1);

drop temporary table if exists temp10;
create temporary table temp10(select * from Empirical_Prod.Company where AltCompanyName in (select * from temp9));

drop temporary table if exists temp11;
create temporary table temp11(select * from Empirical_Prod.Company where CompanyID in (  select BranchID from temp3));

select * from temp10;


