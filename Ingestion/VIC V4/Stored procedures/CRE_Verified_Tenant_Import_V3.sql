CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `CRE_Verified_Tenant_Import_V3`(
IN P_BatchID INT,IN P_StateID INT,IN P_CountryID INT,IN P_ProviderID INT,IN P_MetroID INT)
BEGIN
	declare L_NewBranchID int;
    DECLARE foreign_key_error BOOLEAN DEFAULT FALSE;
    DECLARE CONTINUE HANDLER FOR SQLSTATE '23000' SET foreign_key_error = TRUE;

    set L_NewBranchID=null;
		DROP TEMPORARY TABLE IF EXISTS tempTenantStageALA;
        CREATE TEMPORARY TABLE tempTenantStageALA
        (
				SELECT 
		`Tenant_Stage_Id`,
		#DUNS,
		`TenantNAME` as CompanyName,
		`ADDRESS1` as Address1, 
		`ADDRESS2` as Address2,
        14 as CountryID,
		/*`CITY` AS City,
		C.CityID,
		#`STATEABBR`,
		S.StateID,
		
		`PostalCode` as Zipcode,*/
		#concat(trim(LEADING '0' from `CountryCode`),OfficePhone) as OfficePhone,
        CASE 
			WHEN OfficePhone LIKE '1%' THEN OfficePhone
			WHEN OfficePhone LIKE '0%' THEN CONCAT('61', SUBSTRING(OfficePhone, 2))
			WHEN OfficePhone REGEXP '^[2-3847-8]' THEN CONCAT('61', OfficePhone)
            else OfficePhone
		END as OfficePhone,
		CASE WHEN `FAX` ='' THEN '' ELSE FAX END as Fax,
		`SICCode` as SICCode,
		P_MetroID as MetroID,
		1 as BranchStatus,
		0 as IsMember,
        Email,
        Latitude,
        Longitude,
        PropertyID,
        VendorID,
        CEOName,
        CEOTitle, 
        LineOfBusiness,
        EmployeesAtLocation,
        NAICSCode,
        NACECode,
        ANZSICCode,
        SubsidiaryCode,
        StatusCode,
        Revenue,
        RevenueIndicator,
        RegistrationOrIncorporationDate,
        PrimarySIC2DigitDesc,
        PrimarySIC3DigitDesc,
        PrimarySICDivisionDesc,
        WebsiteURL,
		EmployeeCount,
        NationalID
	FROM
		Tenants_Stage ST
		/*	JOIN
		Empirical_Prod.City C ON C.CityName = TRIM(LOWER(ST.`CITY`))
			AND C.StateID = P_StateID
			JOIN
		Empirical_Prod.State S ON S.StateAbbr= TRIM(LOWER(ST.`STATEABBR`))
			#AND S.CountryID = P_CountryID 
            and C.StateID=S.StateID*/
	WHERE
		ST.BatchID=P_BatchID and ST.BranchID is null
            and (IsProcessed is null or IsProcessed =0)  
		and ST.ProviderID=P_ProviderID
        );
        
        CREATE INDEX idx_Tenant_Stage_Id_ALA ON tempTenantStageALA (Tenant_Stage_Id);
		DROP TEMPORARY TABLE IF EXISTS tempIllionTenantStage;
        CREATE TEMPORARY TABLE tempIllionTenantStage(
			select * from Tenants_Stage as a where VendorID in (select VendorID from tempTenantStageALA) and ProviderID=6
        );
        CREATE INDEX idx_Tenant_Stage_Id_ill ON tempIllionTenantStage (Tenant_Stage_ID);
        CREATE INDEX idx_VendorID_ill ON tempIllionTenantStage (VendorID); 
       SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED; -- Set isolation level
        select min(Tenant_Stage_Id) into @minTenantStageID from tempTenantStageALA;
        -- select * from tempTenantStageALA;
		SET autocommit = 0;
        while @minTenantStageID is not null
        do
			-- Start the outer transaction
			START TRANSACTION;

			select distinct VendorID,PropertyID into @ALA_VendorID, @Incoming_PropertyID  from tempTenantStageALA where Tenant_Stage_ID=@minTenantStageID;
          -- select distinct VendorID   from tempTenantStageALA where Tenant_Stage_ID=@minTenantStageID;
            
            
			set @CRE_BranchID=-1, @CRE_BranchID1=null, @ExistingPropertyID=null, @MovingToOtherProperty=0, @Illion_Tenant_Stage_ID=null;
            
          --  select distinct BranchID 
           -- from tempIllionTenantStage where VendorID=@ALA_VendorID;
            
			select distinct BranchID,PropertyID,Tenant_Stage_Id into @CRE_BranchID1,@ExistingPropertyID,@Illion_Tenant_Stage_ID
            from tempIllionTenantStage where VendorID=@ALA_VendorID;
            

			if @ExistingPropertyID != @Incoming_PropertyID then 
              set @MovingToOtherProperty=1;
			end if;

            if @CRE_BranchID1 is null then
				set @CRE_BranchID=-1;
			else
				 set @CRE_BranchID=@CRE_BranchID1;
             end if;
             
            if @CRE_BranchID1 is not null and EXISTS ( Select 1 From Empirical_Prod.Company Where CompanyID=@CRE_BranchID1) then 
			 	set @CRE_BranchID=@CRE_BranchID1;
			else 
				set @CRE_BranchID=-1;
			end if;

            Select 
				 CompanyName,
				 #DUNS,
				 Address1,
				 Address2,
				 #CityID ,StateID,CountryID,Zipcode,
                 OfficePhone,Fax,SICCode,MetroID,BranchStatus,IsMember,Latitude,Longitude,22,  PropertyID,Tenant_Stage_Id,Email,
                 CEOName,
				CEOTitle, 
				LineOfBusiness,
				EmployeesAtLocation,
				NAICSCode,
				NACECode,
				ANZSICCode,
				SubsidiaryCode,
				StatusCode,
                Revenue,
				RevenueIndicator,
				RegistrationOrIncorporationDate,
				PrimarySIC2DigitDesc,
				PrimarySIC3DigitDesc,
				PrimarySICDivisionDesc,
				WebsiteURL,
				EmployeeCount  ,
                NationalID
				 INTO @CompanyName,@Address1,@Address2,
                 #@CityID,@StateID,@CountryID,@Zipcode,
                 @OfficePhone,@Fax,
				 @SICCode,@MetroID,@BranchStatus,@IsMember,@Latitude,@Longitude,@EntityID ,@PropertyID,@Tenant_Stage_Id,@Email,
                 @CEOName,
				@CEOTitle, 
				@LineOfBusiness,
				@EmployeesAtLocation,
				@NAICSCode,
				@NACECode,
				@ANZSICCode,
				@SubsidiaryCode,
				@StatusCode,
                @Revenue,
				@RevenueIndicator,
				@RegistrationOrIncorporationDate,
				@PrimarySIC2DigitDesc,
				@PrimarySIC3DigitDesc,
				@PrimarySICDivisionDesc,
				@WebsiteURL,
				@EmployeeCount ,
                @NationalID
                
				 From tempTenantStageALA where Tenant_Stage_Id=@minTenantStageID;  

			if @CRE_BranchID = -1  then
				 -- Create Company
                 INSERT INTO Empirical_Prod.Company
						(
							CompanyName,					
							CreatedBy,
							CreatedDate,
							ModifiedBy,
							ModifiedDate
						)
						Values
						(
							@CompanyName,
							@EntityID,
							current_timestamp(),
							@EntityID,
							current_timestamp()
						);
				 
					select  LAST_INSERT_ID() INTO @ParentCompanyID;         
				else
                --  Need to get ParnetCompanyID
                
                -- get active parent company
					select ParentCompanyID into @ParentCompanyID from Empirical_Prod.CompanyRelationship where ChildCompanyID=@CRE_BranchID and IsActive=1;
				END IF;

				select AddressText,CityID,StateID,ZipCode,CountyID,CountryID into @Address1,@CityID,@StateID,@ZipCode,@CountyID,@CountryID from Empirical_Prod.Address where ParentID=@PropertyID and ParentTableID=1 and Sequence=1;
				
                if @CRE_BranchID !=-1 then
                select Address2 into @Address2 from Empirical_Prod.Address where ParentID=@CRE_BranchID and ParentTableID=6 and Sequence=1;
				end if;
               CALL Empirical_Prod.CRE_Import_Branch_Save_Zessta(
					@CRE_BranchID, -- P_CompanyID INT, 
					@ParentCompanyID, -- P_ParentCompanyID INT,
					NULL, -- P_ConfirmedTenantID int,
					null, -- P_CompanyName Varchar(200),
					@WebsiteURL, -- P_Website  varchar(225),	
					8, -- P_CompanyTypeID INT,
					NULL, -- P_IsNationalBrokerageCompany tinyint(1),
					NULL, -- p_IsPublicCompany tinyint(1),
					NULL, -- P_TickerSymbol varchar(20),
					@Address1, -- P_Address1 VARCHAR(200),
					@Address2, -- P_Address2 VARCHAR(50),
					@CityID, -- P_CityID INT,
					@StateID, -- P_StateID INT,
					@Zipcode, -- P_ZipCode Varchar(10),
					@CountyID, -- P_CountyID INT,
					@CountryID, -- P_CountryID INT,
					@MetroID, -- P_MetroID INT,
					@Latitude, -- P_Latitude DECIMAL(21,14),
					@Longitude, -- P_Longitude DECIMAL(21,14),
					@OfficePhone,-- P_OfficePhone varchar(20),
					@Fax, -- P_Fax varchar(20),
					@Email, -- P_Email varchar(255),
					1,-- P_IsActive tinyint(1),
					NULL, -- P_IsCountyHQ tinyint(1),
					NULL, -- P_IsGlobalHQ tinyint(1),
					NULL, -- P_Researcher int ,
					NULL, -- P_SalesRep int,
					NULL, -- P_SupportAgent int,
					@NAICSCode, -- P_NAICSCode Varchar(20),
					@SICCode, -- P_ISIC varchar(20),
					null, -- P_RatingTierID int,
				-- 	0, -- P_IsMember tinyint(1),
					@ALA_VendorID,-- @DUNS,-- P_ExtVendorID
					1, -- P_ProviderID,                
					@EntityID,-- P_EntityID INT-- Logged in user  
					null,
					null,
					@NationalID, 
					@PropertyID,                    
					@EmployeeCount , -- Employee count
					@ANZSICCode,
					@CEOName,
					@CEOTitle,
					@LineOfBusiness,
					@EmployeesAtLocation,
					@LegalStatus,
					@StatusCode,
					@SubsidiaryCode,
					@NACECode,
					@PrimarySICDivisionDesc,
					@PrimarySIC2DigitDesc,
					@PrimarySIC3DigitDesc,
					@RegistrationOrIncorporationDate,
                    @Revenue,
					@RevenueIndicator,
                    @minTenantStageID,
					@MovingToOtherProperty,
					L_NewBranchID
				);
			SELECT  ConfirmedTenantID INTO @VendorTenantID from Empirical_Tenants.ConfirmedTenants CT  WHERE CompanyID=L_NewBranchID and ProviderID=1 limit 1;
            
         --    SELECT  ConfirmedTenantID,L_NewBranchID from Empirical_Tenants.ConfirmedTenants CT  WHERE CompanyID=L_NewBranchID and ProviderID=1 limit 1;
            
			drop temporary table if exists tempTenantStageIDs;
            create temporary table tempTenantStageIDs(
			select Tenant_Stage_ID from Empirical_DataStage.VIC_Raw_CompanyWebsites_Tenants where main_id=@ALA_VendorID
            union
			select Tenant_Stage_ID from Empirical_DataStage.VIC_Raw_GoogleMaps_Tenants where main_id=@ALA_VendorID
            union
            select Tenant_Stage_ID from Empirical_DataStage.VIC_Raw_Overture_Tenants where main_id=@ALA_VendorID
            union
            select Tenant_Stage_ID from Empirical_DataStage.VIC_Raw_ZoomInfo_Tenants where main_id=@ALA_VendorID
            union
            select Tenant_Stage_ID from Empirical_DataStage.VIC_Raw_Confirmed_Tenants where VendorID=@ALA_VendorID
            union
            select Tenant_Stage_ID from Empirical_DataStage.VIC_Raw_Illion_Tenants where VendorID=@ALA_VendorID);
            
            
			UPDATE Tenants_Stage as TS
            inner join tempTenantStageIDs as TTS on TS.Tenant_Stage_ID=TTS.Tenant_Stage_ID
            SET ParentCompanyID=@ParentCompanyID, BranchID=L_NewBranchID,ConfirmedTenantID=@VendorTenantID,IsProcessed=1,ModifiedDate=current_timestamp();

			UPDATE Tenants_Stage SET PropertyID=@Incoming_PropertyID,ModifiedDate=current_timestamp()
			where Tenant_Stage_Id=@Illion_Tenant_Stage_ID;

			select min(Tenant_Stage_Id) into @minTenantStageID from tempTenantStageALA
			WHERE Tenant_Stage_Id > @minTenantStageID
			ORDER BY Tenant_Stage_Id;  
			  

			-- Enable automatic commit (optional, depending on your requirements)
			 IF foreign_key_error THEN
        -- Handle the foreign key constraint violation
			ROLLBACK;
			SELECT 'Error 1452: Cannot add or update a child row. Foreign key constraint fails.' AS Result;
		ELSE
			-- Commit changes if no error
			COMMIT;
		-- 	SELECT 'Changes committed successfully.' AS Result;
		END IF;
        end while;
        	SET autocommit = 1;
		DROP TEMPORARY TABLE IF EXISTS tempTenantStageALA;
        DROP TEMPORARY TABLE IF EXISTS tempIllionTenantStage;
END