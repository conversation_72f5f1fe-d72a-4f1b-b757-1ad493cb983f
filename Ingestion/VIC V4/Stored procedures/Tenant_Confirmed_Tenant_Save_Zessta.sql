CREATE DEFINER=`imperium_admin`@`%` PROCEDURE `Tenant_Confirmed_Tenant_Save_Zessta`(
IN P_ConfirmedTenantID int(11),
IN P_TenantName varchar(255),
IN P_CompanyName varchar(255),
IN P_AlternateCompanyName varchar(255),
IN P_Address1 varchar(255),
IN P_Address2 varchar(100),
IN P_AddressStreetNumber varchar(45),
IN P_AddressStreetName varchar(45),
IN P_CityID int(11),
IN P_StateID int(11),
IN P_ZipCode varchar(10),
IN P_CountyID int(11),
IN P_CountryID int(11),
IN P_AddressText varchar(255),
IN P_ExtVendorID varchar(45),
IN P_ProviderID int(11),
IN P_MetroID int(11),
IN P_LoginEntityID int(11),
IN P_OfficePhone varchar(20),
IN P_CompanyID int(11),
IN P_Email varchar(255),
IN P_WebsiteURL varchar(255),
IN P_Fax varchar(50),
IN P_FloorNumber varchar(45),
IN P_NationalID varchar(45),
IN P_EmployeeCount int(11),
IN P_Revenue varchar(50),
IN P_ABN varchar(15),
IN P_ACN varchar(15),
IN P_ANZSIC int(11),
IN P_CEOName varchar(100),
IN P_CEOTitle varchar(100), 
IN P_LineOfBusiness varchar(100),
IN P_SICCode varchar(100),
IN P_EmployeesAtLocation varchar(100),
IN P_LegalStatus varchar(100),
IN P_StatusCode varchar(100),
IN P_SubsidiaryCode varchar(100),
IN P_NAICSCode varchar(100),
IN P_NACECode varchar(100),
IN P_PrimarySICDivisionDesc varchar(100),
IN P_PrimarySIC2DigitDesc varchar(100),
IN P_PrimarySIC3DigitDesc varchar(100),
IN P_RegistrationOrIncorporationDate varchar(100),
IN P_RevenueIndicator varchar(100),
IN P_Tenant_Stage_ID INT,
IN P_PropertyID INT,
IN P_EntityID INT,
IN P_MovingToOtherProperty tinyint(1),
OUT P_NewConfirmedTenantID int(11)
)
BEGIN

	DECLARE JSONTEXT text;
	if P_ConfirmedTenantID > 0 THEN
    
-- select "Tenant_Confirmed_Tenant_Save_Zessta";
    BEGIN
	/*	UPDATE Empirical_Tenants.ConfirmedTenants
        SET
			`TenantName` = P_TenantName,
			`CompanyName` = P_CompanyName,
			`AlternateCompanyName` =P_AlternateCompanyName,
			`Address1` = P_Address1,
			`Address2` = P_Address2,
			`AddressStreetNumber` = P_AddressStreetNumber,
			`AddressStreetName` = P_AddressStreetName,
			`CityID` = P_CityID,
			`StateID` = P_StateID,
			`ZipCode` = P_ZipCode,
			`CountyID` =P_CountyID,
			`CountryID` = P_CountryID,
			`AddressText` = P_AddressText,
			`ExtVendorID` = P_ExtVendorID,
			`ProviderID` = P_ProviderID,
            `MetroID`=P_MetroID,
			`ModifiedBy` = P_LoginEntityID,
			`ModifiedDate` =current_timestamp(),
            `OfficePhone` = P_OfficePhone,
            `Email` = P_Email,
            `WebsiteURL` = P_WebsiteURL,
             Fax =P_Fax,
             FloorNumber=P_FloorNumber,
             NationalID = P_NationalID,
             EmployeeCount = P_EmployeeCount,
             Revenue = P_Revenue,
             `ANZSICCode` = P_ANZSIC,
             CEOName=P_CEOName,
  			 CEOTitle=P_CEOTitle,
			 LineOfBusiness=P_LineOfBusiness,
			 SICCode=P_SICCode,
			 EmployeesAtLocation=P_EmployeesAtLocation,
			 LegalStatus=P_LegalStatus,
			 StatusCode=P_StatusCode,
			 SubsidiaryCode=P_SubsidiaryCode,
			 NAICSCode=P_NAICSCode,
			 NACECode=P_NACECode,
			 FloorNumber=P_FloorNumber,
			 PrimarySICDivisionDesc=P_PrimarySICDivisionDesc,
			 PrimarySIC2DigitDesc=P_PrimarySIC2DigitDesc,
			 PrimarySIC3DigitDesc=P_PrimarySIC3DigitDesc,
			 RegistrationOrIncorporationDate=P_RegistrationOrIncorporationDate,
			 RevenueIndicator=P_RevenueIndicator
        WHERE
			ConfirmedTenantID =P_ConfirmedTenantID;
		*/
        SET P_NewConfirmedTenantID = P_ConfirmedTenantID;
        
        -- temporary list to hold changed fiels for auditing
        DROP TEMPORARY TABLE IF EXISTS tempChangeLog;
        CREATE TEMPORARY TABLE tempChangeLog
        (
			ConfirmedTenantID INT,
            ProviderID INT,
            Source_Tenant_Record_ID INT,
            FieldID INT            	
        );
        
        -- Prepare Change Log fields by comparing with merged view fields. if merged view field is missing data and empirical does, then update
        -- merged view with empirical data and track change log.
		SET @ConfirmedTenantID=null,@TenantName=null,@Address1=null,@Address2=null,@CityID=null,@StateID=null,@ZipCode=null,
		    @CountryID=null,@OfficePhone=null,@Fax=null,@Email=null,@WebsiteURL=null,@FloorNumber=null,@NationalID=null,
			@EmployeeCount=null,@Revenue=null,@CEOName=null,@CEOTitle=null,@LineOfBusiness=null,@SICCode=null,@EmployeesAtLocation=null,
            @LegalStatus=null,@StatusCode=null,@SubsidiaryCode=null,@NAICSCode=null,@NACECode=null,@FloorNumber=null,@PrimarySICDivisionDesc=null,
            @PrimarySIC2DigitDesc=null,@PrimarySIC3DigitDesc=null,@RegistrationOrIncorporationDate=null,@RevenueIndicator=null;

        SELECT 
        ConfirmedTenantID,TenantName,Address1,Address2,CityID,StateID,ZipCode,CountryID,OfficePhone,Fax,Email,WebsiteURL,FloorNumber,NationalID,
        EmployeeCount,Revenue,CEOName,CEOTitle,LineOfBusiness,SICCode,EmployeesAtLocation,LegalStatus,StatusCode,SubsidiaryCode,NAICSCode,NACECode,
        FloorNumber,PrimarySICDivisionDesc,PrimarySIC2DigitDesc,PrimarySIC3DigitDesc,RegistrationOrIncorporationDate,RevenueIndicator
        INTO
        @ConfirmedTenantID,@TenantName,@Address1,@Address2,@CityID,@StateID,@ZipCode,@CountryID,@OfficePhone,@Fax,@Email,@WebsiteURL,@FloorNumber,
        @NationalID,@EmployeeCount,@Revenue,@CEOName,@CEOTitle,@LineOfBusiness,@SICCode,@EmployeesAtLocation,@LegalStatus,@StatusCode,@SubsidiaryCode,
        @NAICSCode,@NACECode,@FloorNumber,@PrimarySICDivisionDesc,@PrimarySIC2DigitDesc,@PrimarySIC3DigitDesc,@RegistrationOrIncorporationDate,
        @RevenueIndicator
        from Empirical_Tenants.ConfirmedTenants Where CompanyID=P_CompanyID and ProviderID=5;

        SELECT 
        TenantName,Address1,Address2,City,State,Country,OfficePhone,Fax,Email,WebsiteURL,EmployeeCount,Revenue,CEOName,CEOTitle,
        LineOfBusiness,SICCode,EmployeesAtLocation,LegalStatus,SubsidiaryCode,ABN,ACN ,
        PrimarySICDivisionDesc,PrimarySIC2DigitDesc,PrimarySIC3DigitDesc,RegistrationOrIncorporationDate,RevenueIndicator,ANZSICCode,PostalCode,StateAbbr
        INTO
		@ref_TenantName,@ref_Address1,@ref_Address2,@ref_CityID,@ref_StateID,@ref_CountryID,@ref_OfficePhone,@ref_Fax,@ref_Email,@ref_WebsiteURL,
        @ref_EmployeeCount,@ref_Revenue,@ref_CEOName,@ref_CEOTitle,@ref_LineOfBusiness,@ref_SICCode,@ref_EmployeesAtLocation,@ref_LegalStatus,
        @ref_SubsidiaryCode,@ref_ABN,@ref_ACN ,@ref_PrimarySICDivisionDesc,@ref_PrimarySIC2DigitDesc,@ref_PrimarySIC3DigitDesc,
        @ref_RegistrationOrIncorporationDate,@ref_RevenueIndicator,@ref_ANZSICCode,@ref_PostalCode,@ref_StateAbbr
        from Empirical_DataStage.VIC_Raw_Confirmed_Tenants_Fields_Reference Where VendorID=P_ExtVendorID;
    --     select @ConfirmedTenantID;
	-- 	set	@ConfirmedTenantID ="QWEWQ";
        IF @ConfirmedTenantID IS NOT NULL AND trim(IFNULL(P_MetroID,'')) <> '' THEN
            Update Empirical_Tenants.ConfirmedTenants Set MetroID=P_MetroID,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;

        IF @ConfirmedTenantID IS NOT NULL AND trim(IFNULL(P_CountyID,'')) <> '' THEN
            Update Empirical_Tenants.ConfirmedTenants Set CountyID=P_CountyID,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        
        
		 IF @ConfirmedTenantID IS NOT NULL AND trim(IFNULL(P_Address1,'')) <> '' THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,1,P_Tenant_Stage_ID,5; -- Provider 1 , Getting from Property
            Update Empirical_Tenants.ConfirmedTenants Set Address1=P_Address1,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        -- IF @ConfirmedTenantID IS NOT NULL AND trim(IFNULL(@Address2,'')) ='' and trim(IFNULL(P_Address2,'')) <> '' THEN
		IF P_MovingToOtherProperty=1 AND  @ConfirmedTenantID IS NOT NULL THEN
			-- INSERT INTO tempChangeLog
			-- SELECT @ConfirmedTenantID,@ref_Address2,P_Tenant_Stage_ID,6;
            Update Empirical_Tenants.ConfirmedTenants Set Address2=null,FloorNumber=null,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
       IF @ConfirmedTenantID IS NOT NULL AND trim(IFNULL(P_CityID,'')) <> ''  THEN -- Provider 1 , Getting from Property
			INSERT INTO tempChangeLog
    		SELECT @ConfirmedTenantID,1,P_Tenant_Stage_ID,7
            UNION
            SELECT @ConfirmedTenantID,1,P_Tenant_Stage_ID,8;                
            Update Empirical_Tenants.ConfirmedTenants Set CityID=P_CityID,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        IF @ConfirmedTenantID IS NOT NULL AND trim(IFNULL(P_StateID,'')) <> '' THEN -- Provider 1 , Getting from Property
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,1,P_Tenant_Stage_ID,9
            UNION
            SELECT @ConfirmedTenantID,1,P_Tenant_Stage_ID,10;
            Update Empirical_Tenants.ConfirmedTenants Set StateID=P_StateID,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
    	IF @ConfirmedTenantID IS NOT NULL AND trim(IFNULL(P_ZipCode,'')) <> ''  THEN -- Provider 1 , Getting from Property
    		INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,1,P_Tenant_Stage_ID,11;
            Update Empirical_Tenants.ConfirmedTenants Set ZipCode=P_ZipCode,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
    	IF @ConfirmedTenantID IS NOT NULL AND trim(IFNULL(P_CountryID,'')) <> ''   THEN -- Provider 1 , Getting from Property
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,1,P_Tenant_Stage_ID,12
            UNION
            SELECT @ConfirmedTenantID,1,P_Tenant_Stage_ID,13;
			Update Empirical_Tenants.ConfirmedTenants Set CountryID=P_CountryID,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_OfficePhone,'')) <> '' and @ref_OfficePhone is not null THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,@ref_OfficePhone,P_Tenant_Stage_ID,14;
            Update Empirical_Tenants.ConfirmedTenants Set OfficePhone=P_OfficePhone,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
         IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_Fax,'')) <> '' and @ref_Fax is not null THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,@ref_Fax,P_Tenant_Stage_ID,15;
            Update Empirical_Tenants.ConfirmedTenants Set Fax=P_Fax,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_Email,'')) <> '' and @ref_Email is not null THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,@ref_Email,P_Tenant_Stage_ID,28;
            Update Empirical_Tenants.ConfirmedTenants Set Email=P_Email,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_WebsiteURL,'')) <> '' and @ref_WebsiteURL is not null  THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,@ref_WebsiteURL,P_Tenant_Stage_ID,29;
            Update Empirical_Tenants.ConfirmedTenants Set WebsiteURL=P_WebsiteURL,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
 		IF @ConfirmedTenantID IS NOT NULL AND trim(IFNULL(P_FloorNumber,'')) <> '' and @ref_FloorNumber is not null THEN
			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_FloorNumber,P_Tenant_Stage_ID,30;
            Update Empirical_Tenants.ConfirmedTenants Set FloorNumber=P_FloorNumber,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
		IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_NationalID,'')) <> ''  and (@ref_ACN is not null or @ref_ABN is not null)THEN
			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,IFNULL(@ref_ACN,@ref_ABN),P_Tenant_Stage_ID,32;
             			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_ABN,P_Tenant_Stage_ID,44;
             			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_ACN,P_Tenant_Stage_ID,43;
            Update Empirical_Tenants.ConfirmedTenants Set NationalID=P_NationalID,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        IF @ConfirmedTenantID IS NOT NULL and trim(IFNULL(P_EmployeeCount,'')) <> '' and @ref_EmployeeCount is not null  THEN
			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_EmployeeCount,P_Tenant_Stage_ID,24;
            Update Empirical_Tenants.ConfirmedTenants Set EmployeeCount =P_EmployeeCount,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        IF @ConfirmedTenantID IS NOT NULL and trim(IFNULL(P_ExtVendorID,'')) <> '' THEN
			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,6,P_Tenant_Stage_ID,38;
            Update Empirical_Tenants.ConfirmedTenants Set ExtVendorID =P_ExtVendorID,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
		IF @ConfirmedTenantID IS NOT NULL and trim(IFNULL(P_Revenue,'')) <> '' and @ref_Revenue is not null THEN
			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_Revenue,P_Tenant_Stage_ID,22;
            Update Empirical_Tenants.ConfirmedTenants Set Revenue =P_Revenue,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
				IF @ConfirmedTenantID IS NOT NULL and trim(IFNULL(P_CEOName,'')) <> ''  and @ref_CEOName is not null  THEN
			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_CEOName,P_Tenant_Stage_ID,16;
            Update Empirical_Tenants.ConfirmedTenants Set CEOName =P_CEOName,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;  
		IF @ConfirmedTenantID IS NOT NULL and trim(IFNULL(P_CEOTitle,'')) <> ''  and @ref_CEOTitle is not null THEN
			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_CEOTitle,P_Tenant_Stage_ID,17;
            Update Empirical_Tenants.ConfirmedTenants Set CEOTitle =P_CEOTitle,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;  		
        IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_LineOfBusiness,'')) <> ''  and @ref_LineOfBusiness is not null THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,@ref_LineOfBusiness,P_Tenant_Stage_ID,18;
            Update Empirical_Tenants.ConfirmedTenants Set LineOfBusiness=P_LineOfBusiness,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
         IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_SICCode,'')) <> '' and @ref_SICCode is not null THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,@ref_SICCode,P_Tenant_Stage_ID,19;
            Update Empirical_Tenants.ConfirmedTenants Set SICCode=P_SICCode,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_EmployeesAtLocation,'')) <> ''  and @ref_EmployeesAtLocation is not null  THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,@ref_EmployeesAtLocation,P_Tenant_Stage_ID,23;
            Update Empirical_Tenants.ConfirmedTenants Set EmployeesAtLocation=P_EmployeesAtLocation,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_LegalStatus,'')) <> ''   and @ref_LegalStatus is not null   THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,@ref_LegalStatus,P_Tenant_Stage_ID,25;
            Update Empirical_Tenants.ConfirmedTenants Set LegalStatus=P_LegalStatus,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        -- IF @ConfirmedTenantID IS NOT NULL AND trim(IFNULL(@StatusCode,'')) ='' and trim(IFNULL(P_StatusCode,'')) <> '' THEN
		IF @ConfirmedTenantID IS NOT NULL AND trim(IFNULL(P_StatusCode,'')) <> ''   and @ref_StatusCode is not null    THEN
			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_StatusCode,P_Tenant_Stage_ID,26;
            Update Empirical_Tenants.ConfirmedTenants Set StatusCode=P_StatusCode,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
		IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_SubsidiaryCode,'')) <> ''  and @ref_SubsidiaryCode is not null  THEN
			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_SubsidiaryCode,P_Tenant_Stage_ID,27;
            Update Empirical_Tenants.ConfirmedTenants Set SubsidiaryCode=P_SubsidiaryCode,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        IF @ConfirmedTenantID IS NOT NULL and trim(IFNULL(P_NAICSCode,'')) <> ''  and @ref_NAICSCode is not null  THEN
			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_NAICSCode,P_Tenant_Stage_ID,20;
            Update Empirical_Tenants.ConfirmedTenants Set NAICSCode =P_NAICSCode,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
		IF @ConfirmedTenantID IS NOT NULL and trim(IFNULL(P_NACECode,'')) <> ''  and @ref_NACECode is not null   THEN
			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_NACECode,P_Tenant_Stage_ID,21;
            Update Empirical_Tenants.ConfirmedTenants Set NACECode =P_NACECode,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_PrimarySICDivisionDesc,'')) <> ''  and @ref_PrimarySICDivisionDesc is not null   THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,@ref_PrimarySICDivisionDesc,P_Tenant_Stage_ID,39;
            Update Empirical_Tenants.ConfirmedTenants Set PrimarySICDivisionDesc=P_PrimarySICDivisionDesc,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
         IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_PrimarySIC2DigitDesc,'')) <> ''  and @ref_PrimarySIC2DigitDesc is not null  THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,@ref_PrimarySIC2DigitDesc,P_Tenant_Stage_ID,40;
            Update Empirical_Tenants.ConfirmedTenants Set PrimarySIC2DigitDesc=P_PrimarySIC2DigitDesc,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_PrimarySIC3DigitDesc,'')) <> ''   and @ref_PrimarySIC3DigitDesc is not null  THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,@ref_PrimarySIC3DigitDesc,P_Tenant_Stage_ID,41;
            Update Empirical_Tenants.ConfirmedTenants Set PrimarySIC3DigitDesc=P_PrimarySIC3DigitDesc,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        IF @ConfirmedTenantID IS NOT NULL  and trim(IFNULL(P_RegistrationOrIncorporationDate,'')) <> ''  and @ref_RegistrationOrIncorporationDate is not null THEN
			INSERT INTO tempChangeLog
			SELECT @ConfirmedTenantID,@ref_RegistrationOrIncorporationDate,P_Tenant_Stage_ID,42;
            Update Empirical_Tenants.ConfirmedTenants Set RegistrationOrIncorporationDate=P_RegistrationOrIncorporationDate,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
        
		IF @ConfirmedTenantID IS NOT NULL AND trim(IFNULL(P_RevenueIndicator,'')) <> ''  and @ref_RevenueIndicator is not null THEN
			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_RevenueIndicator,P_Tenant_Stage_ID,49;
            Update Empirical_Tenants.ConfirmedTenants Set RevenueIndicator=P_RevenueIndicator,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
		IF @ConfirmedTenantID IS NOT NULL AND trim(IFNULL(P_ANZSIC,'')) <> ''  and @ref_ANZSICCode is not null THEN
			 INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_ANZSICCode,P_Tenant_Stage_ID,45;
              INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_ANZSICCode,P_Tenant_Stage_ID,46;
             INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_ANZSICCode,P_Tenant_Stage_ID,47;
              INSERT INTO tempChangeLog
			 SELECT @ConfirmedTenantID,@ref_ANZSICCode,P_Tenant_Stage_ID,48;
            Update Empirical_Tenants.ConfirmedTenants Set ANZSICCode=P_ANZSIC,ModifiedDate=current_timestamp(),ModifiedBy=P_LoginEntityID Where ConfirmedTenantID=@ConfirmedTenantID;
        END IF;
		
		-- Prepare JSON object from change log to pass it to the change log save SP        

			SELECT 
				CONCAT('[', FirstPass, ']') INTO JSONTEXT
			FROM
				(SELECT 
					GROUP_CONCAT('{', Jason1, '}'
							SEPARATOR ',') AS FirstPass
				FROM
					(
						SELECT 
						CONCAT('"ConfirmedTenantID":', '"', CL.ConfirmedTenantID, '"', ',',
								'"ProviderID":', '"', CL.ProviderID, '"', ',',
                                '"Source_Tenant_Record_ID":', '"', CL.Source_Tenant_Record_ID, '"', ',',
                                '"FieldID":', '"', CL.FieldID, '"'
							   ) AS Jason1
				FROM
					tempChangeLog CL) AS Jason2) AS Jason3;
                  --  select JSONTEXT;
		if JSONTEXT IS NOT NULL THEN
		 --  save change log
		  CALL CRE_Property_Tenant_Matrix_Save_MergedView_Changelog_1(JSONTEXT,P_LoginEntityID);
		end if;
        
		Call CRE_NationalIdentifiers_Save(P_CompanyID,P_Tenant_Stage_ID,@ConfirmedTenantID,P_ACN,P_ABN);
    END;
    ELSE
		BEGIN
			INSERT INTO Empirical_Tenants.ConfirmedTenants
            (
				`TenantName`,
				`CompanyName`,
				`AlternateCompanyName`,
				`Address1`,
				`Address2`,
				`AddressStreetNumber`,
				`AddressStreetName`,
				`CityID`,
				`StateID`,
				`ZipCode`,
				`CountyID`,
				`CountryID`,
				`AddressText`,
				`ExtVendorID`,
				`ProviderID`,
                `MetroID`,
				`CreatedBy`,
				`ModifiedBy`,
				`ModifiedDate`,
                `OfficePhone`,
                `CompanyID`,
                `Email`,
                `WebsiteURL`,
                Fax,
                FloorNumber,
                NationalID,
                EmployeeCount,
				Revenue,
                `ANZSICCode`,
                CEOName,
				CEOTitle,
				LineOfBusiness,
				SICCode,
				EmployeesAtLocation,
				LegalStatus,
				StatusCode,
				SubsidiaryCode,
				NAICSCode,
				NACECode,

				PrimarySICDivisionDesc,
				PrimarySIC2DigitDesc,
				PrimarySIC3DigitDesc,
				RegistrationOrIncorporationDate,
				RevenueIndicator
			)
            VALUES
            (
				P_TenantName,
				P_CompanyName,
				P_AlternateCompanyName,
				P_Address1,
				P_Address2,
				P_AddressStreetNumber,
				P_AddressStreetName,
				P_CityID,
				P_StateID,
				P_ZipCode,
				P_CountyID,
				P_CountryID,
				P_AddressText,
				P_ExtVendorID,
				P_ProviderID,
                P_MetroID,
				P_LoginEntityID,
                P_LoginEntityID,
				current_timestamp(),
                P_OfficePhone,
                P_CompanyID,
                P_Email,
                P_WebsiteURL,
                P_Fax,
                P_FloorNumber,
                P_NationalID,
                P_EmployeeCount,
				P_Revenue,
                P_ANZSIC,
				P_CEOName,
				P_CEOTitle,
				P_LineOfBusiness,
				P_SICCode,
				P_EmployeesAtLocation,
				P_LegalStatus,
				P_StatusCode,
				P_SubsidiaryCode,
				P_NAICSCode,
				P_NACECode,
				P_PrimarySICDivisionDesc,
				P_PrimarySIC2DigitDesc,
				P_PrimarySIC3DigitDesc,
				P_RegistrationOrIncorporationDate,
				P_RevenueIndicator
            );
            
            SET P_NewConfirmedTenantID = LAST_INSERT_ID() ;            
            
        END;
			
			select SuiteID into @SuiteID from Empirical_Prod.Suite where PropertyID=P_PropertyID limit 1;
			CALL CRE_SuiteTenant_Save_Zessta(P_NewConfirmedTenantID,P_PropertyID,P_EntityID,null,P_ExtVendorID,P_Tenant_Stage_ID);
            
            SELECT ConfirmedTenantID INTO @ConfirmedTenantID from Empirical_Tenants.ConfirmedTenants Where CompanyID=P_CompanyID and ProviderID=5;
            
			Call CRE_NationalIdentifiers_Save(P_CompanyID,P_Tenant_Stage_ID,@ConfirmedTenantID,P_ACN,P_ABN);

    END IF;
    
   
END