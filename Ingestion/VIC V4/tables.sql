-- Table structure for table `Tenant_Ingestion_Data`
--

DROP TABLE IF EXISTS `NSW_Raw_Confirmed_Tenants`;
CREATE TABLE `NSW_Raw_Confirmed_Tenants` (
  `ABN` bigint DEFAULT NULL,
  `ABNStatus` text,
  `ABN_StatusFromDate` text,
  `ACN` double DEFAULT NULL,
  `ANZ<PERSON>CCode` double DEFAULT NULL,
  `ASICEntityClass` text,
  `ASICEntityStatus` text,
  `ASICEntityType` text,
  `Address1` text,
  `Address2` text,
  `CEOName` text,
  `CEOTitle` text,
  `City` double DEFAULT NULL,
  `Country` double DEFAULT NULL,
  `DomesticParentCountry` text,
  `DomesticParentDUNS` double DEFAULT NULL,
  `DomesticParentName` text,
  `Email` text,
  `EmployeeCount` double DEFAULT NULL,
  `EmployeeIndicator` text,
  `EmployeesAtLocation` double DEFAULT NULL,
  `EntityAge` double DEFAULT NULL,
  `Fax` double DEFAULT NULL,
  `GST_Status` text,
  `GST_StatusFromDate` text,
  `GlobalUltimateParentCountry` text,
  `GlobalUltimateParentDUNS` double DEFAULT NULL,
  `GlobalUltimateParentName` text,
  `HQ_CompanyName` double DEFAULT NULL,
  `HQ_ID` double DEFAULT NULL,
  `ImmediateParentCountry` text,
  `ImmediateParentDUNS` double DEFAULT NULL,
  `ImmediateParentName` text,
  `Latitude` double DEFAULT NULL,
  `LegalStatus` text,
  `LineOfBusiness` text,
  `Longitude` double DEFAULT NULL,
  `MarketableFlag` bigint DEFAULT NULL,
  `NumberofMembersinHierarchy` double DEFAULT NULL,
  `OfficePhone` double DEFAULT NULL,
  `PostalCode` double DEFAULT NULL,
  `PrimarySIC2Digit` double DEFAULT NULL,
  `PrimarySIC2DigitDesc` text,
  `PrimarySIC3Digit` double DEFAULT NULL,
  `PrimarySIC3DigitDesc` text,
  `PrimarySICDesc` text,
  `PrimarySICDivision` text,
  `PrimarySICDivisionDesc` text,
  `PropertyID` bigint DEFAULT NULL,
  `RegistrationOrIncorporationDate` text,
  `Revenue` double DEFAULT NULL,
  `RevenueIndicator` text,
  `SICCode` double DEFAULT NULL,
  `State` double DEFAULT NULL,
  `StateAbbr` text,
  `SubsidiaryCode` double DEFAULT NULL,
  `TenantName` text,
  `VendorID` bigint DEFAULT NULL,
  `WebsiteURL` text,
  `similarity_score_google_maps` double DEFAULT NULL,
  `similarity_score_overture` double DEFAULT NULL,
  `similarity_score_zoominfo` double DEFAULT NULL,
  `BatchID` int DEFAULT NULL,
  `CountryCode` int DEFAULT NULL,
  `ProviderID` int DEFAULT NULL,
  `NationalID` varchar(500) DEFAULT NULL,
  `NSW_Raw_Confirmed_TenantsID` int NOT NULL AUTO_INCREMENT,
  `IsProcessed` int DEFAULT NULL,
  `Tenant_Stage_ID` int DEFAULT NULL,
  PRIMARY KEY (`NSW_Raw_Confirmed_TenantsID`),
  KEY `idx_Tenant_Stage_ID` (`Tenant_Stage_ID`) /*!80000 INVISIBLE */,
  KEY `idx_VendorID` (`VendorID`)
) ENGINE=InnoDB AUTO_INCREMENT=87952 DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `NSW_Raw_Confirmed_Tenants_Fields_Reference`;
CREATE TABLE `NSW_Raw_Confirmed_Tenants_Fields_Reference` (
  `ABN` bigint DEFAULT NULL,
  `ABNStatus` bigint DEFAULT NULL,
  `ABN_StatusFromDate` bigint DEFAULT NULL,
  `ACN` double DEFAULT NULL,
  `ANZSICCode` double DEFAULT NULL,
  `ASICEntityClass` double DEFAULT NULL,
  `ASICEntityStatus` double DEFAULT NULL,
  `ASICEntityType` double DEFAULT NULL,
  `Address1` double DEFAULT NULL,
  `Address2` bigint DEFAULT NULL,
  `CEOName` bigint DEFAULT NULL,
  `CEOTitle` double DEFAULT NULL,
  `City` double DEFAULT NULL,
  `Country` double DEFAULT NULL,
  `DomesticParentCountry` double DEFAULT NULL,
  `DomesticParentDUNS` double DEFAULT NULL,
  `DomesticParentName` double DEFAULT NULL,
  `Email` double DEFAULT NULL,
  `EmployeeCount` double DEFAULT NULL,
  `EmployeeIndicator` double DEFAULT NULL,
  `EmployeesAtLocation` double DEFAULT NULL,
  `EntityAge` double DEFAULT NULL,
  `Fax` double DEFAULT NULL,
  `GST_Status` double DEFAULT NULL,
  `GST_StatusFromDate` double DEFAULT NULL,
  `GlobalUltimateParentCountry` double DEFAULT NULL,
  `GlobalUltimateParentDUNS` double DEFAULT NULL,
  `GlobalUltimateParentName` double DEFAULT NULL,
  `HQ_CompanyName` double DEFAULT NULL,
  `HQ_ID` double DEFAULT NULL,
  `ImmediateParentCountry` double DEFAULT NULL,
  `ImmediateParentDUNS` double DEFAULT NULL,
  `ImmediateParentName` double DEFAULT NULL,
  `Latitude` double DEFAULT NULL,
  `LegalStatus` bigint DEFAULT NULL,
  `LineOfBusiness` double DEFAULT NULL,
  `Longitude` double DEFAULT NULL,
  `MarketableFlag` bigint DEFAULT NULL,
  `NumberofMembersinHierarchy` double DEFAULT NULL,
  `OfficePhone` double DEFAULT NULL,
  `PostalCode` double DEFAULT NULL,
  `PrimarySIC2Digit` double DEFAULT NULL,
  `PrimarySIC2DigitDesc` double DEFAULT NULL,
  `PrimarySIC3Digit` double DEFAULT NULL,
  `PrimarySIC3DigitDesc` double DEFAULT NULL,
  `PrimarySICDesc` double DEFAULT NULL,
  `PrimarySICDivision` double DEFAULT NULL,
  `PrimarySICDivisionDesc` double DEFAULT NULL,
  `PropertyID` bigint DEFAULT NULL,
  `RegistrationOrIncorporationDate` double DEFAULT NULL,
  `Revenue` double DEFAULT NULL,
  `RevenueIndicator` double DEFAULT NULL,
  `SICCode` double DEFAULT NULL,
  `State` double DEFAULT NULL,
  `StateAbbr` bigint DEFAULT NULL,
  `SubsidiaryCode` double DEFAULT NULL,
  `TenantName` bigint DEFAULT NULL,
  `VendorID` bigint DEFAULT NULL,
  `WebsiteURL` double DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `NSW_Raw_GoogleMaps_Tenants`;
CREATE TABLE `NSW_Raw_GoogleMaps_Tenants` (
  `ProviderName` text,
  `ProviderID` bigint DEFAULT NULL,
  `match_PropertyID` bigint DEFAULT NULL,
  `main_ID` bigint DEFAULT NULL,
  `main_PropertyID` bigint DEFAULT NULL,
  `scenario` bigint DEFAULT NULL,
  `match_count` bigint DEFAULT NULL,
  `ABN` double DEFAULT NULL,
  `ABNStatus` double DEFAULT NULL,
  `ABN_StatusFromDate` double DEFAULT NULL,
  `ACN` double DEFAULT NULL,
  `ANZSICCode` double DEFAULT NULL,
  `ASICEntityClass` double DEFAULT NULL,
  `ASICEntityStatus` double DEFAULT NULL,
  `ASICEntityType` double DEFAULT NULL,
  `Address1` double DEFAULT NULL,
  `Address2` text,
  `CEOName` double DEFAULT NULL,
  `CEOTitle` double DEFAULT NULL,
  `City` double DEFAULT NULL,
  `Country` double DEFAULT NULL,
  `DomesticParentCountry` double DEFAULT NULL,
  `DomesticParentDUNS` double DEFAULT NULL,
  `DomesticParentName` double DEFAULT NULL,
  `Email` double DEFAULT NULL,
  `EmployeeCount` double DEFAULT NULL,
  `EmployeeIndicator` double DEFAULT NULL,
  `EmployeesAtLocation` double DEFAULT NULL,
  `EntityAge` double DEFAULT NULL,
  `Fax` double DEFAULT NULL,
  `GST_Status` double DEFAULT NULL,
  `GST_StatusFromDate` double DEFAULT NULL,
  `GlobalUltimateParentCountry` double DEFAULT NULL,
  `GlobalUltimateParentDUNS` double DEFAULT NULL,
  `GlobalUltimateParentName` double DEFAULT NULL,
  `HQ_CompanyName` double DEFAULT NULL,
  `HQ_ID` double DEFAULT NULL,
  `ImmediateParentCountry` double DEFAULT NULL,
  `ImmediateParentDUNS` double DEFAULT NULL,
  `ImmediateParentName` double DEFAULT NULL,
  `Latitude` double DEFAULT NULL,
  `LegalStatus` double DEFAULT NULL,
  `LineOfBusiness` double DEFAULT NULL,
  `Longitude` double DEFAULT NULL,
  `MarketableFlag` double DEFAULT NULL,
  `NumberofMembersinHierarchy` double DEFAULT NULL,
  `OfficePhone` double DEFAULT NULL,
  `PostalCode` double DEFAULT NULL,
  `PrimarySIC2Digit` double DEFAULT NULL,
  `PrimarySIC2DigitDesc` double DEFAULT NULL,
  `PrimarySIC3Digit` double DEFAULT NULL,
  `PrimarySIC3DigitDesc` double DEFAULT NULL,
  `PrimarySICDesc` double DEFAULT NULL,
  `PrimarySICDivision` double DEFAULT NULL,
  `PrimarySICDivisionDesc` double DEFAULT NULL,
  `PropertyID` double DEFAULT NULL,
  `RegistrationOrIncorporationDate` double DEFAULT NULL,
  `Revenue` double DEFAULT NULL,
  `RevenueIndicator` double DEFAULT NULL,
  `SICCode` double DEFAULT NULL,
  `State` double DEFAULT NULL,
  `StateAbbr` double DEFAULT NULL,
  `SubsidiaryCode` double DEFAULT NULL,
  `TenantName` text,
  `TradingNames` double DEFAULT NULL,
  `VendorID` double DEFAULT NULL,
  `WebsiteURL` text,
  `match_ID` double DEFAULT NULL,
  `main_name` text,
  `similarity_score` double DEFAULT NULL,
  `match_name` text
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `NSW_Raw_Illion_Tenants`;
CREATE TABLE `NSW_Raw_Illion_Tenants` (
  `ProviderName` text,
  `ProviderID` bigint DEFAULT NULL,
  `match_PropertyID` bigint DEFAULT NULL,
  `main_ID` bigint DEFAULT NULL,
  `main_PropertyID` bigint DEFAULT NULL,
  `scenario` bigint DEFAULT NULL,
  `match_count` bigint DEFAULT NULL,
  `ABN` bigint DEFAULT NULL,
  `ABNStatus` text,
  `ABN_StatusFromDate` text,
  `ACN` double DEFAULT NULL,
  `ANZSICCode` double DEFAULT NULL,
  `ASICEntityClass` text,
  `ASICEntityStatus` text,
  `ASICEntityType` text,
  `Address1` text,
  `Address2` text,
  `CEOName` text,
  `CEOTitle` text,
  `City` text,
  `Country` double DEFAULT NULL,
  `DomesticParentCountry` text,
  `DomesticParentDUNS` double DEFAULT NULL,
  `DomesticParentName` text,
  `Email` text,
  `EmployeeCount` double DEFAULT NULL,
  `EmployeeIndicator` text,
  `EmployeesAtLocation` double DEFAULT NULL,
  `EntityAge` double DEFAULT NULL,
  `Fax` double DEFAULT NULL,
  `GST_Status` text,
  `GST_StatusFromDate` text,
  `GlobalUltimateParentCountry` text,
  `GlobalUltimateParentDUNS` double DEFAULT NULL,
  `GlobalUltimateParentName` text,
  `HQ_CompanyName` double DEFAULT NULL,
  `HQ_ID` double DEFAULT NULL,
  `ImmediateParentCountry` text,
  `ImmediateParentDUNS` double DEFAULT NULL,
  `ImmediateParentName` text,
  `Latitude` double DEFAULT NULL,
  `LegalStatus` text,
  `LineOfBusiness` text,
  `Longitude` double DEFAULT NULL,
  `MarketableFlag` bigint DEFAULT NULL,
  `NumberofMembersinHierarchy` double DEFAULT NULL,
  `OfficePhone` double DEFAULT NULL,
  `PostalCode` double DEFAULT NULL,
  `PrimarySIC2Digit` double DEFAULT NULL,
  `PrimarySIC2DigitDesc` text,
  `PrimarySIC3Digit` double DEFAULT NULL,
  `PrimarySIC3DigitDesc` text,
  `PrimarySICDesc` text,
  `PrimarySICDivision` text,
  `PrimarySICDivisionDesc` text,
  `PropertyID` bigint DEFAULT NULL,
  `RegistrationOrIncorporationDate` text,
  `Revenue` double DEFAULT NULL,
  `RevenueIndicator` text,
  `SICCode` double DEFAULT NULL,
  `State` double DEFAULT NULL,
  `StateAbbr` text,
  `SubsidiaryCode` double DEFAULT NULL,
  `TenantName` text,
  `TradingNames` text,
  `VendorID` bigint DEFAULT NULL,
  `WebsiteURL` text,
  `match_ID` double DEFAULT NULL,
  `main_name` double DEFAULT NULL,
  `similarity_score` double DEFAULT NULL,
  `match_name` double DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `NSW_Raw_Overture_Tenants`;
CREATE TABLE `NSW_Raw_Overture_Tenants` (
  `ProviderName` text,
  `ProviderID` bigint DEFAULT NULL,
  `match_PropertyID` bigint DEFAULT NULL,
  `main_ID` bigint DEFAULT NULL,
  `main_PropertyID` bigint DEFAULT NULL,
  `scenario` bigint DEFAULT NULL,
  `match_count` bigint DEFAULT NULL,
  `ABN` double DEFAULT NULL,
  `ABNStatus` double DEFAULT NULL,
  `ABN_StatusFromDate` double DEFAULT NULL,
  `ACN` double DEFAULT NULL,
  `ANZSICCode` double DEFAULT NULL,
  `ASICEntityClass` double DEFAULT NULL,
  `ASICEntityStatus` double DEFAULT NULL,
  `ASICEntityType` double DEFAULT NULL,
  `Address1` double DEFAULT NULL,
  `Address2` text,
  `CEOName` double DEFAULT NULL,
  `CEOTitle` double DEFAULT NULL,
  `City` text,
  `Country` text,
  `DomesticParentCountry` double DEFAULT NULL,
  `DomesticParentDUNS` double DEFAULT NULL,
  `DomesticParentName` double DEFAULT NULL,
  `Email` double DEFAULT NULL,
  `EmployeeCount` double DEFAULT NULL,
  `EmployeeIndicator` double DEFAULT NULL,
  `EmployeesAtLocation` double DEFAULT NULL,
  `EntityAge` double DEFAULT NULL,
  `Fax` double DEFAULT NULL,
  `GST_Status` double DEFAULT NULL,
  `GST_StatusFromDate` double DEFAULT NULL,
  `GlobalUltimateParentCountry` double DEFAULT NULL,
  `GlobalUltimateParentDUNS` double DEFAULT NULL,
  `GlobalUltimateParentName` double DEFAULT NULL,
  `HQ_CompanyName` double DEFAULT NULL,
  `HQ_ID` double DEFAULT NULL,
  `ImmediateParentCountry` double DEFAULT NULL,
  `ImmediateParentDUNS` double DEFAULT NULL,
  `ImmediateParentName` double DEFAULT NULL,
  `Latitude` double DEFAULT NULL,
  `LegalStatus` double DEFAULT NULL,
  `LineOfBusiness` double DEFAULT NULL,
  `Longitude` double DEFAULT NULL,
  `MarketableFlag` double DEFAULT NULL,
  `NumberofMembersinHierarchy` double DEFAULT NULL,
  `OfficePhone` double DEFAULT NULL,
  `PostalCode` double DEFAULT NULL,
  `PrimarySIC2Digit` double DEFAULT NULL,
  `PrimarySIC2DigitDesc` double DEFAULT NULL,
  `PrimarySIC3Digit` double DEFAULT NULL,
  `PrimarySIC3DigitDesc` double DEFAULT NULL,
  `PrimarySICDesc` double DEFAULT NULL,
  `PrimarySICDivision` double DEFAULT NULL,
  `PrimarySICDivisionDesc` double DEFAULT NULL,
  `PropertyID` double DEFAULT NULL,
  `RegistrationOrIncorporationDate` double DEFAULT NULL,
  `Revenue` double DEFAULT NULL,
  `RevenueIndicator` double DEFAULT NULL,
  `SICCode` double DEFAULT NULL,
  `State` text,
  `StateAbbr` double DEFAULT NULL,
  `SubsidiaryCode` double DEFAULT NULL,
  `TenantName` text,
  `TradingNames` double DEFAULT NULL,
  `VendorID` text,
  `WebsiteURL` text,
  `match_ID` text,
  `main_name` text,
  `similarity_score` double DEFAULT NULL,
  `match_name` text
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `NSW_Raw_ZoomInfo_Tenants`;
CREATE TABLE `NSW_Raw_ZoomInfo_Tenants` (
  `ProviderName` text,
  `ProviderID` bigint DEFAULT NULL,
  `match_PropertyID` bigint DEFAULT NULL,
  `main_ID` bigint DEFAULT NULL,
  `main_PropertyID` bigint DEFAULT NULL,
  `scenario` bigint DEFAULT NULL,
  `match_count` bigint DEFAULT NULL,
  `ABN` double DEFAULT NULL,
  `ABNStatus` double DEFAULT NULL,
  `ABN_StatusFromDate` double DEFAULT NULL,
  `ACN` double DEFAULT NULL,
  `ANZSICCode` double DEFAULT NULL,
  `ASICEntityClass` double DEFAULT NULL,
  `ASICEntityStatus` double DEFAULT NULL,
  `ASICEntityType` double DEFAULT NULL,
  `Address1` text,
  `Address2` text,
  `CEOName` double DEFAULT NULL,
  `CEOTitle` double DEFAULT NULL,
  `City` text,
  `Country` double DEFAULT NULL,
  `DomesticParentCountry` double DEFAULT NULL,
  `DomesticParentDUNS` double DEFAULT NULL,
  `DomesticParentName` double DEFAULT NULL,
  `Email` double DEFAULT NULL,
  `EmployeeCount` double DEFAULT NULL,
  `EmployeeIndicator` double DEFAULT NULL,
  `EmployeesAtLocation` double DEFAULT NULL,
  `EntityAge` double DEFAULT NULL,
  `Fax` double DEFAULT NULL,
  `GST_Status` double DEFAULT NULL,
  `GST_StatusFromDate` double DEFAULT NULL,
  `GlobalUltimateParentCountry` double DEFAULT NULL,
  `GlobalUltimateParentDUNS` double DEFAULT NULL,
  `GlobalUltimateParentName` double DEFAULT NULL,
  `HQ_CompanyName` double DEFAULT NULL,
  `HQ_ID` double DEFAULT NULL,
  `ImmediateParentCountry` double DEFAULT NULL,
  `ImmediateParentDUNS` double DEFAULT NULL,
  `ImmediateParentName` double DEFAULT NULL,
  `Latitude` double DEFAULT NULL,
  `LegalStatus` double DEFAULT NULL,
  `LineOfBusiness` double DEFAULT NULL,
  `Longitude` double DEFAULT NULL,
  `MarketableFlag` double DEFAULT NULL,
  `NumberofMembersinHierarchy` double DEFAULT NULL,
  `OfficePhone` double DEFAULT NULL,
  `PostalCode` text,
  `PrimarySIC2Digit` double DEFAULT NULL,
  `PrimarySIC2DigitDesc` double DEFAULT NULL,
  `PrimarySIC3Digit` double DEFAULT NULL,
  `PrimarySIC3DigitDesc` double DEFAULT NULL,
  `PrimarySICDesc` double DEFAULT NULL,
  `PrimarySICDivision` double DEFAULT NULL,
  `PrimarySICDivisionDesc` double DEFAULT NULL,
  `PropertyID` double DEFAULT NULL,
  `RegistrationOrIncorporationDate` double DEFAULT NULL,
  `Revenue` double DEFAULT NULL,
  `RevenueIndicator` double DEFAULT NULL,
  `SICCode` double DEFAULT NULL,
  `State` text,
  `StateAbbr` double DEFAULT NULL,
  `SubsidiaryCode` double DEFAULT NULL,
  `TenantName` text,
  `TradingNames` text,
  `VendorID` double DEFAULT NULL,
  `WebsiteURL` text,
  `match_ID` double DEFAULT NULL,
  `main_name` text,
  `similarity_score` double DEFAULT NULL,
  `match_name` text,
  `DisplayName` text,
  `CompanyID` double DEFAULT NULL,
  `ZI_C_LAST_UPDATED_DATE` text,
  `ZI_C_RELEASE_DATE` double DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
