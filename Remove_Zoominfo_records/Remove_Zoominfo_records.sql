select * from Empirical_DataStage.Tenants_Stage where ProviderID=15 and <PERSON><PERSON> is null; -- 1186311

delete from Empirical_DataStage.Tenants_Stage where ProviderID=15 and BranchID is null; -- 1186311

drop temporary table if exists confirmedTenantsWithZoominfo;
create temporary table confirmedTenantsWithZoominfo as(
select st.PropertyID,CTFA.ConfirmedTenantFieldAuditID,CTFA.FieldID,AF.FieldName,CT.* from Empirical_Tenants.ConfirmedTenantsFieldAudit CTFA
inner join Empirical_Tenants.AuditFields AF on CTFA.FieldID=AF.FieldID 
inner join Empirical_Tenants.ConfirmedTenants CT on CT.ConfirmedTenantID=CTFA.ConfirmedTenantID
inner join Empirical_Tenants.ConfirmedTenants CTE on CTE.CompanyID=CT.CompanyID and CTE.ProviderID=1
left join Empirical_Prod.SuiteTenant st on st.ConfirmedTenantID=CTE.ConfirmedTenantID
where CTFA.ProviderID=15 -- and CTFA.IsActive=1 and st.IsActive=1 -- and st.PropertyID=269652
); -- 493660
-- select * from confirmedTenantsWithZoominfo;

drop temporary table if exists TenantsStageRecordsWithZoominfo;
create temporary table TenantsStageRecordsWithZoominfo as(
select PropertyID,TenantName from Empirical_DataStage.Tenants_Stage where ProviderID=15 and BranchID is not null -- and PropertyID=269652
); -- 154834

drop table if exists Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields;
create table Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
select *,0 as IsProcessed from confirmedTenantsWithZoominfo;

drop table if exists Empirical_DataFixes.TenantsStageRecordsWithZoominfo;
create table Empirical_DataFixes.TenantsStageRecordsWithZoominfo
select * from TenantsStageRecordsWithZoominfo;

select * from Empirical_Tenants.AuditFields;

select FieldID,FieldName,count(*) from Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields group by FieldID,FieldName;

-- WebsiteUrl (FieldID: 29)
UPDATE Empirical_Tenants.ConfirmedTenants
SET WebsiteUrl = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 29
);

-- OfficePhone (FieldID: 14)
UPDATE Empirical_Tenants.ConfirmedTenants
SET OfficePhone = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 14
);

-- Fax (FieldID: 15)
UPDATE Empirical_Tenants.ConfirmedTenants
SET Fax = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 15
);

-- Revenue (FieldID: 22)
UPDATE Empirical_Tenants.ConfirmedTenants
SET Revenue = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 22
);

-- Address1 (FieldID: 5)
UPDATE Empirical_Tenants.ConfirmedTenants
SET Address1 = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 5
);

-- Address2 (FieldID: 6)
UPDATE Empirical_Tenants.ConfirmedTenants
SET Address2 = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 6
);

-- EmployeeCount (FieldID: 24)
UPDATE Empirical_Tenants.ConfirmedTenants
SET EmployeeCount = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 24
);

-- SICCode (FieldID: 19)
UPDATE Empirical_Tenants.ConfirmedTenants
SET SICCode = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 19
);

-- NAICSCode (FieldID: 20)
UPDATE Empirical_Tenants.ConfirmedTenants
SET NAICSCode = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 20
);

-- NACECode (FieldID: 21)
UPDATE Empirical_Tenants.ConfirmedTenants
SET NACECode = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 21
);

-- ExtVendorID (FieldID: 38)
UPDATE Empirical_Tenants.ConfirmedTenants
SET ExtVendorID = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 38
);

-- CEOName (FieldID: 16)
UPDATE Empirical_Tenants.ConfirmedTenants
SET CEOName = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 16
);

-- CEOTitle (FieldID: 17)
UPDATE Empirical_Tenants.ConfirmedTenants
SET CEOTitle = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 17
);

-- LineOfBusiness (FieldID: 18)
UPDATE Empirical_Tenants.ConfirmedTenants
SET LineOfBusiness = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 18
);

-- RegistrationOrIncorporationDate (FieldID: 42)
UPDATE Empirical_Tenants.ConfirmedTenants
SET RegistrationOrIncorporationDate = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 42
);

-- EmployeesAtLocation (FieldID: 23)
UPDATE Empirical_Tenants.ConfirmedTenants
SET EmployeesAtLocation = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 23
);

-- SubsidiaryCode (FieldID: 27)
UPDATE Empirical_Tenants.ConfirmedTenants
SET SubsidiaryCode = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 27
);

-- ANZSICCode (FieldID: 45)
UPDATE Empirical_Tenants.ConfirmedTenants
SET ANZSICCode = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 45
);

-- TenantName (FieldID: 2)
UPDATE Empirical_Tenants.ConfirmedTenants
SET TenantName = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 2
);

-- NationalID (FieldID: 32)
UPDATE Empirical_Tenants.ConfirmedTenants
SET NationalID = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 32
);

-- FloorNumber (FieldID: 30)
UPDATE Empirical_Tenants.ConfirmedTenants
SET FloorNumber = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 30
);

-- Email (FieldID: 28)
UPDATE Empirical_Tenants.ConfirmedTenants
SET Email = NULL,
    ModifiedDate = NOW(),
    ModifiedBy = 22
WHERE ProviderID = 5
  AND ConfirmedTenantID IN (
    SELECT ConfirmedTenantID
    FROM Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields
    WHERE FieldID = 28
);

select count(*) from Empirical_Tenants.ConfirmedTenantsFieldAudit where ConfirmedTenantFieldAuditID in (select ConfirmedTenantFieldAuditID from Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields); -- 493590
delete from Empirical_Tenants.ConfirmedTenantsFieldAudit where ConfirmedTenantFieldAuditID in (select ConfirmedTenantFieldAuditID from Empirical_DataFixes.ConfirmedTenantsWithZoomInfoFields);

select count(*) from Empirical_DataStage.Tenants_Stage where ProviderID=15 and BranchID is not null; -- 154834
delete from Empirical_DataStage.Tenants_Stage where ProviderID=15 and BranchID is not null;


drop temporary table if exists confirmedTenantsWithZoominfo;
create temporary table confirmedTenantsWithZoominfo as(
select st.PropertyID,CTFA.ConfirmedTenantFieldAuditID,CTFA.FieldID,AF.FieldName,CT.* from Empirical_Tenants.ConfirmedTenantsFieldAudit CTFA
inner join Empirical_Tenants.AuditFields AF on CTFA.FieldID=AF.FieldID 
inner join Empirical_Tenants.ConfirmedTenants CT on CT.ConfirmedTenantID=CTFA.ConfirmedTenantID
inner join Empirical_Tenants.ConfirmedTenants CTE on CTE.CompanyID=CT.CompanyID and CTE.ProviderID=1
left join Empirical_Prod.SuiteTenant st on st.ConfirmedTenantID=CTE.ConfirmedTenantID
where CTFA.ProviderID=15 -- and CTFA.IsActive=1 and st.IsActive=1 -- and st.PropertyID=269652
);

UPDATE Empirical_Prod.Providers SET IsActive = '0' WHERE (ProviderID = '15');
