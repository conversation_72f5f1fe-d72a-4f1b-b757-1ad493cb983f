#%%
import numpy as np
import pandas as pd
from datetime import date
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
COLUMNS =[
 'ABN',
 'ACN',
 'VendorID',
 'TenantName',
 'Address1',
 'Address2',
 'City',
 'State',
 'StateAbbr',
 'CountryCode',
 'PostalCode',
 'NationalID',
 'OfficePhone',
 'Fax',
 'CEO<PERSON><PERSON>',
 'CEOTit<PERSON>',
 'LineOfBusiness',
 'SICCode',
 'Revenue',
 'EmployeesAtLocation',
 'EmployeeCount',
 'LegalStatus',
 'StatusCode',
 'SubsidiaryCode',
 'IsProcessed',
 'ConfirmedTenantID',
 'PropertyID',
 'MatchingScore',
 'CreatedDate',
 'ModifiedDate',
 'Latitude',
 'Longitude',
 'ParentCompanyID',
 'BranchID',
 'BatchID',
 'ProviderID',
 'Provider',
 'IsDefault',
 'NAICSCode',
 'NACECode',
 'Email',
 'WebsiteURL',
 'ModifiedBy',
 'IsHidden',
 'IsDeleted',
 'HidedBy',
 'HidedDate',
 'HideReasonID',
 'HideReasonComments',
 'ASICEntityStatus',
 'ASICEntityType',
 'ASICEntityClass',
 'ABNStatus',
 'ABN_StatusFromDate',
 'GST_Status',
 'GST_StatusFromDate',
 'RegistrationOrIncorporationDate',
 'EntityAge',
 'EmployeeIndicator',
 'RevenueIndicator',
 'HQ_ID',
 'HQ_CompanyName',
 'NumberofMembersinHierarchy',
 'ImmediateParentDUNS',
 'ImmediateParentName',
 'ImmediateParentCountry',
 'DomesticParentDUNS',
 'DomesticParentName',
 'DomesticParentCountry',
 'GlobalUltimateParentDUNS',
 'GlobalUltimateParentName',
 'GlobalUltimateParentCountry',
 'PrimarySICDesc',
 'PrimarySIC3Digit',
 'PrimarySIC3DigitDesc',
 'PrimarySIC2Digit',
 'PrimarySIC2DigitDesc',
 'PrimarySICDivision',
 'PrimarySICDivisionDesc',
 'SubHideReasonID',
 'ANZSICCode',
 'TradingNames',
 'CleanedAddress',
 'FloorNumber',
 'MarketableFlag',
 'OriginalAddress',
 'StreetName',
 'StreetNo',
 'StreetNoMax',
 'StreetNoMin',
 'StreetNoSet',
 'Suffix',
 'SuiteNumber',
 'ShopNumber',
 'SuiteNumberDisplay',
 'UnitNumber',
 'transform_commit',
 'transform_created_date',
 'transform_username',
 'TSI_Bucket',
 'TSI_State',
 'TSI_Country',
'TSI_Ingested_Date',
 'DisplayName', 
 'CompanyID',
 'ZI_C_RELEASE_DATE',
 'ZI_C_LAST_UPDATED_DATE',
 'AddressID',
 'address_match_score',
 'address_match_type',
 'al_property_id',
 'verify_commit',
 'verify_created_date',
 'verify_username',
'GNAFAddressDetailPID',
'MasterPropertyID',
'SuffixAbbr',
'ZI_C_TIER_GRADE',
'GoogleMapsOperationalStatus',
'AL_PropertyID',
'Mapped_Properties',
'street_name_match_score',
'street_number_match_score',
'No_of_property_matches',
'No_of_property_matches_with_range'
 ]

#%%
COLUMNS = sorted(COLUMNS)
#%%
len(COLUMNS)
#%%
state='WA'

#%%
il_df = pd.read_parquet(f'/home/<USER>/Downloads/{state}/Illion_prim_bronze_2024_05_28.parquet')
#%%
il_df.rename(columns={'Address1_x':'Address1','Address2_x':'Address2','Latitude_x':'Latitude','Longitude_x':'Longitude'},inplace=True)
il_df.shape
#%%
il_df = il_df[il_df['PropertyID'].notna()]
#%%
il_df.shape
#%%
il_df.info()
#%%
il_df['ID'].nunique()
#%%
il_df['StateAbbr'].unique()
#%%
il_df[il_df.duplicated(['ID'], keep=False)].shape
#%%
il_df.rename(columns={'ID':'VendorID'},inplace=True)
#%%
il_df['CountryCode'] = 14
il_df['IsProcessed'] = 0
il_df['Provider'] = 'Illion'
il_df['ProviderID'] = 6
il_df['TSI_Bucket'] = 'Bronze'
il_df['TSI_State']= state
il_df['StateAbbr']= state
il_df['TSI_Country'] = 'Australia'
il_df['TSI_Ingested_Date'] = date.today()
#%%
list(set(COLUMNS) - set(il_df))
#%%
set(il_df) - set(COLUMNS)
#%%
il_df[list(set(COLUMNS) - set(il_df))] = np.nan
#%% md
## zoominfo
#%%
zoominfo_df = pd.read_parquet(f'/home/<USER>/Downloads/{state}/ZoomInfo_prim_bronze_2024_05_28.parquet')
#%%
zoominfo_df.rename(columns={'Address1_x':'Address1','Address2_x':'Address2','Latitude_x':'Latitude','Longitude_x':'Longitude'},inplace=True)
zoominfo_df.shape
#%%
zoominfo_df = zoominfo_df[zoominfo_df['PropertyID'].notna()]
#%%
zoominfo_df.info()
#%%
zoominfo_df['ID'].nunique()
#%%
zoominfo_df.head()
#%%
zoominfo_df['State'].unique()
#%%
zoominfo_df['IsProcessed'] = 0
zoominfo_df['Provider'] = 'zoominfo'
zoominfo_df['ProviderID'] = 15
zoominfo_df['CountryCode'] = 14
zoominfo_df['TSI_Bucket'] = 'Bronze'
zoominfo_df['StateAbbr']= state
zoominfo_df['TSI_State']= state
zoominfo_df['TSI_Country'] = 'Australia'
zoominfo_df['TSI_Ingested_Date'] = date.today()
#%%
zoominfo_df.rename(columns={'ID':'VendorID'},inplace=True)
#%%
zoominfo_df[zoominfo_df.duplicated('VendorID', )].shape
#%%
list(set(COLUMNS) - set(zoominfo_df))
#%%
zoominfo_df[list(set(COLUMNS) - set(zoominfo_df))] = np.nan
#%%
list(set(zoominfo_df) - set(COLUMNS))
#%%
zoominfo_df[COLUMNS].info()
#%% md
## GOOGLE
#%%
google_df = pd.read_parquet(f'/home/<USER>/Downloads/{state}/Google_prim_bronze_2024_05_28.parquet')
#%%
google_df.rename(columns={'Address1_x':'Address1','Address2_x':'Address2','Latitude_x':'Latitude','Longitude_x':'Longitude'},inplace=True)
google_df.shape
#%%
google_df = google_df[google_df['PropertyID'].notna()]
#%%
google_df.shape
#%%
google_df.head()
#%%
google_df.info()
#%%
google_df['ID'].nunique()
#%%
google_df['State'].unique()
#%%
google_df.rename(columns={'ID':'VendorID'},inplace=True)
#%%
google_df['CountryCode'] = 14
google_df['IsProcessed'] = 0
google_df['Provider'] = 'Google_Maps'
google_df['ProviderID'] = 18
google_df['TSI_Bucket'] = 'Bronze'
google_df['TSI_State']= state
google_df['StateAbbr']=state
google_df['TSI_Country'] = 'Australia'
google_df['TSI_Ingested_Date'] = date.today()
#%%
list(set(COLUMNS) - set(google_df))
#%%

google_df[list(set(COLUMNS) - set(google_df))] = np.nan
#%%
google_df[google_df['VendorID'].isna()].shape
#%%
google_df[(google_df['Address1'].isna()) | (google_df['TenantName'].isna())].shape
#%%
set(google_df) - set(COLUMNS)
#%% md
## company Websites
#%%
company_df=pd.read_parquet(f'/home/<USER>/Downloads/{state}/Website_prim_bronze_2024_05_28.parquet')
#%%
company_df.rename(columns={'Address1_x':'Address1','Address2_x':'Address2','Latitude_x':'Latitude','Longitude_x':'Longitude'},inplace=True)
company_df.shape
#%%
company_df = company_df[company_df['PropertyID'].notna()]
#%%
company_df.shape
#%%
company_df.head()
#%%
company_df['ID'].nunique()
#%%
company_df['State'].unique()
#%%
company_df.rename(columns={'ID':'VendorID'},inplace=True)
#%%
company_df.info()
#%%
company_df['StateAbbr'].unique()
#%%
company_df['ProviderID']=19
company_df['Provider'] = 'company_websites'
company_df['TSI_Bucket'] = 'Orphan'
company_df['TSI_State']= state
company_df['StateAbbr']=state
company_df['TSI_Country'] = 'Australia'
company_df['CountryCode'] = 14
company_df['TSI_Ingested_Date'] = date.today()
#%%
(list(set(COLUMNS) - set(company_df)))
#%%
import numpy as np
company_df[list(set(COLUMNS) - set(company_df))] = np.nan
#%%
set(company_df) - set(COLUMNS)
#%%
company_df['VendorID'].isna().sum()
#%%
il_df[COLUMNS].shape[0] + google_df[COLUMNS].shape[0] + zoominfo_df[COLUMNS].shape[0] + company_df[COLUMNS].shape[0]
#%%
concatenated_tenant_df = pd.concat([il_df[COLUMNS],
google_df[COLUMNS],
zoominfo_df[COLUMNS],
company_df[COLUMNS]])
#%%
concatenated_tenant_df.shape
#%%
# concatenated_tenant_df.to_csv('melbourne_bronze_new.csv', index=False)
#%%
1103892 + 695799 + 873988
#%%
# Ingesting the Data to MYSQL
# Import create_engine to create and manage database connection pool
from sqlalchemy import create_engine


username = 'xxxxxxxxxx'
password = 'xxxxxxxxxx'
host = 'localhost'
port = '4339'
database_name = 'Empirical_DataStage' # Change the database_name if required

# Connection String
connection_string = f'mysql+mysqlconnector://{username}:{password}@{host}:{port}/{database_name}'
#connection_string = f'mysql+mysqlconnector://{db_user}:{db_password}@{db_host}/{db_name}'
# Create the SQLAlchemy engine
database_engine = create_engine(connection_string)
#%%
company_df[COLUMNS].to_sql('BT_Bronze_Tenants_2024_05_20', database_engine, if_exists='append', index=False, chunksize=10000)
#%%
131586+15430
#%%
