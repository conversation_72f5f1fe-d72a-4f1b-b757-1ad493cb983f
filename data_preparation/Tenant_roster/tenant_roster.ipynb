#%%
import pandas as pd
import geopandas as gpd
import logging
from sqlalchemy import create_engine,text
import yaml
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
tenant_stage_columns=['VendorID',
 'TenantName',
 'Address1',
 'Address2',
 'City',
 'State',
 'StateAbbr',
 'CountryCode',
 'PostalCode',
 'NationalID',
 'OfficePhone',
 'Fax',
 'CEON<PERSON>',
 'CEOTitle',
 'LineOfBusiness',
 'SICCode',
 'Revenue',
 'EmployeesAtLocation',
 'EmployeeCount',
 'LegalStatus',
 'StatusCode',
 'SubsidiaryCode',
 'IsProcessed',
 'ConfirmedTenantID',
 'PropertyID',
 'MatchingScore',
 'CreatedDate',
 'ModifiedDate',
 'Latitude',
 'Longitude',
 'ParentCompanyID',
 'BranchID',
 'BatchID',
 'ProviderID',
 'IsDefault',
 'NAICSCode',
 'NACECode',
 'Email',
 'WebsiteURL',
 'ModifiedBy',
 'IsHidden',
 'IsDeleted',
 'HidedBy',
 'HidedDate',
 'HideReasonID',
 'HideReasonComments',
 'ASICEntityStatus',
 'ASICEntityType',
 'ASICEntityClass',
 'ABNStatus',
 'ABN_StatusFromDate',
 'GST_Status',
 'GST_StatusFromDate',
 'RegistrationOrIncorporationDate',
 'EntityAge',
 'EmployeeIndicator',
 'RevenueIndicator',
 'HQ_ID',
 'HQ_CompanyName',
 'NumberofMembersinHierarchy',
 'ImmediateParentDUNS',
 'ImmediateParentName',
 'ImmediateParentCountry',
 'DomesticParentDUNS',
 'DomesticParentName',
 'DomesticParentCountry',
 'GlobalUltimateParentDUNS',
 'GlobalUltimateParentName',
 'GlobalUltimateParentCountry',
 'PrimarySICDesc',
 'PrimarySIC3Digit',
 'PrimarySIC3DigitDesc',
 'PrimarySIC2Digit',
 'PrimarySIC2DigitDesc',
 'PrimarySICDivision',
 'PrimarySICDivisionDesc',
 'SubHideReasonID',
 'ANZSICCode']
#%%
table_name = 'Tenant_Roster_2024_06_19'
#%%
# Load configuration from config.yaml
with open('./config.yaml', 'r') as f:
    config = yaml.safe_load(f)
#%%
# Extract database connection details
source_db_config = config['source_db']
source_db_config
#%%
# Connection String
source_connection_string = f"mysql+mysqlconnector://{source_db_config['username']}:{source_db_config['password']}@{source_db_config['host']}:{source_db_config['port']}/{source_db_config['database_name']}"
engine = create_engine(source_connection_string)
#%%
stage_query = '''select * from  Empirical_DataStage.Tenants_Stage where ProviderID = 40'''
#%%
stage_df = pd.read_sql(stage_query,con=engine)
#%%
stage_df.head()
#%%
stage_df.sort_index(axis=1).info()
#%%
stage_df.to_csv('tenant_roster_ingested_2024_06_19.csv', index=False)
#%%
stage_df.columns.to_list()
#%%
df = pd.read_parquet('/home/<USER>/Downloads/roster-000000000000.parquet')
#%%
df.info()
#%%
df = df[df['TenantName'].notna()]
#%%
df.shape
#%%
df.drop_duplicates(['TenantName','PropertyID'], inplace=True)
#%%
import hashlib

def generate_vendor_id(row):
    combined_str = f"{row['PropertyID']}_{row['TenantName']}_Tenant_Roster"
    md5_hash = hashlib.md5(combined_str.encode()).hexdigest()
    return md5_hash

df['VendorID'] = df.apply(generate_vendor_id, axis=1)


#%%
df = df[['VendorID','TenantName','PropertyID']]
#%%
df['ProviderID'] = 40
df['ModifiedBy'] = 22
df['BatchID'] = 20240619
df['CreatedDate'] = pd.to_datetime('now')
#%%
df.head()
#%%
df.info()
#%%
# import hashlib

# def generate_vendor_id(row):
#     combined_str = f"{row['PropertyID']}_{row['TenantName']}_Tenant_Roster"
#     sha256_hash = hashlib.sha256(combined_str.encode()).hexdigest()
#     truncated_hash = sha256_hash[:50]  # Take the first 10 characters
#     return truncated_hash

# # Assuming df is your pandas DataFrame
# df['VendorID_old'] = df.apply(generate_vendor_id, axis=1)

#%%
non_null_columns = df.columns[df.notnull().any()].tolist()

#%%
df.to_sql(table_name, con=source_connection_string, if_exists='replace', index=False, chunksize=10000)
#%%
query=f'''
select ts.*,c.CityName as City,a.CountryID as CountryCode,a.ZipCode as PostalCode, a.StateID as State, s.StateAbbr from Empirical_DataStage.{table_name} as ts 
inner join Empirical_Prod.Address a on ts.PropertyID=a.ParentID
inner join Empirical_Prod.City c on a.CityID=c.CityID
inner join Empirical_Prod.State s on a.StateID = s.StateID
where a.ParentTableID=1 and a.Sequence=1 and a.IsActive=1;
'''
#%%
ingest_df = pd.read_sql(query,con=source_connection_string)
#%%
ingest_df.info()
#%%
ingest_df['VendorID'].nunique()
#%%
list(set(tenant_stage_columns)-set(ingest_df))
#%%
import numpy as np
ingest_df[list(set(tenant_stage_columns)-set(ingest_df))] = np.nan
#%%
ingest_df.head()
#%%
6185713 + ingest_df.shape[0]
#%%
list(set(stage_df)-set(ingest_df))
#%%
ingest_df[tenant_stage_columns].to_sql('Tenants_Stage', con=source_connection_string, if_exists='append', index=False, chunksize=10000)
#%%
