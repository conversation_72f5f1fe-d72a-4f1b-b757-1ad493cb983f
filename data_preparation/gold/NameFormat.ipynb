#%%
import pandas as pd
#%%
tenant_df = pd.read_csv('./TenantNameCeoName.csv')
#%%
tenant_df.head()
#%%
tenant_df['FormattedTenantName'] = tenant_df['TenantName'].str.title()
#%%
tenant_df['CEOName'] = tenant_df['CEOName'].str.replace(' none ','').str.replace('none none','').str.replace('none ','')
#%%
tenant_df['CEOName'].str.contains('none').sum()
#%%
tenant_df[tenant_df['CEOName'].str.contains('none')]
#%%
tenant_df['FormattedCEOName'] = tenant_df['CEOName'].str.title()
#%%
tenant_df.to_csv('./FormattedTenantCEOName.csv')
#%%
tenant_df.info()
#%%
