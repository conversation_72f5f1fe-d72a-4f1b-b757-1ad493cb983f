#%%
import pandas as pd
#%%
req_df = pd.read_csv('./db_tenant_stage.csv')
#%%
tenant_df = pd.read_parquet('./gold/tenant_df.parquet')
#%%
tenant_df.head(100)
#%%
list(set(req_df) - set(tenant_df))
#%%
['ConfidenceScore',
 'HideReasonID',
 'HidedBy',
 'MatchingScore',
 'CountryCode',
 'Tenant_Stage_Id',
 'BatchID',
 'IsHidden',
 'CreatedDate',
 'PrimaryProviderArealyticsTenantID',
 'HidedDate',
 'IsDeleted',
 'BranchID',
 'ProviderID',
 'Provider',
 'HideReasonComments',
 'IsProcessed',
 'ModifiedDate',
 'NACECode',
 'Arealytics_Tenant_Id',
 'IsPrimaryProvider',
 'ModifiedBy',
 'SubHideReasonID',
 'StatusCode',
 'ConfirmedTenantID',
 'NAICSCode',
 'IsDefault',
 'ParentCompanyID',
 'NationalID']
#%%
tenant_df.info()
#%%
tenant_df_req = tenant_df.drop(['similarity_score_google_maps','similarity_score_overture','similarity_score_zoominfo'],axis=1)
#%%
tenant_df_req.info()
#%%
tenant_ref_df = pd.read_parquet('./gold/tenant_ref_df.parquet')
#%%
tenant_ref_df.info()
#%%
import pandas as pd

# Assuming df1 and df2 are your two DataFrames

# Step 1: Check if the columns are the same in both DataFrames
if set(tenant_df_req.columns) != set(tenant_ref_df.columns):
    print("Columns are not the same in both DataFrames.")
else:
    # Step 2: Check if the non-null values are the same in corresponding columns
    for column in tenant_df_req.columns:
        if not tenant_df_req[column].notna().equals(tenant_ref_df[column].notna()):
            print(f"Non-null values are not the same in column {column}.")
            break
        else:
            print(f"Both DataFrames have the same non-null values in {column} columns.")
            
    print("Both DataFrames have the same non-null values in corresponding columns.")

#%%
import pandas as pd

# Assuming df1 and df2 are your two DataFrames

# Step 1: Check if the columns are the same in both DataFrames
if set(tenant_df_req.columns) != set(tenant_ref_df.columns):
    print("Columns are not the same in both DataFrames.")
else:
    # Step 2: Check if each row has the same number of non-null values
    if (tenant_df_req.notna().sum(axis=1) == tenant_ref_df.notna().sum(axis=1)).all():
        print("Each row has the same number of non-null values in both DataFrames.")
    else:
        print("Each row does not have the same number of non-null values in both DataFrames.")

#%%
tenant_df_req.notna().sum(axis=1)
#%%
tenant_ref_df.notna().sum(axis=1)
#%%
(tenant_df_req.notna().sum(axis=1) == tenant_ref_df.notna().sum(axis=1)).all()
#%%
contains_none_columns = tenant_df_req.columns[tenant_df_req.apply(lambda col: any('none' in str(val) for val in col) if col.notna().any() else False)]

#%%
contains_none_columns
#%%
filtered_df = tenant_df_req[tenant_df_req['CEOName'].notna() & tenant_df_req['CEOName'].str.contains('none')]

#%%
filtered_df .shape
#%%
filtered_df['CEOName'].head()
#%%
filtered_df['Email']
#%%
