#%%
import pandas as pd
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
import re
def extract_street_number(s):
    s=str(s)
    matches = re.findall(r'\d+', s)
    if matches:
        longest_match = max(matches, key=len)
        return longest_match
    else:
        return None
#%%
def add_street_max(row):
    if pd.isna(row['StreetNumberMax']):
        return row['StreetNumberMin']
    return row['StreetNumberMax']

#%%
prop_df = pd.read_csv('./VIC_Properties_from_prop.csv', low_memory=False)
#%%
# prop_df = prop_df[prop_df['CondoTypeName'] != 'Strata']
#%%
prop_df['StreetNumberMin'] = prop_df['StreetNumberMin'].apply(extract_street_number)
#%%
prop_df['StreetNumberMax'] = prop_df['StreetNumberMax'].apply(extract_street_number)
#%%
prop_df['StreetNumberMax'] = prop_df.apply(add_street_max, axis=1)
#%%
prop_df.info()
#%%
prop_df.head()
#%%
(prop_df['StreetNumberMin'].str.isdigit()).sum()
#%%
prop_df.dropna(subset=['AddressStreetName','StreetNumberMin'], how='any', inplace=True)
#%%
prop_df[(~prop_df['StreetNumberMin'].str.isdigit())].head()
#%%
prop_df['StreetNumberMin'] = prop_df['StreetNumberMin'].astype('int')
prop_df['StreetNumberMax'] = prop_df['StreetNumberMax'].astype('int')
#%%
req_zipcodes = ['3000', '3002', '3003', '3006', '3008', '3031', '3051', '3052', '3053', '3054']
prop_df['ZipCode'] = prop_df['ZipCode'].astype('str')
prop_df = prop_df[prop_df['ZipCode'].isin(req_zipcodes)]
#%%
import geopandas as gpd
geometry_prop = gpd.points_from_xy(prop_df['Longitude'],prop_df['Latitude'])
prop_gdf = gpd.GeoDataFrame(prop_df, crs='EPSG:4326', geometry=geometry_prop)


#%%
prop_gdf.info()
#%%
prop_gdf.head()
#%%
# Change CRS to EPSG:7854
prop_gdf = prop_gdf.to_crs(epsg=7854)
# Add a buffer column with a distance of 50 meters
prop_gdf['buffer'] = prop_gdf.geometry.buffer(100)
# Convert the buffer to EPSG:4326 and set it as active geometry
prop_gdf = prop_gdf.set_geometry(prop_gdf['buffer'].to_crs(epsg=4326))
#%%
prop_gdf.info()
#%%
prop_gdf.head()
#%%
tenant_df = pd.read_parquet('./VIC/city_of_melbourne_illion.parquet')
#%%
# tenant_df = tenant_df[tenant_df['PropertyID'].isna()]
#%%

#%% md
tenant_df.info()
#%%
tenant_df.sort_index(axis=1).head()
#%%
(tenant_df['StreetNoMin'].astype('str').str.isdigit()).sum()
#%%
address_detail_df = pd.read_csv('/home/<USER>/Downloads/VIC_ADDRESS_DETAIL_psv.psv', delimiter='|', low_memory=False)
#%%
address_detail_df.shape
#%%
address_detail_df.head()
#%%
address_detail_df['ADDRESS_DETAIL_PID'] = address_detail_df['ADDRESS_DETAIL_PID'].str.lower()
#%%
address_site_df = pd.read_csv('/home/<USER>/Downloads/VIC_ADDRESS_SITE_GEOCODE_psv.psv', delimiter='|', low_memory=False)
#%%
address_site_df.duplicated('ADDRESS_SITE_PID', keep=False).sum()
#%%
address_site_df.head()
#%%
address_site_df = address_site_df.sort_values('DATE_CREATED', ascending=False).drop_duplicates('ADDRESS_SITE_PID', keep='first')
#%%
address_site_df.duplicated('ADDRESS_SITE_PID', keep=False).sum()
#%%
address_detail_site_df = pd.merge(address_detail_df, address_site_df, on='ADDRESS_SITE_PID')
#%%
address_detail_site_df.shape
#%%
tenant_merge_df = pd.merge(tenant_df, address_detail_site_df[['ADDRESS_DETAIL_PID','LONGITUDE','LATITUDE']], left_on='GNAFAddressDetailPID', right_on='ADDRESS_DETAIL_PID', how='inner')
#%%
tenant_merge_df.info()
#%%
tenant_merge_df.sort_index(axis=1)[['Longitude','LONGITUDE','Latitude','LATITUDE']].head()
#%%
import geopandas as gpd
geometry_tenant = gpd.points_from_xy(tenant_merge_df['LONGITUDE'],tenant_merge_df['LATITUDE'])
tenant_merge_gdf = gpd.GeoDataFrame(tenant_merge_df, crs='EPSG:4326', geometry=geometry_tenant)
tenant_merge_gdf.info()
tenant_merge_gdf.head()
#%%
tenant_merge_gdf.crs
#%%
tenant_prop_df = gpd.sjoin(tenant_merge_gdf,prop_gdf, predicate='within')
#%%
tenant_prop_df.head()
#%%
tenant_prop_df[['ID','TenantName','StreetName','StreetNo','StreetNoMin','StreetNoMax','AddressStreetName','StreetNumberMin','StreetNumberMax','PropertyID_left','PropertyID_right']].info()
#%%
tenant_prop_df.shape
#%%
def get_number_set(start,end):
    # Ensure the numbers are in ascending order and are both even or odd
    if (start > end) or (start % 2 != end % 2):
        return set()
    
    # Return a set of numbers incrementing by 2
    return set(range(start, end + 1, 2))
#%%
def street_number_match(row):
    address_set_1=get_number_set(int(row['StreetNoMin']),int(row['StreetNoMax']))
    address_set_2=get_number_set(int(row['StreetNumberMin']),int(row['StreetNumberMax']))
    return int(bool(address_set_1.intersection(address_set_2)))
    
    
#%%
from fuzzywuzzy import fuzz
def fuzzy_match(row):
    similarity_score = fuzz.partial_ratio(row['AddressStreetName'].upper(), row['StreetName'].upper())
    return similarity_score
#%%
tenant_prop_df.sort_index(axis=1).sort_values('StreetNumberMax')[['ID','TenantName','StreetName','StreetNo','StreetNoMin','StreetNoMax','AddressStreetName','StreetNumberMin','StreetNumberMax','PropertyID_left','PropertyID_right']].head(100)
#%%
tenant_prop_df['ID'].nunique()
#%%
tenant_prop_df['StreetNameMatch'] = tenant_prop_df.apply(fuzzy_match, axis=1)
#%%
tenant_prop_df['StreetNumberMatch'] = tenant_prop_df.apply(street_number_match, axis=1)
#%%
tenant_prop_df.sort_index(axis=1).sort_values('ID')[['ID','TenantName','StreetName','StreetNo','StreetNoMin','StreetNoMax','AddressStreetName','StreetNumberMin','StreetNumberMax','PropertyID_left','PropertyID_right','StreetNameMatch','StreetNumberMatch']].head(100)
#%%
tenant_prop_mapped_df = tenant_prop_df[(tenant_prop_df['StreetNameMatch'] >= 85) & (tenant_prop_df['StreetNumberMatch'] == 1)]
#%%
tenant_prop_mapped_df.shape
#%%
tenant_prop_mapped_df['ID'].nunique()
#%%
tenant_prop_mapped_df.sort_index(axis=1).sort_values('ID')[['ID','TenantName','StreetName','StreetNo','StreetNoMin','StreetNoMax','AddressStreetName','StreetNumberMin','StreetNumberMax','PropertyID_left','PropertyID_right','StreetNameMatch','StreetNumberMatch']].head(100)
#%%
from fuzzywuzzy import fuzz
fuzz.partial_ratio('Bligh','Bligh')
#%%
