#%%
import pandas as pd
import pandas as pd
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
from pandarallel import pandarallel
from fuzzywuzzy import fuzz

pandarallel.initialize(progress_bar=True, nb_workers=8)
#%%
import re
def extract_number(s):
    s=str(s)
    matches = re.findall(r'\d+', s)
    if matches:
        longest_match = max(matches, key=len)
        return longest_match
    else:
        return None
#%%
def add_street_max(row):
    if pd.isna(row['StreetNumberMax']):
        return row['StreetNumberMin']
    return row['StreetNumberMax']

#%%
def add_tenant_street_max(row):
    if pd.isna(row['StreetNoMax']):
        return row['StreetNoMin']
    return row['StreetNoMax']
#%%
prop_df = pd.read_csv('/home/<USER>/Downloads/WA_prop_with_alt_addresses.csv', low_memory=False)
#%%
# prop_df = prop_df[prop_df['ZipCode'] == 6000]
#%%
prop_df['PropertyID'].nunique()
#%%
prop_df.info()
#%%
prop_df.head()
#%%
def combining_suffix(row):
    if pd.notna(row['SuffixName1']) and pd.notna(row['SuffixName2']):
        return row['SuffixName1'] + " " + row['SuffixName2']
    elif pd.notna(row['SuffixName1']) and pd.isna(row['SuffixName2']):
        return row['SuffixName1']
    else:
        return None


prop_df['SuffixName'] = prop_df.apply(combining_suffix, axis=1)
#%%
prop_df[prop_df['SuffixName2'].notna()].head()
#%%
prop_df = prop_df[(prop_df['SuffixName1'].notna())]
#%%
prop_df.shape
#%%
prop_df[prop_df['Sequence']==1].shape
#%%
prop_df['StreetNumberMin'] = prop_df['StreetNumberMin'].apply(extract_number)
#%%
prop_df['StreetNumberMax'] = prop_df['StreetNumberMax'].apply(extract_number)
#%%
prop_df['StreetNumberMax'] = prop_df.apply(add_street_max, axis=1)
#%%
prop_df = prop_df[(prop_df['StreetNumberMin'].notna()) & (prop_df.AddressStreetName.notna())]
#%%
prop_df['StreetNumberMin'] = prop_df['StreetNumberMin'].astype('int')
prop_df['StreetNumberMax'] = prop_df['StreetNumberMax'].astype('int')
#%%
prop_df['StreetNumberMin'].isna().sum()
#%%
prop_df.info()
#%%
prop_df['PropertyID'].nunique()
#%%
provider = 'Website'
#%%

# zoominfo_df_1 = pd.read_parquet('/home/<USER>/Downloads/WA/orphan/beta_data_zoominfo.parquet')
#%%
# zoominfo_df_1.shape
#%%
# zoominfo_gl_df = pd.read_parquet(f'/home/<USER>/Downloads/WA/orphan/beta_data_zoominfo_google_addresses.parquet')
#%%
# zoominfo_gl_df.shape
#%%
# zoominfo_gl_df = zoominfo_gl_df[zoominfo_gl_df['PostalCode'].notna()]
#%%
# zoominfo_df_1[zoominfo_df_1['ID'].isin(zoominfo_gl_df['ID'])].shape
#%%
# zoominfo_df_1 = zoominfo_df_1[~zoominfo_df_1['ID'].isin(zoominfo_gl_df['ID'])]
#%%
# zoominfo_df = pd.concat([zoominfo_df_1,zoominfo_gl_df])
#%%
# zoominfo_df.shape
#%%
# zoominfo_df.info()
#%%
# zoominfo_df = zoominfo_df[zoominfo_df['PostalCode'].str.isdigit()]
#%%
tenant_first_df = pd.read_parquet('/home/<USER>/Downloads/beta_data_company_websites.parquet')
# tenant_first_df = zoominfo_df
#%%
tenant_first_df.shape[0]
#%%
tenant_first_df.PostalCode.info()
#%%
# tenant_first_df['PostalCode'] = tenant_first_df['PostalCode'].apply(extract_number)
#%%
tenant_first_df = tenant_first_df[(tenant_first_df['PostalCode'].notna()) &(tenant_first_df['PostalCode'].str.isdigit())]
#%%
tenant_first_df['PostalCode'] = tenant_first_df['PostalCode'].astype('int')
#%%
# tenant_first_df = tenant_first_df[tenant_first_df['PostalCode'] == 6000]
#%%
tenant_first_df.info()
#%%
tenant_df = tenant_first_df[tenant_first_df['Suffix'].notna()]
#%%
tenant_df = tenant_df[(tenant_df['StreetNoMin'].notna()) &(tenant_df['StreetName'].notna())]
#%%
tenant_df.shape
#%%
tenant_df = tenant_df[tenant_df['PostalCode'].isin(prop_df['ZipCode'])]
#%%
tenant_df.shape
#%%
(tenant_df['StreetName'].isna() | tenant_df['Suffix'].isna() | tenant_df['StreetNo'].isna()).sum()
#%%
(prop_df.AddressStreetName.isna() | prop_df.StreetNumberMin.isna()).sum()
#%%
tenant_df.head()
#%%
tenant_check_df = tenant_df.head(10)
#%%
prop_df.set_index('ZipCode', inplace=True)
#%%
tenant_street_name = 'Little Bourke'
prop_street_name = 'Bourke'
fuzz.token_set_ratio(tenant_street_name.upper(),prop_street_name.upper())
#%%
from fuzzywuzzy import fuzz
import numpy as np
import pandas as pd

def get_number_set(start, end):
    if pd.isna(start) or pd.isna(end):
        return set()
    # Return a set of numbers incrementing by 2
    return set(range(start, end + 1, 2))

def map_properties(tenant_row):
    post_code = tenant_row['PostalCode']
    tenant_street_name = tenant_row['StreetName']
    tenant_street_numbers = get_number_set(tenant_row['StreetNoMin'], tenant_row['StreetNoMax'])
    req_prop_list = []

    try:
        properties = prop_df.loc[post_code]

        # If properties is a Series, convert it to a DataFrame
        if isinstance(properties, pd.Series):
            properties = properties.to_frame().T

        for index, row in properties.iterrows():
            prop_street_name = row['AddressStreetName']
            street_name_matching_score = fuzz.ratio(tenant_street_name.strip().upper(), prop_street_name.strip().upper())
            if street_name_matching_score <=80:
                continue
            prop_street_numbers = get_number_set(row['StreetNumberMin'], row['StreetNumberMax'])
            street_number_matching_score = int(bool(tenant_street_numbers.intersection(prop_street_numbers)))
            if street_name_matching_score > 80 and street_number_matching_score == 1 and (row['SuffixName'].strip().upper() == tenant_row['Suffix'].strip().upper()):
                if row['PropertyID'] not in req_prop_list:
                    req_prop_list.append(row['PropertyID'])

    except Exception as e:
        # Handle any exception and print the error message
        print(f"An error occurred: {e}")
        print(row['SuffixName'])
    
    if len(req_prop_list) >= 1:
        return req_prop_list
    return np.nan

#%%
%time tenant_df['Mapped_Properties'] = tenant_df.parallel_apply(map_properties, axis=1)
#%%
import numpy as np

tenant_df['No_of_property_matches_with_range'] = tenant_df['Mapped_Properties'].apply(lambda x: 0 if np.isnan(x).any() else len(x))
#%%
tenant_df['Mapped_Properties'].notna().sum()
#%%
# tenant_df.to_parquet('Illion_ratio_alt_property_mapped.parquet', index=False)
# tenant_df.to_csv('Illion_ratio_alt_property_mapped.csv', index=False)
#%%
expload_tenant_df = tenant_df.explode('Mapped_Properties')
#%%
expload_tenant_df.dropna(subset='Mapped_Properties')['ID'].nunique()
#%%
expload_tenant_df.rename(columns={'PropertyID':'AL_PropertyID'}, inplace=True)
#%%
expload_tenant_property_df = pd.merge(expload_tenant_df,prop_df, how='inner', left_on='Mapped_Properties', right_on='PropertyID')
#%%
expload_tenant_property_df.info()
#%%
expload_tenant_property_df.dropna(subset='Mapped_Properties')['ID'].nunique()
#%%
def fuzzy_match(row):
    similarity_score = fuzz.ratio(row['AddressStreetName'].strip().upper(), row['StreetName'].strip().upper())
    return similarity_score
#%%
expload_tenant_property_df['street_name_match_score'] = expload_tenant_property_df.parallel_apply(fuzzy_match, axis=1)
#%%
expload_tenant_property_df = expload_tenant_property_df[expload_tenant_property_df['street_name_match_score']>80]
#%%
expload_tenant_property_df['Suffix']=expload_tenant_property_df['Suffix'].apply(lambda x:x.strip().upper() if pd.notna(x) else x)
expload_tenant_property_df['SuffixName']=expload_tenant_property_df['SuffixName'].apply(lambda x:x.strip().upper() if pd.notna(x) else x)
#%%
expload_tenant_property_df = expload_tenant_property_df[expload_tenant_property_df['Suffix']==expload_tenant_property_df["SuffixName"]]
#%%
expload_tenant_property_df.shape
#%%
def match_score(row):
    if pd.isna(row['Mapped_Properties']):
        return 0
    tenant_num_min = row['StreetNoMin']
    tenant_num_max = row['StreetNoMax']
    prop_num_min = int(row['StreetNumberMin'])
    prop_num_max = int(row['StreetNumberMax'])
    if (tenant_num_min==prop_num_min) and (tenant_num_max==prop_num_max):
        return 100
    elif (tenant_num_min==prop_num_min):
        return 95
    elif (tenant_num_max==prop_num_max):
        return 90
    else:
        return 0
#%%
expload_tenant_property_df['street_number_match_score'] = expload_tenant_property_df.parallel_apply(match_score, axis=1)
#%%
expload_tenant_property_matched_df = expload_tenant_property_df[expload_tenant_property_df['street_number_match_score'] != 0]
#%%
expload_tenant_property_matched_df.shape
#%%
prim = expload_tenant_property_matched_df[expload_tenant_property_matched_df['Sequence'] == 1]
#%%
prim.shape
#%%
prim.dropna(subset='Mapped_Properties')['ID'].nunique()
#%%
copied = expload_tenant_property_matched_df.copy()
#%%
# expload_tenant_property_df[expload_tenant_property_df['No_of_property_matches'] == 2][['ID','PropertyID','PropertyID','street_number_match_score','street_name_match_score','CondoTypeName','StreetName','AddressStreetName']].sort_values('ID').sort_index(axis=1).head(100)
#%%
# req = expload_tenant_property_df.sort_values('Sequence').sort_values('street_number_match_score', ascending=False).sort_values(['CondoTypeName'])

#%%
req = prim.sort_values(['Sequence','CondoTypeName','street_number_match_score'],ascending=[True,True,False])
#%%
req[req.duplicated('ID', keep=False)][['ID','PropertyID','street_number_match_score','CondoTypeName','StreetName','AddressStreetName','StreetNoMin','StreetNumberMax','StreetNumberMin','StreetNoMax','Sequence']].head(100)
#%%
req.dropna(subset='Mapped_Properties')['ID'].nunique()
#%%
req[req['ID'] == '892721577'][['ID','TenantName','StreetName','StreetNo','AddressStreetName','Mapped_Properties','Sequence']]
#%%
req = req.drop_duplicates(['ID','Mapped_Properties'])
#%%
req = req[~req.duplicated(['ID'],keep=False)]
#%%
req.shape
#%%
req.head()
#%%
req.to_parquet(f'/home/<USER>/Downloads/WA/{provider}_prim_bronze_2024_05_28.parquet',index=False)
req.to_csv(f'/home/<USER>/Downloads/WA/{provider}_prim_bronze_2024_05_28.csv',index=False)
#%%
set2 = copied[~(copied['ID'].isin(req['ID']))]

#%%
set2.head()
#%%
set2.shape
#%%
set2 = set2.drop_duplicates(['ID','Mapped_Properties'])
#%%
set2.shape
#%%
req_set_2 = set2[~set2.duplicated(['ID'],keep=False)]
#%%
req_set_2.shape
#%%
set3 = pd.concat([req_set_2,req])
#%%
set3.shape
#%%
# set3.to_parquet(f'/home/<USER>/Downloads/WA/{provider}_alt_bronze_2024_05_28.parquet',index=False)
# set3.to_csv(f'/home/<USER>/Downloads/WA/{provider}_alt_bronze_2024_05_28.csv',index=False)
#%%
set_range = expload_tenant_property_df[~expload_tenant_property_df['ID'].isin(set3['ID'])]
#%%
set_range = set_range.drop_duplicates(['ID','Mapped_Properties'])
#%%
set_range_unique = set_range[~set_range.duplicated(['ID'],keep=False)]
#%%
set_range_unique.shape
#%%
set_4 = pd.concat([set3,set_range_unique])
# set_4 = pd.concat([req_set_2,set_range_unique])
#%%
set_4.shape
#%%
set_4.head()
#%%
set_4['CondoTypeName'].value_counts()
#%%
set_4.shape
#%%
# set_4.to_parquet(f'/home/<USER>/Downloads/WA/{provider}_range_bronze_2024_05_28.parquet',index=False)
# set_4.to_csv(f'/home/<USER>/Downloads/WA/{provider}_range_bronze_2024_05_28.csv',index=False)
#%%
set_5 = expload_tenant_property_df[~expload_tenant_property_df['ID'].isin(set_4['ID'])]
#%%
set_5['ID'].nunique()
#%%
set_5.shape
#%%
set_5.drop('Mapped_Properties', axis=1,inplace=True)
#%%
set_5_mapped = pd.merge(set_5,tenant_df[['ID','Mapped_Properties']])
#%%
set_5_mapped['CondoTypeName'].value_counts()
#%%
set_5_mapped.shape
#%%
set_5_mapped['Mapped_Properties'] = set_5_mapped['Mapped_Properties'].astype('str')
#%%
set_5_mapped.head()
#%%
# set_5_mapped.to_csv(f'/home/<USER>/Downloads/WA/{provider}_multiple_Property_matched_tenants.csv', index=False)
#%%
110314 + 167720 + 108284 + 23722
#%%
410040/1199446
#%%
155/400
#%%
# tenant_first_df[~tenant_first_df['ID'].isin(final_unique['ID'])].to_parquet('Iliion_orphan_2024_05_18.parquet',index=False)
#%%
# final_unique_check.to_csv('melbourne_Iliion_bronze_2024_05_18.csv')
#%%
(req_unique_with_all_mapped['No_of_property_matches'] ==1 ).sum()
#%%
final_unique.shape[0]/tenant_df.shape[0]
#%%
7956+2217
#%%
###END
#%% md
# Filtering
#%%
import pandas as pd
#%%
df=pd.read_csv("ZoomInfo_unique_property_mapped_May_15_1356.csv",low_memory=False)
#%%
zoominfo=df[(df['AL_PropertyID']==df['PropertyID'])&(df['No_of_property_matches']==1)&(df['Sequence']==1)]
#%%
zoominfo.shape
#%%
zoominfo.to_csv("ZoomInfo_unique_property_mapped_filtered.csv",index=False)
#%%
zoominfo.to_parquet("ZoomInfo_unique_property_mapped_filtered_may_15.parquet")
#%%
30567+24318+3346+15793
#%%
df_set = pd.read_parquet('./')
#%%
import pandas as pd
df = pd.read_parquet('./Iliion_bronze_2024_05_18.parquet')
#%%
df.info()
#%%
COLUMNS = ['ID','TenantName','StreetName','Suffix','StreetNo','StreetNoMin','StreetNoMax','Latitude_x','Longitude_x','PropertyID','PropertyName','AddressStreetName','StreetNumberMin','StreetNumberMax','Latitude_y','Longitude_y']
#%%
df[COLUMNS].sample(n=100, random_state=42).sort_values('PropertyID')
#%%
Illion_df['AL_PropertyID'].shape
#%%
df[df['AL_PropertyID'] ==  df['PropertyID']].shape
#%%
