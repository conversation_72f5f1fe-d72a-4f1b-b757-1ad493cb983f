#%% md
# 
#%% md
#
#%%
import pandas as pd
import pandas as pd
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
from pandarallel import pandarallel
from fuzzywuzzy import fuzz

pandarallel.initialize(progress_bar=True, nb_workers=12)
#%%
import vaex
#%%
import re
def extract_number(s):
    s=str(s)
    # print(s)
    matches = re.findall(r'\d+', s)
    if matches:
        longest_match = max(matches, key=len)
        return longest_match
    else:
        return None
#%%

def add_street_max(StreetNumberMax,StreetNumberMin):
    if StreetNumberMax[i] is None:
        StreetNumberMin
    return StreetNumberMax

#%%
def add_tenant_street_max(StreetNoMax,StreetNoMin):
    if StreetNoMax!=StreetNoMax:
        return StreetNoMin
    return StreetNoMax
#%%
prop_df = vaex.read_csv('VIC_prop_with_alt_addresses.csv')
#%%
prop_df['PropertyID'].nunique()
#%%
prop_df.info()
#%%
prop_df.head()
#%%
def combining_suffix(SuffixName1, SuffixName2):
    if SuffixName1 is not None and SuffixName2 is not None:
        return SuffixName1 + " " + SuffixName2
    elif SuffixName1 is not None and SuffixName2 is None:
        return SuffixName1
    else:
        return None

#%%
prop_df['SuffixName1'].nunique()
#%%
prop_df['SuffixName2'].value_counts()
#%%
prop_df['SuffixName'] = prop_df.apply(combining_suffix,arguments = [prop_df['SuffixName1'],prop_df['SuffixName2']])
#%%
# prop_df['SuffixName']
#%%
# prop_df['SuffixName'].value_counts()
#%%
prop_df[prop_df['SuffixName2'].notna()].head(100)
#%%
# prop_df.info()
#%%
# prop_df = prop_df[(prop_df['SuffixName1'].notna())]
#%%
prop_df.shape
#%%
prop_df[prop_df['Sequence']==1].shape

#%%
res = prop_df['StreetNumberMin'].to_numpy()
#%%
result=[]
for i in res:
    s=str(i)
    # print(s)
    matches = re.findall(r'\d+', s)
    if matches:
        longest_match = max(matches, key=len)
        result.append(longest_match)
#%%
len(list(set(result)))
#%%
prop_df['StreetNumberMin'] = prop_df.apply(extract_number,arguments=[prop_df['StreetNumberMin']])
#%%
prop_df['StreetNumberMin'].nunique()
#%%
prop_df['StreetNumberMax'] = prop_df.apply(extract_number,arguments=[prop_df['StreetNumberMax']])
#%%
prop_df['StreetNumberMax'].nunique()
#%%
StreetNumberMax = prop_df['StreetNumberMax'].to_numpy()
#%%
StreetNumberMin = prop_df['StreetNumberMin'].to_numpy()
#%%
for i in range(len(StreetNumberMax)):
    if StreetNumberMax[i] is None:
        StreetNumberMax[i]=StreetNumberMin[i]
print(i) 
#%%
len(list(set(StreetNumberMax)))
#%%
prop_df['StreetNumberMax']=StreetNumberMax
#%%
prop_df = prop_df[(prop_df['SuffixName1'].notna())]
#%%
# prop_df['StreetNumberMax'] = prop_df.apply(add_street_max,arguments=[prop_df['StreetNumberMax'],prop_df['StreetNumberMin']])
#%%
prop_df['StreetNumberMax'].nunique()
#%%
prop_df = prop_df[prop_df['StreetNumberMin'].notna()]
#%%
prop_df['StreetNumberMin'].nunique()
#%%
prop_df['StreetNumberMax'].nunique()
#%%
prop_df = prop_df[prop_df['StreetNumberMin'].notna()]
#%%
prop_df['StreetNumberMin'].nunique()
#%%
prop_df['StreetNumberMax'].nunique()
#%%
# prop_df['StreetNumberMin'] = prop_df['StreetNumberMin'].astype('int')
# prop_df['StreetNumberMax'] = prop_df['StreetNumberMax'].astype('int')
#%%
prop_df['StreetNumberMin'].isna().sum()
#%%
prop_df.info()
#%%
tenant_first_df = pd.read_parquet('beta_data_company_websites.parquet')
# tenant_first_df = zoominfo_df
#%%
tenant_first_df = vaex.from_pandas(tenant_first_df)
#%%
tenant_first_df.shape[0]
#%%
tenant_first_df.PostalCode
#%%
tenant_first_df = tenant_first_df[tenant_first_df['PostalCode'].str.isdigit()]
#%%
tenant_first_df['PostalCode'].nunique()
#%%
tenant_first_df['PostalCode'] = tenant_first_df['PostalCode'].apply(lambda x: None if x!=x or x.strip()=="" else int(x))
#%%
tenant_first_df['PostalCode'].value_counts()
#%%
tenant_first_df.info()
#%%
tenant_df = tenant_first_df[tenant_first_df['Suffix'].notna()]
#%%
tenant_df = tenant_df[(tenant_df['StreetNoMin'].notna()) &(tenant_df['StreetName'].notna())]
#%%
tenant_df.shape
#%%

unique_zipcodes = (prop_df['ZipCode'].tolist())
tenant_df = tenant_df[tenant_df['PostalCode'].apply(lambda x: x in unique_zipcodes)]

#%%
tenant_df['StreetName']
#%%
tenant_df.head()
#%%
tenant_df[tenant_df['PostalCode']==5000].shape
#%%
from fuzzywuzzy import fuzz
import numpy as np
import pandas as pd
#%%
def get_number_set(start, end):
    if end is None or start is None:
        return set()
    start=int(start)
    end=int(end)
    return set(range(start, end + 1, 2))
#%%
PostalCode = tenant_df['PostalCode'].to_numpy()
#%%
len(PostalCode)
#%%
StreetName = tenant_df['StreetName'].to_numpy()
#%%
len(StreetName)
#%%
StreetNoMin = tenant_df['StreetNoMin'].to_numpy()
#%%
StreetNoMax = tenant_df['StreetNoMax'].to_numpy()
#%%
Suffix= tenant_df['Suffix'].to_numpy()
#%%
AddressStreetName_array = prop_df['AddressStreetName'].to_numpy()
#%%
StreetNumberMin_array = prop_df['StreetNumberMin'].to_numpy()
#%%
len(StreetNumberMin_array)
#%%
StreetNumberMax_array = prop_df['StreetNumberMax'].to_numpy()
#%%
len(StreetNumberMin_array)
#%%
PropertyID_array = prop_df['PropertyID'].to_numpy()
#%%
len(PropertyID_array)
#%%
SuffixName_array = prop_df['SuffixName'].to_numpy()
#%%
len(SuffixName_array )
#%%
from collections import defaultdict
Zipcode_dict = defaultdict(list)
res=0
for i, code in enumerate(prop_df['ZipCode'].values):
    res+=1
    Zipcode_dict[code].append(i)
for k,v in Zipcode_dict.items():
    Zipcode_dict[k]= str(v)
print(res)

#%%
# Convert defaultdict to a DataFrame
import ast

Zipcode = pd.DataFrame(list(Zipcode_dict.items()), columns=['ZIPCODE', 'Indices'])
Zipcode_array = Zipcode.to_numpy()
ast.literal_eval(Zipcode_array[Zipcode_array[:,0]==3000][0][1])[1]
#%%
# Zipcode_array[0]
#%%
# Zipcode_array[Zipcode_array[0, 0] == pid]
#%%
# PropertyID_array[3043]
#%%

# prop_df['AddressStreetName'].nunique()
#%%
# result=[]
# import ast
# count1=0
# count2=0
# count3=0
# count4=0
# count5=0
# count6=0
# count7=0
# Not_prepare=[]
# from tqdm import tqdm
# for i in tqdm(range(len(PostalCode))):
#     post_code = PostalCode[i]
#     tenant_street_name = StreetName[i]
#     tenant_street_numbers = get_number_set(StreetNoMin[i],StreetNoMax[i])
#     req_prop_list = []
   
#     properties = Zipcode_array[Zipcode_array[:,0]==post_code][0][1]
#     properties = ast.literal_eval(properties)
#     r = False
#     k=False
#     sc= False
#     if(len(properties)>=1):
#         # print(properties)
#         r=True
#         count5+=1
#     for index in properties:
#         prop_street_name = AddressStreetName_array[index]
#         street_name_matching_score = fuzz.ratio(tenant_street_name.strip().upper(), prop_street_name.strip().upper())
#         if street_name_matching_score <=80:
#             continue
#         prop_street_numbers = get_number_set(StreetNumberMin_array[index], StreetNumberMax_array[index])
#         street_number_matching_score = int(bool(tenant_street_numbers.intersection(prop_street_numbers)))
#         if(street_number_matching_score==1):
#             count1+=1
#         if(street_name_matching_score>80):
#             count2+=1
#         if SuffixName_array[index].strip().upper() == Suffix[i].strip().upper():
#             count3+=1
            
#         if street_name_matching_score > 80 and street_number_matching_score == 1 and (SuffixName_array[index].strip().upper() == Suffix[i].strip().upper()):
#             sc=True
#             property_id = PropertyID_array[index]
#             if(property_id):
#                 k=True
#             req_prop_list.append(property_id)

    
#     if(r &k ):
#         count7+=1
#     elif( r and not k and sc):
#         Not_prepare.append(post_code)
    
        
#     if len(req_prop_list)>=1:
#         count4+=1
#         result.append(list(set(req_prop_list)))
#         continue
    

#     result.append(np.nan)
    
#%%
# result
#%%
# len(Not_prepare)
#%%
# ast.literal_eval(Zipcode_array[Zipcode_array[:,0]==3000][0][1])[1]
#%%
# count1,count2,count3,count4,count5,count7
#%%
# (52857, 3490284, 3407502, 25256, 43471, 51888)

#%%
# len(result)
# # count=0
#%%
# SuffixName_array[0].strip().upper()
#%%
# Suffix[0].strip().upper()
#%%
def map_properties(post_code,tenant_street_name,StreetNoMin,StreetNoMax,Suffix):
    tenant_street_numbers = get_number_set(StreetNoMin,StreetNoMax)
    # print(tenant_street_numbers)
    req_prop_list = np.array([])
    try:
        properties = Zipcode_array[Zipcode_array[:,0]==post_code][0][1]
        properties = ast.literal_eval(properties)
        for index in properties:
            prop_street_name = AddressStreetName_array[index]
            street_name_matching_score = fuzz.ratio(tenant_street_name.strip().upper(), prop_street_name.strip().upper())
            if street_name_matching_score <=80:
                continue
            prop_street_numbers = get_number_set(StreetNumberMin_array[index], StreetNumberMax_array[index])
            street_number_matching_score = int(bool(tenant_street_numbers.intersection(prop_street_numbers)))
            if street_name_matching_score > 80 and street_number_matching_score == 1 and (SuffixName_array[index].strip().upper() == Suffix.strip().upper()):
                property_id = PropertyID_array[index]
                req_prop_list = np.append(req_prop_list, property_id)
                # req_prop_list.append(property_id)

    except Exception as e:
        # Handle any exception and print the error message
        print(f"An error occurred: {e}")
    if len(req_prop_list) >= 1:
        req_prop_list=(req_prop_list).tolist()
        return list(set(req_prop_list))
    return np.nan
#%%
temp=tenant_df.copy()
#%%
temp = temp.to_pandas_df()
#%%
%%time temp['Mapped_Properties'] = temp.parallel_apply(lambda row: map_properties(row['PostalCode'], row['StreetName'],row['StreetNoMin'], row['StreetNoMax'],row['Suffix']), axis=1)

#%%
# count
#%%
temp.info()
#%%
import numpy as np

temp['No_of_property_matches_with_range'] = temp['Mapped_Properties'].apply(lambda x: 0 if np.isnan(x).any() else len(x))
#%%
temp['Mapped_Properties'].notna().sum()
#%%
expload_tenant_df = temp.explode('Mapped_Properties')
#%%
expload_tenant_df.dropna(subset='Mapped_Properties')['ID'].nunique()
#%%
expload_tenant_df.rename(columns={'PropertyID':'AL_PropertyID'}, inplace=True)
#%%
print(type(prop_df))
#%%
prop_df = prop_df.to_pandas_df()
#%%
expload_tenant_property_df = pd.merge(expload_tenant_df,prop_df, how='inner', left_on='Mapped_Properties', right_on='PropertyID')
#%%
def fuzzy_match(row):
    similarity_score = fuzz.ratio(row['AddressStreetName'].strip().upper(), row['StreetName'].strip().upper())
    return similarity_score
#%%
expload_tenant_property_df['street_name_match_score'] = expload_tenant_property_df.parallel_apply(fuzzy_match, axis=1)
#%%
expload_tenant_property_df = expload_tenant_property_df[expload_tenant_property_df['street_name_match_score']>80]
#%%
expload_tenant_property_df.shape
#%%
def match_score(row):
    if pd.isna(row['Mapped_Properties']):
        return 0
    tenant_num_min = row['StreetNoMin']
    tenant_num_max = row['StreetNoMax']
    prop_num_min = int(row['StreetNumberMin'])
    prop_num_max = int(row['StreetNumberMax'])
    if (tenant_num_min==prop_num_min) and (tenant_num_max==prop_num_max):
        return 100
    elif (tenant_num_min==prop_num_min):
        return 95
    elif (tenant_num_max==prop_num_max):
        return 90
    else:
        return 0
#%%
expload_tenant_property_df['street_number_match_score'] = expload_tenant_property_df.parallel_apply(match_score, axis=1)
#%%
expload_tenant_property_df = expload_tenant_property_df[expload_tenant_property_df['street_number_match_score'] != 0]
#%%
expload_tenant_property_df['Suffix']=expload_tenant_property_df['Suffix'].apply(lambda x:x.strip().upper() if pd.notna(x) else x)
expload_tenant_property_df['SuffixName']=expload_tenant_property_df['SuffixName'].apply(lambda x:x.strip().upper() if pd.notna(x) else x)
#%%
temp = expload_tenant_property_df[expload_tenant_property_df['Suffix']==expload_tenant_property_df["SuffixName"]]
#%%
temp = temp[temp['Sequence'] == 1]
#%%
temp.shape

#%%
expload_tenant_property_df.shape
#%%
expload_tenant_property_df.dropna(subset='Mapped_Properties')['ID'].nunique()
#%%
expload_tenant_property_df.head()
#%%
copied = expload_tenant_property_df.copy()
#%%
req = temp.sort_values(['Sequence','CondoTypeName','street_number_match_score'],ascending=[True,True,False])
#%%
req[req.duplicated('ID', keep=False)][['ID','PropertyID','street_number_match_score','CondoTypeName','StreetName','AddressStreetName','StreetNoMin','StreetNumberMax','StreetNumberMin','StreetNoMax','Sequence']].head(100)
#%%
req.dropna(subset='Mapped_Properties')['ID'].nunique()
#%%
req = req[~req.duplicated(['ID'],keep=False)]
#%%
req.shape
#%%
req.to_csv("websites_propery_id_with_alternate_address(sequence=1).csv")
#%%
set2 = copied[~(copied['ID'].isin(req['ID']))]

#%%
set2.shape
#%%
set2_grouped = set2.groupby(['ID'])\
 .agg({
     'Mapped_Properties': lambda x: list(set(x))
 }).reset_index()
#%%
set2_grouped['No_of_Mapped_Properties'] = set2_grouped['Mapped_Properties'].apply(len)
#%%
set2_grouped.shape
#%%
mapped_props = set2_grouped[set2_grouped['No_of_Mapped_Properties'] == 1]
#%%
mapped_props.shape
#%%
set2_final = copied[copied['ID'].isin(mapped_props['ID'])]
#%%
set2_final[set2_final.duplicated(['ID'],keep=False)].shape
#%%
set2_final.drop_duplicates(subset="ID").shape
#%%
set2_final = set2_final.drop_duplicates(subset="ID")
#%%
set3 = pd.concat([set2_final,req])
#%%
set3.shape
#%%
set3.to_csv("websites_propery_id_with_alternate_address.csv",index=False)
#%%
