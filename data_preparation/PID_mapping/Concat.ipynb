#%%
import pandas as pd
import numpy as np
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
COLUMNS=['ID','TenantName','Provider','CEOTitle', 'ShopNumber', 'DomesticParentCountry', 'SICCode',
       'HQ_CompanyName', 'WebsiteURL', 'PrimarySIC2DigitDesc', 'EntityAge',
       'LegalStatus', 'EmployeeIndicator', 'GST_StatusFromDate',
       'ImmediateParentCountry', 'Fax', 'RegistrationOrIncorporationDate',
       'Latitude_x', 'LineOfBusiness', 'PostalCode', 'Longitude_x',
       'RevenueIndicator', 'SuffixAbbr', 'PrimarySICDivisionDesc',
       'OriginalAddress', 'Suffix',  'ASICEntityType', 'Address1_x',
        'StreetNo', 'FloorNumber', 'City', 'StreetNoMax',
       'EmployeeCount', 'NumberofMembersinHierarchy', 'GST_Status',
       'PrimarySIC2Digit', 'Revenue', 'PrimarySICDesc', 'SuiteNumber',
       'ImmediateParentName', 'HQ_ID', 'ABN', 'SuiteNumberDisplay',
       'ABN_StatusFromDate', 'CEOName', 'Address2_x',
       'GlobalUltimateParentName', 'GlobalUltimateParentDUNS', 'State',
       'ASICEntityStatus', 'StateAbbr', 'StreetName', 'StreetNoMin',
       'SubsidiaryCode', 'UnitNumber', 'Email', 'GlobalUltimateParentCountry',
       'PrimarySIC3Digit', 'ABNStatus', 'ANZSICCode', 'ASICEntityClass',
       'ImmediateParentDUNS', 'MarketableFlag', 'DomesticParentDUNS',
       'OfficePhone', 'TradingNames', 'PrimarySIC3DigitDesc',
       'DomesticParentName', 'PrimarySICDivision', 'ACN', 'CleanedAddress',
       'transform_created_date', 'transform_username', 'transform_commit',
       'GNAFAddressDetailPID', 'address_match_score', 'address_match_type',
       'AL_PropertyID', 'MasterPropertyID', 'verify_created_date',
       'verify_username', 'verify_commit', 'Mapped_Properties', 'PropertyID',
       'PropertyName', 'AddressStreetName', 'Address1_y', 'Address2_y',
       'StreetNumberMin', 'StreetNumberMax', 'CityName', 'CondoTypeName',
       'StateName', 'Longitude_y', 'Latitude_y', 'LGA']
#%%
df_il = pd.read_parquet('/home/<USER>/Downloads/Illion_unique_property_mapped_May_14.parquet')
#%%
df_il['Provider'] = 'Illion'
#%%
df_il[COLUMNS].info()
#%%

#%%
df_il['Suffix'].head()
#%%
# # Drop rows with NaN values in the 'Suffix' column
# df_il.dropna(subset=['Suffix'], inplace=True)

df_il[df_il['StreetName'].str.contains('little')]['StreetName']

#%%
df_zoom = pd.read_parquet('/home/<USER>/Downloads/ZoomInfo_unique_property_mapped_May_14.parquet')
#%%
df_zoom['Provider'] = 'Zoominfo'
#%%
df_zoom[list(set(COLUMNS)-set(df_zoom))] = np.nan
#%%
df_zoom[COLUMNS].info()
#%%

#%%
df_gl = pd.read_parquet('/home/<USER>/Downloads/google_unique_property_mapped_May_14.parquet')
#%%
df_gl['Provider'] = 'Google'
#%%
list(set(COLUMNS)-set(df_gl))
#%%
df_gl[list(set(COLUMNS)-set(df_gl))] = np.nan
#%%
df_gl[COLUMNS].info()
#%%
web_df = pd.read_parquet('/home/<USER>/Downloads/Websites_unique_property_mapped_May_14.parquet')
#%%
web_df['Provider'] = 'Website'
#%%
web_df[list(set(COLUMNS)-set(web_df))] = np.nan
#%%
web_df[COLUMNS].info()
#%%
final_df = pd.concat([df_il[COLUMNS],df_zoom[COLUMNS],df_gl[COLUMNS],web_df[COLUMNS]])
#%%
final_df.head()
#%%
final_df.to_csv('all_providers_PID_mapped_may_14.csv', index=False)
#%%
