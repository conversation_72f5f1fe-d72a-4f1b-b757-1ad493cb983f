#%%
import pandas as pd
import geopandas as gpd
from sqlalchemy import create_engine
from pandarallel import pandarallel
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
import yaml
from fuzzywuzzy import fuzz
pandarallel.initialize(progress_bar=True, nb_workers=8)
#%%
# Load configuration from config.yaml
with open('./config.yaml', 'r') as f:
    config = yaml.safe_load(f)
#%%
# Extract database connection details
source_db_config = config['source_db']
source_db_config
#%%
# Connection String
source_connection_string = f"mysql+mysqlconnector://{source_db_config['username']}:{source_db_config['password']}@{source_db_config['host']}:{source_db_config['port']}/{source_db_config['database_name']}"
source_database_engine = create_engine(source_connection_string)
#%%
sql_query = '''
select * from Empirical_DataStage.Tenant_Roster_CT;
'''
#%%
df = pd.read_sql(sql_query, con=source_database_engine)
#%%
df.info()
#%%
df['Tenant_Stage_Id'].nunique()
#%%
df[df['ConfirmedTenantID'].isna()].shape
#%%
df[(df['ConfirmedTenantID'].notna()) & (df['TenantName_CT'].isna())]['ConfirmedTenantID'].nunique()
#%%
req_df = df[df['TenantName_CT'].notna()]
#%%
req_df.info()
#%%
def match_names(row):
    matching_score = fuzz.token_sort_ratio(row['TenantName_Roster'].strip().upper(), row['TenantName_CT'].strip().upper())
    return matching_score
#%%
req_df['fuzzy_score'] = req_df.parallel_apply(match_names, axis=1)
#%%
req_df.head()
#%%
tenant_roster_ct_df = req_df[req_df['fuzzy_score'] >= 90]
#%%
tenant_roster_ct_df.info()
#%%
tenant_roster_ct_df[['Tenant_Stage_Id']].nunique()
#%%
tenant_roster_ct_df[tenant_roster_ct_df.duplicated(['Tenant_Stage_Id','ConfirmedTenantID'],keep=False)].sort_values('VendorID')
#%%
tenant_roster_ct_df[tenant_roster_ct_df['TenantName_Roster'].str.contains('Migration')]
#%%
tenant_roster_ct_df.to_csv('Tenant_Roster_CT_match.csv', index=False)
#%%
