#%%
import pandas as pd
import numpy as np
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
tenant_df = pd.read_parquet('./silver/tenant_df.parquet')
#%%
tenant_df.info()
#%%
tenant_df['TenantName'] = tenant_df['TenantName'].str.title()
#%%
tenant_df['VendorID'].nunique()
#%%
tenant_df['VendorID'].duplicated(keep=False).sum()
#%%
tenant_df[~tenant_df.duplicated('VendorID',keep=False)]['VendorID'].nunique()

#%%
23920 - 23329
#%%
tenant_df.head()
#%%
# tenant_df.to_sql('Silver_NSW_Raw_Confirmed_Tenants', database_engine, if_exists='replace', index=False, chunksize=10000)
#%%
tenant_df[tenant_df['VendorID']=='758447536'][['TenantName']]
#%%
tenant_ref_df = pd.read_parquet('./silver/tenant_ref_df.parquet')
#%%
tenant_ref_df.info()
#%%
tenant_ref_df.replace(to_replace='zoominfo', value=15, inplace=True)
tenant_ref_df.replace(to_replace='google_maps', value=17, inplace=True);
tenant_ref_df.replace(to_replace='overture', value=16, inplace=True);
#%%
tenant_ref_df['WebsiteURL'].unique()
#%%
tenant_ref_df.head()
#%%
# tenant_ref_df.to_sql('Silver_NSW_Raw_Confirmed_Tenants_Fields_Reference', database_engine, if_exists='replace', index=False, chunksize=10000)
#%%
tenant_ref_df.columns.to_list()
#%%
COLUMNS =[
 'ProviderName',
 'ProviderID', 
 'match_PropertyID', 
 'main_ID', 
 'main_PropertyID',
 'scenario',
 'match_count',
 'ABN',
 'ABNStatus',
 'ABN_StatusFromDate',
 'ACN',
 'ANZSICCode',
 'ASICEntityClass',
 'ASICEntityStatus',
 'ASICEntityType',
 'Address1',
 'Address2',
 'CEOName',
 'CEOTitle',
 'City',
 'Country',
 'DomesticParentCountry',
 'DomesticParentDUNS',
 'DomesticParentName',
 'Email',
 'EmployeeCount',
 'EmployeeIndicator',
 'EmployeesAtLocation',
 'EntityAge',
 'Fax',
 'GST_Status',
 'GST_StatusFromDate',
 'GlobalUltimateParentCountry',
 'GlobalUltimateParentDUNS',
 'GlobalUltimateParentName',
 'HQ_CompanyName',
 'HQ_ID',
 'ImmediateParentCountry',
 'ImmediateParentDUNS',
 'ImmediateParentName',
 'Latitude',
 'LegalStatus',
 'LineOfBusiness',
 'Longitude',
 'MarketableFlag',
 'NumberofMembersinHierarchy',
 'OfficePhone',
 'PostalCode',
 'PrimarySIC2Digit',
 'PrimarySIC2DigitDesc',
 'PrimarySIC3Digit',
 'PrimarySIC3DigitDesc',
 'PrimarySICDesc',
 'PrimarySICDivision',
 'PrimarySICDivisionDesc',
 'PropertyID',
 'RegistrationOrIncorporationDate',
 'Revenue',
 'RevenueIndicator',
 'SICCode',
 'State',
 'StateAbbr',
 'SubsidiaryCode',
 'TenantName',
 'TradingNames',
 'VendorID',
 'WebsiteURL',
 'match_ID',
 'main_name',
 'similarity_score',
 'match_name'
]
#%%
master_df = pd.read_json('./silver/master_record.json')
#%%
master_df.head()
#%%
master_df.info()
#%%
master_df.info()
#%%
len(master_df.columns.to_list())
#%%
master_df.columns.to_list()

#%%
master_df.shape
#%%
gl_columns = list(filter(lambda x: 'google' in x, master_df.columns.to_list()))
#%%
len(gl_columns)

#%%
ov_columns = list(filter(lambda x: 'overture' in x, master_df.columns.to_list()))
#%%
len(ov_columns)
#%%
zoom_columns = list(filter(lambda x: 'zoominfo' in x, master_df.columns.to_list()))
#%%
len(zoom_columns)
#%%
oth_columns = list(filter(lambda x: 'illion' not in x and 'overture' not in x and 'google' not in x and  'zoominfo' not in x, master_df.columns.to_list()))
#%%
len(oth_columns)
#%%
oth_columns
#%%
req_gl_columns = oth_columns + gl_columns
req_ov_columns = oth_columns + ov_columns
req_zoom_columns = oth_columns + zoom_columns


#%%
len(req_gl_columns)
#%%
len(req_zoom_columns)
#%%
len(req_ov_columns)
#%%
ov_df = master_df[req_ov_columns]
#%%
ov_df.head()
#%%
ov_df.info()
#%%
ov_df.columns = ov_df.columns.str.replace('_overture', '')
#%%
ov_df.rename(columns={'ID':'VendorID'},inplace=True)
#%%
ov_df.loc[:,'ProviderName'] = 'overture'
ov_df.loc[:,'ProviderID'] = 16
ov_df['TenantName'] = ov_df['TenantName'].str.title()
#%%
ov_df.loc[:,['ABN', 'ABNStatus', 'ABN_StatusFromDate', 'ACN', 'ANZSICCode', 'ASICEntityClass', 'ASICEntityStatus', 'ASICEntityType', 'Address1', 'CEOName', 'CEOTitle', 'DomesticParentCountry', 'DomesticParentDUNS', 'DomesticParentName', 'EmployeeCount', 'EmployeeIndicator', 'EmployeesAtLocation', 'EntityAge', 'Fax', 'GST_Status', 'GST_StatusFromDate', 'GlobalUltimateParentCountry', 'GlobalUltimateParentDUNS', 'GlobalUltimateParentName', 'HQ_CompanyName', 'HQ_ID', 'ImmediateParentCountry', 'ImmediateParentDUNS', 'ImmediateParentName', 'LegalStatus', 'LineOfBusiness', 'MarketableFlag', 'NumberofMembersinHierarchy', 'PrimarySIC2Digit', 'PrimarySIC2DigitDesc', 'PrimarySIC3Digit', 'PrimarySIC3DigitDesc', 'PrimarySICDesc', 'PrimarySICDivision', 'PrimarySICDivisionDesc', 'RegistrationOrIncorporationDate', 'Revenue', 'RevenueIndicator', 'SICCode', 'StateAbbr', 'SubsidiaryCode', 'TradingNames']] = np.nan 
#%%
ov_df[COLUMNS].head()
#%%
ov_df[ov_df['VendorID'].notna()][COLUMNS].shape
#%%
list(set(ov_df) - set(COLUMNS))
#%%
ov_df[ov_df['VendorID'].notna()][COLUMNS].to_csv('./silver_overture_set.csv',index=False)
#%%
gl_df = master_df[req_gl_columns]
#%%
gl_df.info()
#%%
gl_df.columns = gl_df.columns.str.replace('_google_maps', '')
#%%
gl_df.rename(columns={'ID':'VendorID'},inplace=True)
#%%
gl_df.loc[:,'ProviderName'] = 'google_maps'
gl_df.loc[:,'ProviderID'] = 17
gl_df['TenantName'] = gl_df['TenantName'].str.title()
#%%
gl_df.loc[:,['ABN', 'ABNStatus', 'ABN_StatusFromDate', 'ACN', 'ANZSICCode', 'ASICEntityClass', 'ASICEntityStatus', 'ASICEntityType', 'Address1', 'CEOName', 'CEOTitle', 'City', 'Country', 'DomesticParentCountry', 'DomesticParentDUNS', 'DomesticParentName', 'Email', 'EmployeeCount', 'EmployeeIndicator', 'EmployeesAtLocation', 'EntityAge', 'Fax', 'GST_Status', 'GST_StatusFromDate', 'GlobalUltimateParentCountry', 'GlobalUltimateParentDUNS', 'GlobalUltimateParentName', 'HQ_CompanyName', 'HQ_ID', 'ImmediateParentCountry', 'ImmediateParentDUNS', 'ImmediateParentName', 'Latitude', 'LegalStatus', 'LineOfBusiness', 'Longitude', 'MarketableFlag', 'NumberofMembersinHierarchy', 'PrimarySIC2Digit', 'PrimarySIC2DigitDesc', 'PrimarySIC3Digit', 'PrimarySIC3DigitDesc', 'PrimarySICDesc', 'PrimarySICDivision', 'PrimarySICDivisionDesc', 'RegistrationOrIncorporationDate', 'Revenue', 'RevenueIndicator', 'SICCode', 'State', 'StateAbbr', 'SubsidiaryCode', 'TradingNames']] = np.nan
#%%
gl_df[COLUMNS].head()
#%%
gl_df[gl_df['VendorID'].notna()][COLUMNS].shape
#%%
gl_df[gl_df['VendorID'].notna()][COLUMNS].to_csv('silver_google_maps_set.csv',index=False)
#%%
zoom_df = master_df[req_zoom_columns]
#%%
zoom_df.info()
#%%
zoom_df.columns = zoom_df.columns.str.replace('_zoominfo', '')
#%%
zoom_df.info()
#%%
zoom_df.rename(columns={'ID':'VendorID'},inplace=True)
#%%
zoom_df.loc[:,'ProviderName'] = 'zoominfo'
zoom_df.loc[:,'ProviderID'] = 15
zoom_df['TenantName'] = zoom_df['TenantName'].str.title()
#%%
list(set(zoom_df) - set(COLUMNS))
#%%
ZOOM_COLUMNS = COLUMNS + list(set(zoom_df) - set(COLUMNS))
#%%
list(set(zoom_df) - set(COLUMNS))
#%%
zoom_df[['DisplayName', 'CompanyID', 'ZI_C_RELEASE_DATE', 'ZI_C_LAST_UPDATED_DATE']].head()
#%%
zoom_df.loc[:,['ABN', 'ABNStatus', 'ABN_StatusFromDate', 'ACN', 'ANZSICCode', 'ASICEntityClass', 'ASICEntityStatus', 'ASICEntityType', 'CEOName', 'CEOTitle', 'Country', 'DomesticParentCountry', 'DomesticParentDUNS', 'DomesticParentName', 'Email', 'EmployeeIndicator', 'EmployeesAtLocation', 'GST_Status', 'GST_StatusFromDate', 'GlobalUltimateParentCountry', 'GlobalUltimateParentDUNS', 'GlobalUltimateParentName', 'HQ_CompanyName', 'ImmediateParentCountry', 'ImmediateParentDUNS', 'ImmediateParentName', 'LegalStatus', 'LineOfBusiness', 'NumberofMembersinHierarchy', 'PrimarySIC2DigitDesc', 'PrimarySIC3DigitDesc', 'PrimarySICDesc', 'PrimarySICDivision', 'PrimarySICDivisionDesc', 'RegistrationOrIncorporationDate', 'RevenueIndicator', 'StateAbbr', 'SubsidiaryCode','match_ID', 'main_name', 'similarity_score', 'match_name']] = np.nan
#%%
zoom_df[ZOOM_COLUMNS].head()
#%%
zoom_df[ZOOM_COLUMNS].info()
#%%
zoom_df[zoom_df['VendorID'].notna()].shape
#%%
zoom_df[zoom_df['VendorID'].notna()][ZOOM_COLUMNS].to_csv('silver_zoom_info_set.csv', index=False)
#%%
import pandas as pd
#%%
db_ov_df = pd.read_csv('./silver_overture_set.csv')
#%%
db_ov_df.shape
#%%
db_gl_df = pd.read_csv('./silver_google_maps_set.csv')
#%%
db_gl_df.shape
#%%
db_zoom_df = pd.read_csv('./silver_zoom_info_set.csv')
#%%
db_zoom_df.shape
#%%
# Ingesting the Data to MYSQL
# Import create_engine to create and manage database connection pool
from sqlalchemy import create_engine


username = 'xxxxxxxxxx'
password = 'xxxxxxxxxx'
host = 'localhost'
port = '4339'
database_name = 'Empirical_DataStage' # Change the database_name if required

# Connection String
connection_string = f'mysql+mysqlconnector://{username}:{password}@{host}:{port}/{database_name}'
#connection_string = f'mysql+mysqlconnector://{db_user}:{db_password}@{db_host}/{db_name}'
# Create the SQLAlchemy engine
database_engine = create_engine(connection_string)
#%%

# Silver_NSW_Raw_Overture_Tenants
# Silver_NSW_Raw_GoogleMaps_Tenants
# Silver_NSW_Raw_ZoomInfo_Tenants
#%%
db_zoom_df.to_sql('Silver_NSW_Raw_ZoomInfo_Tenants', database_engine, if_exists='replace', index=False, chunksize=10000)
#%%
