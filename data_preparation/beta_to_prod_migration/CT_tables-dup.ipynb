#%%
import pandas as pd
import numpy as np
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
COLUMNS_TITLE = [
'TenantName',
'CEOName',
'CEOTitle',
'ABNStatus',
'Address1',
'Address2',
'LineOfBusiness',
'LegalStatus',
'City',
'EmployeeIndicator',
'PrimarySIC2DigitDesc',
'PrimarySIC3DigitDesc',
'PrimarySICDesc',
'PrimarySICDivisionDesc',
'RevenueIndicator',
'ASICEntityClass',
'ASICEntityStatus',
'ASICEntityType',
'GlobalUltimateParentName',
'GlobalUltimateParentCountry',
'ImmediateParentName',
'ImmediateParentCountry',
'DomesticParentCountry',
'DomesticParentName']
#%%
CT_COLUMNS = [
'PropertyID',
'ConfirmedTenantID',
'TenantName',
'CompanyName',
'AlternateCompanyName',
'Address1',
'Address2',
'AddressStreetNumber',
'AddressStreetName',
'CityID',
'StateID',
'ZipCode',
'AddressText',
'OfficePhone',
'CompanyID',
'NationalID',
'Email',
'WebsiteUrl',
'FloorNumber',
'ANZSICCode',
'ABN',
'ACN'
]
#%%
stage_columns = ['ABNStatus',
 'ABN_StatusFromDate',
 'ASICEntityClass',
 'ASICEntityStatus',
 'ASICEntityType',
 'BatchID',
 'BranchID',
 'CEOName',
 'CEOTitle',
 'City',
 'CountryCode',
 'CreatedDate',
 'DomesticParentCountry',
 'DomesticParentDUNS',
 'DomesticParentName',
 'EmployeeIndicator',
 'EmployeesAtLocation',
 'EntityAge',
 'GST_Status',
 'GST_StatusFromDate',
 'GlobalUltimateParentCountry',
 'GlobalUltimateParentDUNS',
 'GlobalUltimateParentName',
 'HQ_CompanyName',
 'HQ_ID',
 'HideReasonComments',
 'HideReasonID',
 'HidedBy',
 'HidedDate',
 'ImmediateParentCountry',
 'ImmediateParentDUNS',
 'ImmediateParentName',
 'IsDefault',
 'IsDeleted',
 'IsHidden',
 'IsProcessed',
 'Latitude',
 'LegalStatus',
 'LineOfBusiness',
 'Longitude',
 'MatchingScore',
 'ModifiedBy',
 'ModifiedDate',
 'NACECode',
 'NAICSCode',
 'NumberofMembersinHierarchy',
 'ParentCompanyID',
 'PostalCode',
 'PrimarySIC2Digit',
 'PrimarySIC2DigitDesc',
 'PrimarySIC3Digit',
 'PrimarySIC3DigitDesc',
 'PrimarySICDesc',
 'PrimarySICDivision',
 'PrimarySICDivisionDesc',
 'ProviderID',
 'RegistrationOrIncorporationDate',
 'RevenueIndicator',
 'SICCode',
 'State',
 'StateAbbr',
 'StatusCode',
 'SubHideReasonID',
 'SubsidiaryCode',
 'Tenant_Stage_Id',
 'VendorID',
'Fax',
'Revenue',
'EmployeeCount',]
#%%
confirmed_COLUMNS = [
    "ABN_StatusFromDate",
    "ABN",
    "ABNStatus",
    "ACN",
    "Address1",
    "Address2",
    "ANZSICCode",
    "ASICEntityClass",
    "ASICEntityStatus",
    "ASICEntityType",
    "BranchID",
    "BatchID",
    "CEOName",
    "CEOTitle",
    "City",
    "Country",
    "CountryCode",
    "DomesticParentCountry",
    "DomesticParentDUNS",
    "DomesticParentName",
    "Email",
    "EmployeeCount",
    "EmployeeIndicator",
    "EmployeesAtLocation",
    "EntityAge",
    "Fax",
    "FloorNumber",
    "GlobalUltimateParentCountry",
    "GlobalUltimateParentDUNS",
    "GlobalUltimateParentName",
    "GST_Status",
    "GST_StatusFromDate",
    "HQ_CompanyName",
    "HQ_ID",
    "ImmediateParentCountry",
    "ImmediateParentDUNS",
    "ImmediateParentName",
    "IsProcessed",
    "Latitude",
    "LegalStatus",
    "LineOfBusiness",
    "Longitude",
    "MarketableFlag",
    "NationalID",
    "NumberofMembersinHierarchy",
    "OfficePhone",
    "PostalCode",
    "PrimarySIC2Digit",
    "PrimarySIC2DigitDesc",
    "PrimarySIC3Digit",
    "PrimarySIC3DigitDesc",
    "PrimarySICDesc",
    "PrimarySICDivision",
    "PrimarySICDivisionDesc",
    "PropertyID",
    "ProviderID",
    "Raw_Confirmed_TenantsID",
    "RegistrationOrIncorporationDate",
    "Revenue",
    "RevenueIndicator",
    "ShopNumber",
    "SICCode",
    "State",
    "StateAbbr",
    "SubsidiaryCode",
    "SuiteNumber",
    "SuiteNumberDisplay",
    "Tenant_Stage_ID",
    "TenantName",
    "TSI_Bucket",
    "TSI_Country",
    "TSI_State",
    "UnitNumber",
    "VendorID",
    "WebsiteURL",
    "IsSuiteandFloorProcessed",
    "FloorID",
    'CityID',
 'AddressText',
 'CompanyID',
 'ConfirmedTenantID',
 'ZipCode',
 'AddressStreetName',
 'StateID',
 'CompanyName',
 'AlternateCompanyName',
 'AddressStreetNumber',
 'ParentCompanyID'
]

#%%
matched_COLUMNS= [
    "ABN_StatusFromDate",
    "ABN",
    "ABNStatus",
    "ACN",
    'BranchID',
    "Address1",
    "Address2",
    "ANZSICCode",
    "ASICEntityClass",
    "ASICEntityStatus",
    "ASICEntityType",
    "CEOName",
    "CEOTitle",
    "City",
    "CountryCode",
    "DomesticParentCountry",
    "DomesticParentDUNS",
    "DomesticParentName",
    "Email",
    "EmployeeCount",
    "EmployeeIndicator",
    "EmployeesAtLocation",
    "EntityAge",
    "Fax",
    "GlobalUltimateParentCountry",
    "GlobalUltimateParentDUNS",
    "GlobalUltimateParentName",
    "GST_Status",
    "GST_StatusFromDate",
    "HQ_CompanyName",
    "HQ_ID",
    "ImmediateParentCountry",
    "ImmediateParentDUNS",
    "ImmediateParentName",
    "Latitude",
    "LegalStatus",
    "LineOfBusiness",
    "Longitude",
    "main_ID",
    "NationalID",
    "NumberofMembersinHierarchy",
    "OfficePhone",
    "PostalCode",
    "PrimarySIC2Digit",
    "PrimarySIC2DigitDesc",
    "PrimarySIC3Digit",
    "PrimarySIC3DigitDesc",
    "PrimarySICDesc",
    "PrimarySICDivision",
    "PrimarySICDivisionDesc",
    "PropertyID",
    "ProviderID",
    "ProviderName",
    "RegistrationOrIncorporationDate",
    "Revenue",
    "RevenueIndicator",
    "ShopNumber",
    "SICCode",
    "State",
    "StateAbbr",
    "SubsidiaryCode",
    "SuiteNumber",
    "SuiteNumberDisplay",
    "Tenant_Stage_ID",
    "TenantName",
    "TSI_Bucket",
    "TSI_Country",
    "TSI_State",
    "UnitNumber",
    "VendorID",
    "WebsiteURL",
]

#%%
refrence_COLUMNS= [
    "ABN_StatusFromDate",
    "ABN",
    "ABNStatus",
    "ACN",
    "Address1",
    "Address2",
    "ANZSICCode",
    "ASICEntityClass",
    "ASICEntityStatus",
    "ASICEntityType",
    "CEOName",
    "CEOTitle",
    "City",
    "Country",
    "DomesticParentCountry",
    "DomesticParentDUNS",
    "DomesticParentName",
    "Email",
    "EmployeeCount",
    "EmployeeIndicator",
    "EmployeesAtLocation",
    "EntityAge",
    "Fax",
    "FloorNumber",
    "GlobalUltimateParentCountry",
    "GlobalUltimateParentDUNS",
    "GlobalUltimateParentName",
    "GST_Status",
    "GST_StatusFromDate",
    "HQ_CompanyName",
    "HQ_ID",
    "ImmediateParentCountry",
    "ImmediateParentDUNS",
    "ImmediateParentName",
    "Latitude",
    "LegalStatus",
    "LineOfBusiness",
    "Longitude",
    "MarketableFlag",
    "NumberofMembersinHierarchy",
    "OfficePhone",
    "PostalCode",
    "PrimarySIC2Digit",
    "PrimarySIC2DigitDesc",
    "PrimarySIC3Digit",
    "PrimarySIC3DigitDesc",
    "PrimarySICDesc",
    "PrimarySICDivision",
    "PrimarySICDivisionDesc",
    "PropertyID",
    "RegistrationOrIncorporationDate",
    "Revenue",
    "RevenueIndicator",
    "ShopNumber",
    "SICCode",
    "State",
    "StateAbbr",
    "SubsidiaryCode",
    "SuiteNumber",
    "TenantName",
    "TSI_Bucket",
    "TSI_Country",
    "TSI_State",
    "UnitNumber",
    "VendorID",
    "WebsiteURL"
]
#%%
ct_duplicate_df = pd.read_csv('./rem_CT.csv')
#%%
ct_tenants_df = pd.read_csv('./ConfirmedTenants_Beta.csv')
#%%
ct_tenants_df = ct_tenants_df[ct_tenants_df['ConfirmedTenantID'].isin(ct_duplicate_df['ConfirmedTenantID'])]
#%%
ct_tenants_df.shape
#%%
ct_tenants_df[ct_tenants_df['CompanyID'].duplicated()].shape
#%%
ct_tenants_df = ct_tenants_df[~ct_tenants_df['CompanyID'].duplicated()]
#%%
ct_tenants_df.info()
#%%
ct_tenants_df = ct_tenants_df[CT_COLUMNS]
#%%
ct_tenants_df.rename(columns={'WebsiteUrl':'WebsiteURL','CountryID':'CountryCode'}, inplace=True)
#%%
list(set(ct_tenants_df) - set(confirmed_COLUMNS))
#%%
tenant_stage_df = pd.read_csv('./VIC_COM_tenant_stage.csv', low_memory=False)
#%%
tenant_stage_df = tenant_stage_df.sort_values('ProviderID')
#%%
tenant_stage_df.head()

#%%
tenant_stage_df.info()
#%%
sorted(set(tenant_stage_df)-set(ct_tenants_df))
#%%
tenant_stage_df[tenant_stage_df['ProviderID'] == 14].shape
#%%
tenant_stage_req_df = tenant_stage_df[tenant_stage_df['ProviderID'] != 14][stage_columns]
#%%
tenant_stage_req_df.shape
#%%
tenant_stage_req_df.info()
#%%
confirmed_tenant_df =  tenant_stage_req_df.drop_duplicates('BranchID', keep='first')
#%%
confirmed_tenant_df.shape
#%%
confirmed_tenant_df[confirmed_tenant_df.duplicated('ParentCompanyID',keep=False)].shape
#%%
confirmed_tenant_df[confirmed_tenant_df.duplicated('BranchID',keep=False)].shape
#%%
confirmed_tenant_df['ParentCompanyID'].value_counts()
#%%
confirmed_tenant_df[(confirmed_tenant_df.duplicated('ParentCompanyID',keep=False)) |(confirmed_tenant_df.duplicated('BranchID',keep=False))].shape
#%%
confirmed_tenant_df[confirmed_tenant_df.duplicated('BranchID',keep=False)].shape
#%%
confirmed_tenant_df.shape
#%%
confirmed_tenant_table = pd.merge(ct_tenants_df,confirmed_tenant_df ,left_on='CompanyID',right_on='BranchID')
#%%
confirmed_tenant_table.shape
#%%
confirmed_tenant_table.VendorID.nunique()
#%%
confirmed_tenant_table[confirmed_tenant_table.duplicated('VendorID',keep=False)].shape
#%%
confirmed_tenant_table['TSI_Bucket']="Gold"
confirmed_tenant_table['TSI_Country']="Australia"
confirmed_tenant_table['TSI_State']=confirmed_tenant_table['StateAbbr']

#%%
list(set(confirmed_COLUMNS)- set(confirmed_tenant_table))
#%%
list(set(confirmed_tenant_table)- set(confirmed_COLUMNS))
#%%
import numpy as np
confirmed_tenant_table['IsProcessed'] = 0
confirmed_tenant_table['BatchID'] = np.nan
#%%
confirmed_tenant_table[list(set(confirmed_COLUMNS)- set(confirmed_tenant_table))] = np.nan
#%%
confirmed_tenant_table[confirmed_COLUMNS].info()
#%%
confirmed_tenant_table = confirmed_tenant_table[confirmed_COLUMNS]
#%%
reference = confirmed_tenant_table.copy()
#%%
ref_not_modify_columns = ['ProviderID','VendorID','TSI_State','TSI_Bucket','TSI_Country']
#%%
# Function to add ProviderID to non-null values
def add_provider_id(row):
    for col in reference.columns:
        if col not in  ref_not_modify_columns and pd.notnull(row[col]):
            row[col] = row['ProviderID']
    return row

# Apply the function to each row
reference_table = reference.apply(add_provider_id, axis=1)
#%%
reference_table[refrence_COLUMNS].head()
#%%
reference_table.shape
#%%
matched_tenants = tenant_stage_df.copy()[tenant_stage_df['BranchID'].isin(confirmed_tenant_table['BranchID'])]
#%%
matched_tenants.shape
#%%
matched_tenants.to_csv('./beta_tenant_stage.csv', index=False)
#%%
matched_tenants['TSI_Bucket']="Gold"
matched_tenants['TSI_Country']="Australia"
matched_tenants['TSI_State']=matched_tenants['StateAbbr']
#%%
matched_tenants_table = pd.merge(matched_tenants,confirmed_tenant_table[['VendorID','BranchID']], on='BranchID', how='left')
#%%
matched_tenants_table.rename(columns={'VendorID_x':'VendorID','VendorID_y':'main_ID'}, inplace=True)
#%%
matched_tenants_table.shape
#%%
list(set(matched_COLUMNS) - set(matched_tenants_table))

#%%
matched_tenants_table[list(set(matched_COLUMNS) - set(matched_tenants_table))] = np.nan
#%%
# Apply title case to the specified columns
for column in COLUMNS_TITLE:
    confirmed_tenant_table[column] = confirmed_tenant_table[column].str.title()
    matched_tenants_table[column] = matched_tenants_table[column].str.title()
#%%
confirmed_tenant_table[confirmed_COLUMNS].info()
#%%
matched_tenants_table[matched_COLUMNS].info()
#%%
def stats_gen(provider,tenant_df):
    # Create a dictionary to store summary statistics
    summary_stats = {
        'Column': [],
        f'Non-Null Count in {provider}_tenant_df': [],
        'Data Type': [],
        
    }
    
    # Fill the dictionary with summary statistics for each column
    for column in tenant_df.columns:
        summary_stats['Column'].append(column)
        summary_stats[f'Non-Null Count in {provider}_tenant_df'].append(tenant_df[column].count())
        summary_stats['Data Type'].append(tenant_df[column].dtype)
    
    # Create a new DataFrame from the summary statistics dictionary
    summary_df = pd.DataFrame(summary_stats)
    return summary_df

#%%
matched_tenants_table_stats = stats_gen('matched_tenants',matched_tenants_table.sort_index(axis=1))
reference_table_stats = stats_gen('reference_tenants',reference_table.sort_index(axis=1))
confirmed_tenants_table_stats = stats_gen('confirmed_tenants',confirmed_tenant_table.sort_index(axis=1))

#%%
matched_tenants_table_stats.to_csv('./stats/matched_tenants_table_stats.csv', index=False)
reference_table_stats.to_csv('./stats/eference_tenants_table_stats.csv', index=False)
confirmed_tenants_table_stats.to_csv('./stats/confirmed_tenants_table_stats.csv', index=False)
#%%
matched_tenants_table_stats.head()
#%%
confirmed_tenant_table[confirmed_COLUMNS].head()
#%%
reference_table[refrence_COLUMNS].head()
#%%
5869+1687
#%%
# Ingesting the Data to MYSQL
# Import create_engine to create and manage database connection pool
from sqlalchemy import create_engine


username = 'xxxxxxxxxx'
password = 'xxxxxxxxxx'
host = 'localhost'
port = '4339'
database_name = 'Empirical_DataStage' # Change the database_name if required

# Connection String
connection_string = f'mysql+mysqlconnector://{username}:{password}@{host}:{port}/{database_name}'
#connection_string = f'mysql+mysqlconnector://{db_user}:{db_password}@{db_host}/{db_name}'
# Create the SQLAlchemy engine
database_engine = create_engine(connection_string)
#%%
# matched_tenants_table
# reference_table
# confirmed_tenant_table

# Raw_Matched_Tenants_2024_06_10
# Raw_Confirmed_Tenants_2024_06_10
# Raw_Confirmed_Tenants_Fields_Reference_2024_06_10

#%%
# Push the DataFrame to the MySQL database
table_name = 'Raw_Confirmed_Tenants_Fields_Reference_2024_06_10'  # Replace with the desired table name in your database
reference_table[refrence_COLUMNS].to_sql(table_name, con=database_engine, if_exists='append', index=False)
#%%
