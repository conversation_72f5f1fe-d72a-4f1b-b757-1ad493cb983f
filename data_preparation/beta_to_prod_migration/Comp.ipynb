#%%
import pandas as pd
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
CT_COLUMNS = [
'PropertyID',
'VendorID',
'TenantName',
'CompanyName',
'Address1',
'Address2',
'AddressStreetNumber',
'AddressStreetName',
'CityID',
'StateID',
'ZipCode',
'AddressText',
'OfficePhone',
'NationalID',
'Email',
'WebsiteURL',
'FloorNumber',
'ANZSICCode',
'ABN',
'ACN'
]
#%%
COLUMNS_TITLE = ['TenantName','CompanyName','Address1','Address2','AddressStreetName','AddressText']
#%%
ct_beta_df = pd.read_csv('/home/<USER>/Downloads/beta_dup_confirmed_tenants.csv')
#%%
ct_beta_df = ct_beta_df[CT_COLUMNS]
#%%
ct_beta_df[CT_COLUMNS].info()
#%%
ct_beta_df[ct_beta_df['NationalID'].notna()][['NationalID','ABN','ACN']]
#%%
ct_uat_df = pd.read_csv('/home/<USER>/Downloads/UAT_dup_confirmed_tenants.csv')
#%%
ct_uat_df.info()
#%%
ct_uat_df.rename(columns={'WebsiteUrl':'WebsiteURL'}, inplace=True)
#%%
ct_uat_df = ct_uat_df[CT_COLUMNS]
#%%
# Apply title case to the specified columns
for column in COLUMNS_TITLE:
    ct_beta_df[column] = ct_beta_df[column].str.lower()
    ct_uat_df[column] = ct_uat_df[column].str.lower()
#%%
ct_df = pd.merge(ct_uat_df,ct_beta_df, on='VendorID')
#%%
ct_df.info()
#%%
ct_df['VendorID'].nunique()
#%%
ct_df.shape
#%%
equal_columns = []
#%%
# Iterate through each column pair with '_x' and '_y' suffixes
for col_x, col_y in zip(ct_df.columns[ct_df.columns.str.endswith('_x')], ct_df.columns[ct_df.columns.str.endswith('_y')]):
    print(f'{col_x}:{col_y}')
    # Create a new column name for the result
    result_col_name = col_x[:-2] + '_equal'
    equal_columns.append(result_col_name)
    # Determine the fill value based on the dtype of the columns
    fill_value = 0  # Use 0 for numeric columns, '' for others
    # Perform the comparison after filling missing values and assign the result to the new column
    ct_df[result_col_name] = (ct_df[col_x].fillna(fill_value) == ct_df[col_y].fillna(fill_value)).astype(int)
#%%
ct_df.columns[ct_df.columns.str.endswith('_equal')].to_list()
#%%
len(equal_columns)
#%%
ct_df['result'] = ct_df[equal_columns].sum(axis=1)
#%%
max(ct_df['result'])
#%%
(ct_df['result'] == 19).sum()
#%%
ct_df.columns = [col.replace('_x', '_uat').replace('_y', '_beta').lower() for col in ct_df.columns]
#%%
ct_df[ct_df['result'] == 18][['addressstreetnumber_uat','addressstreetnumber_beta']].info()
#%%
ct_df[ct_df['addressstreetname_equal'] == 0][['addressstreetname_uat','addressstreetname_beta']].head(100)
#%%
len(equal_columns)
#%%

ct_df_analy = ct_df[ct_df.columns[ct_df.columns.str.endswith('_equal')].to_list()]
#%%
# Step 3: Create a dictionary mapping old column names to new column names
new_column_names = {col: col.replace('_equal', '_not_equal') for col in ct_df_analy.columns if '_equal' in col}

# Step 4: Use the rename method to update the column names
ct_df_analy = ct_df_analy.rename(columns=new_column_names)
#%%
import matplotlib.pyplot as plt

# Calculate the sum of each column
column_sums = ((ct_df_analy == 0).sum())

# Plot the bar graph
plt.figure(figsize=(10, 10))
column_sums.plot(kind='bar', color='skyblue')
plt.title('Unmatched columns')
plt.xlabel('Columns')
plt.ylabel('percentage')
plt.xticks(rotation=90)
# Save the plot as an image file
plt.savefig('not_equal_counts_plot.png')
plt.show()
#%%
import matplotlib.pyplot as plt

# Calculate the sum of each column
column_sums = ((ct_df_analy == 0).sum()/ct_df_analy.shape[0])*100

# Plot the bar graph
plt.figure(figsize=(10, 10))
column_sums.plot(kind='bar', color='skyblue')
plt.title('Unmatched columns')
plt.xlabel('Columns')
plt.ylabel('percentage')
plt.xticks(rotation=90)
# Save the plot as an image file
plt.savefig('not_equal_counts_plot.png')
plt.show()
#%%
ct_df.to_csv('comp_beta_dup_branchID_uat_CT.csv', index=False)
#%%
