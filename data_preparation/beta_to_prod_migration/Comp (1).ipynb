#%%
import pandas as pd
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
# CT_COLUMNS = [
# 'PropertyID',
# 'VendorID',
# 'TenantName',
# # 'CompanyName',
# 'Address1',
# 'Address2',
# # 'AddressStreetNumber',
# # 'AddressStreetName',
# # 'CityID',
# # 'StateID',
# # 'ZipCode',
# # 'AddressText',
# 'OfficePhone',
# 'NationalID',
# 'Email',
# 'WebsiteURL',
# # 'FloorNumber',
# 'ANZSICCode',
# # 'ABN',
# # 'ACN'
# ]
#%%
Stage_columns=['VendorID', 'TenantName', 'Address1', 'Address2', 'City', 'State',
       'StateAbbr', 'PostalCode', 'NationalID', 'OfficePhone',
       'Fax', 'CEOName', 'CEOTitle', 'LineOfBusiness', 'SICCode', 'Revenue',
       'EmployeesAtLocation', 'EmployeeCount', 'LegalStatus', 'StatusCode',
       'SubsidiaryCode', 'PropertyID',
       'MatchingScore',
       'Latitude', 'Longitude',
       'ProviderID', 'NAICSCode', 'NACECode', 'Email',
       'WebsiteURL', 'ASICEntityStatus',
       'ASICEntityType', 'ASICEntityClass', 'ABNStatus', 'ABN_StatusFromDate',
       'GST_Status', 'GST_StatusFromDate',
       'EntityAge', 'EmployeeIndicator', 'RevenueIndicator', 'HQ_ID',
       'HQ_CompanyName', 'NumberofMembersinHierarchy', 'ImmediateParentDUNS',
       'ImmediateParentName', 'ImmediateParentCountry', 'DomesticParentDUNS',
       'DomesticParentName', 'DomesticParentCountry',
       'GlobalUltimateParentDUNS', 'GlobalUltimateParentName',
       'GlobalUltimateParentCountry', 'PrimarySICDesc', 'PrimarySIC3Digit',
       'PrimarySIC3DigitDesc', 'PrimarySIC2Digit', 'PrimarySIC2DigitDesc',
       'PrimarySICDivision', 'PrimarySICDivisionDesc', 'SubHideReasonID',
       'ANZSICCode']
#%%
len(Stage_columns)
#%%
# COLUMNS_TITLE = ['TenantName','CompanyName','Address1','Address2','AddressStreetName','AddressText']
#%%
# COLUMNS_TITLE = ['TenantName','Address1','Address2']
#%%
ct_beta_df = pd.read_parquet('./beta_tenant_stage.parquet')
#%%
ct_beta_df.columns
#%%
ct_beta_df = ct_beta_df[Stage_columns]
#%%
string_columns=[]
#%%
import numpy as np
#%%
def is_object_dtype(column):
    return isinstance(column.dtype, np.dtype) and column.dtype == np.object_
#%%
for column_name in ct_beta_df.columns:
    if is_object_dtype(ct_beta_df[column_name]):
       string_columns.append(column_name)
string_columns
#%%
# for i in Stage_columns:
#     # print(isinstance(ct_beta_df[i][0],str))
#     string_columns.append(i)
#     # print(type(ct_beta_df[i][0]))
#%%
string_columns
#%%
ct_beta_df[Stage_columns].info()
#%%
ct_uat_df = pd.read_parquet('./uat_tenant_stage.parquet')
#%%
ct_uat_df.shape
#%%

ct_beta_df['VendorID'].nunique()
#%%
ct_uat_df['VendorID'].nunique()
#%%
ct_uat_df[(ct_uat_df['ProviderID']==14)].shape
#%%
ct_uat_df=ct_uat_df[~(ct_uat_df['ProviderID']==14)]
#%%
ct_uat_df[ct_uat_df.duplicated('VendorID')].sort_values(by='VendorID').head()
#%%
ct_uat_df[ct_uat_df['VendorID'].isin(ct_beta_df['VendorID'])].shape
#%%
# ct_beta_df['CountryCode'].unique()
#%%
ct_uat_df['CountryCode'].unique()
#%%
ct_uat_df.info()
#%%
ct_uat_df.rename(columns={'WebsiteUrl':'WebsiteURL'}, inplace=True)
#%%
ct_uat_df = ct_uat_df[Stage_columns]
#%%
ct_beta_df.columns
#%%
ct_uat_df.columns
#%%
# string_columns.remove('CountryCode')
#%%
# Apply title case to the specified columns
for column in string_columns:
    print(column)
    ct_beta_df[column] = ct_beta_df[column].str.lower()
    ct_uat_df[column] = ct_uat_df[column].str.lower()

#%%
ct_df = pd.merge(ct_uat_df,ct_beta_df, on='VendorID')
#%%
ct_df.info()
#%%
ct_df.shape
#%%
equal_columns = []
#%%
# Iterate through each column pair with '_x' and '_y' suffixes
for col_x, col_y in zip(ct_df.columns[ct_df.columns.str.endswith('_x')], ct_df.columns[ct_df.columns.str.endswith('_y')]):
    print(f'{col_x}:{col_y}')
    # Create a new column name for the result
    result_col_name = col_x[:-2] + '_equal'
    equal_columns.append(result_col_name)
    # Determine the fill value based on the dtype of the columns
    fill_value = 0  # Use 0 for numeric columns, '' for others
    # Perform the comparison after filling missing values and assign the result to the new column
    ct_df[result_col_name] = (ct_df[col_x].fillna(fill_value) == ct_df[col_y].fillna(fill_value)).astype(int)
#%%
equal_columns
#%%
len(equal_columns)
#%%
ct_df[equal_columns].head()
#%%
ct_df['result'] = ct_df[equal_columns].sum(axis=1)
#%%
# ct_df['result']
#%%
max(ct_df['result'])
#%%
for i in range(61):
    print(i,(ct_df['result'] == i).sum())
#%%
(ct_df['result'] == 60).sum()
#%%
ct_df.columns = [col.replace('_x', '_uat').replace('_y', '_beta') for col in ct_df.columns]
#%%
ct_df.columns.tolist()
#%%
res = ct_df[ct_df['result'] == 60]
#%%
dict1={}
for i in equal_columns:
    dict1[i]=res[i].sum()
dict1
#%%
ct_df[ct_df['result'] == 59].head(100)
#%%
ct_df.shape
#%%
ct_df.to_csv('comp_beta_uat_TenantSatge.csv', index=False)
#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%
