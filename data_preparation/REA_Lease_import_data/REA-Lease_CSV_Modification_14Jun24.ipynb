#%% md
# rea_qld_Lease csv Modification
#%%
import pandas as pd
import numpy as np
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
USearch_columns = ['PropertyID',
 'Property Name',
 'Address',
 'City',
 'State',
 'Zip',
 'Alt Tenant Name',
 'Level',
 'Suite',
 'Leased Sqm',
 'Commencing Date',
 'Expiry Date',
 'Face Rent - Annual',
 'Lease Rate Type',
 'Outgoings (per sqm)',
 'Escalation Rate %',
 'Term (months)',
 'Renewal Options (No. of Months)',
 'Recorded Date',
 'Lease/Sublease',
 'Lease Trans Type',
 'Transaction Comments',
 'Suite Comments',
 'Execution Date',
 'Space General Use',
 'Lease Status',
 'ConfirmationNotes',
 'SignDate',
 'BankGuaranteeAmount',
 'GSTIncluded',
 'IsCondo',
 'Fitout',
 'TransactionOriginationTypeID',
 'IsStartingRateConfirmed',
 'ShareLevelID',
 'IsExpDateConfirmed',
 'EscalationType',
 'IsTermsConfirmed',
 'ConfirmedTenantID',
 'DealingNumber',
 'IsOccupDateConfirmed',
 'RentFreePeriod',
 'NoticePeriodMin',
 'NoticePeriodMax',
 'ListingTypeID',
 'Folio',
 'StageID',
 'Status',
 'ExceptionReason',
 'LeaseID',
 'IsActive',
 'BatchID',
 'Latitude',
 'Longitude',
 'Location',
 'ProviderID',
 'UID',
 'Url',
 'AgentName1',
 'AgentEmail1',
 'AgentName2',
 'AgentEmail2',
 'CreatedDate',
 'CreatedBy',
 'HideReasonID',
 'MismatchedTaggedBy',
 'HidedBy',
 'ModifiedBy',
 'IsHidden',
 'HidedDate',
 'IsMismatched',
 'SubHideReasonID',
 'ModifiedDate',
 'HideReasonComments']

#%%
LeaseComps_Stage_REA_2024_06_14 = pd.read_csv('rea_qld_14062024_ingest_phase_1.csv')

#%%
LeaseComps_Stage_REA_2024_06_14.shape
#%%
LeaseComps_Stage_REA_2024_06_14.columns
#%%
LeaseComps_Stage_REA_2024_06_14.rename(columns={'PropertyName':"Property Name",'AltTenantName':'Alt Tenant Name','LeasedSqm':'Leased Sqm','CommencingDate':'Commencing Date','ExpiryDate':'Expiry Date','FaceRentAnnual':'Face Rent - Annual','LeaseRateType':'Lease Rate Type','OutgoingsPerSqm':'Outgoings (per sqm)','EscalationRate':'Escalation Rate %','TermMonths':'Term (months)','RenewalOptionsNoOfMonths':'Renewal Options (No. of Months)','RecordedDate':'Recorded Date','LeaseSublease':'Lease/Sublease','LeaseTransType':'Lease Trans Type','TransactionComments':'Transaction Comments','SuiteComments':'Suite Comments','ExecutionDate':'Execution Date','SpaceGeneralUse':'Space General Use','LeaseStatus':'Lease Status','folio':'Folio'}, inplace=True)
#%%
list(set(LeaseComps_Stage_REA_2024_06_14.columns).intersection(USearch_columns))
#%%
list(set(LeaseComps_Stage_REA_2024_06_14)-set(USearch_columns))
#%%
list(set(USearch_columns)-set(LeaseComps_Stage_REA_2024_06_14.columns))
#%%
LeaseComps_Stage_REA_2024_06_14[list(set(USearch_columns)-set(LeaseComps_Stage_REA_2024_06_14.columns))] = np.nan
#%%
LeaseComps_Stage_REA_2024_06_14.shape
#%%
LeaseComps_Stage_REA_2024_06_14.shape
#%%
from datetime import datetime
LeaseComps_Stage_REA_2024_06_14['ProviderID'] = 22
LeaseComps_Stage_REA_2024_06_14['CreatedBy']=22
LeaseComps_Stage_REA_2024_06_14['CreatedDate'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
LeaseComps_Stage_REA_2024_06_14['BatchID'] = 240624

#%%
LeaseComps_Stage_REA_2024_06_14.shape
#%%
LeaseComps_Stage_REA_2024_06_14.head(100)
#%%
LeaseComps_Stage_REA_2024_06_14[USearch_columns].head()
#%%
LeaseComps_Stage_REA_2024_06_14[USearch_columns].info()
#%%
# Ingesting the Data to MYSQL
# Import create_engine to create and manage database connection pool
from sqlalchemy import create_engine

#db_user = 'root'
#db_password = 'mysql'
#db_host = 'localhost'
#db_name = 'geopandas'

username = '*'
password = '*'
host = 'localhost'
# port = '3379'
port = '3396'
# port = '3393'


database_name = 'Empirical_DataStage' # Change the database_name if required

# Connection String
connection_string = f'mysql+mysqlconnector://{username}:{password}@{host}:{port}/{database_name}'
#connection_string = f'mysql+mysqlconnector://{db_user}:{db_password}@{db_host}/{db_name}'
# Create the SQLAlchemy engine
database_engine = create_engine(connection_string)
#%%
LeaseComps_Stage_REA_2024_06_14[USearch_columns].to_sql('LeaseComp_Stage_For_Grouping', database_engine, if_exists='append', index=False,chunksize=10000)
#%%

#%%
