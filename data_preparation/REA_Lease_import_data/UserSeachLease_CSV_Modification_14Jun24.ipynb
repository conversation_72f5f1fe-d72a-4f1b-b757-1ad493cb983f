#%% md
# UserSearch CSV Modifiction
#%%
import pandas as pd
import numpy as np
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
USearch_columns = ['PropertyID',
 'Property Name',
 'Address',
 'City',
 'State',
 'Zip',
 'Alt Tenant Name',
 'Level',
 'Suite',
 'Leased Sqm',
 'Commencing Date',
 'Expiry Date',
 'Face Rent - Annual',
 'Lease Rate Type',
 'Outgoings (per sqm)',
 'Escalation Rate %',
 'Term (months)',
 'Renewal Options (No. of Months)',
 'Recorded Date',
 'Lease/Sublease',
 'Lease Trans Type',
 'Transaction Comments',
 'Suite Comments',
 'Execution Date',
 'Space General Use',
 'Lease Status',
 'ConfirmationNotes',
 'SignDate',
 'BankGuaranteeAmount',
 'GSTIncluded',
 'IsCondo',
 'Fitout',
 'TransactionOriginationTypeID',
 'IsStartingRateConfirmed',
 'ShareLevelID',
 'IsExpDateConfirmed',
 'EscalationType',
 'IsTermsConfirmed',
 'ConfirmedTenantID',
 'DealingNumber',
 'IsOccupDateConfirmed',
 'RentFreePeriod',
 'NoticePeriodMin',
 'NoticePeriodMax',
 'ListingTypeID',
 'Folio',
 'StageID',
 'Status',
 'ExceptionReason',
 'LeaseID',
 'IsActive',
 'BatchID',
 'Latitude',
 'Longitude',
 'Location',
 'ProviderID',
 'UID',
 'Url',
 'AgentName1',
 'AgentEmail1',
 'AgentName2',
 'AgentEmail2',
 'CreatedDate',
 'CreatedBy',
 'HideReasonID',
 'MismatchedTaggedBy',
 'HidedBy',
 'ModifiedBy',
 'IsHidden',
 'HidedDate',
 'IsMismatched',
 'SubHideReasonID',
 'ModifiedDate',
 'HideReasonComments']

#%%
USearchdf = pd.read_csv('usearch_trusted_leases.lease_snapshot_bq-results-********-132100-*************.csv')
#%%
USearchdf.head()
#%%
USearchdf.shape
#%%
USearchdf.columns
#%%
USearchdf.shape
#%%
USearchdf.rename(columns={'Property_Name':"Property Name",'Alt_Tenant_Name':'Alt Tenant Name','Leased_Sqm':'Leased Sqm','Commencing_Date':'Commencing Date','Outgoings _per sqm_':'Outgoings (per sqm)','EscalationRate':'Escalation Rate %','Term _months_':'Term (months)','Renewal Options _No_ of Months_':'Renewal Options (No. of Months)','RecordedDate':'Recorded Date','Lease_Sublease':'Lease/Sublease','LeaseTransType':'Lease Trans Type','TransactionComments':'Transaction Comments','SuiteComments':'Suite Comments','ExecutionDate':'Execution Date','SpaceGeneralUse':'Space General Use','LeaseStatus':'Lease Status','folio':'Folio','Lat':'Latitude','Long':'Longitude','ProviderName':'ProviderID'}, inplace=True)
#%%
from datetime import datetime
USearchdf['CreatedDate']=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
USearchdf['CreatedBy'] = 22
USearchdf['BatchID'] = 240634
#%%
list(set(USearch_columns)-set(USearchdf))
#%%
list(set(USearchdf)-set(USearch_columns))
#%%
USearchdf[list(set(USearch_columns)-set(USearchdf))] = np.nan
#%%
# USearchdf.drop(columns=['Metadata'],inplace=True)
#%%
list(set(USearch_columns)-set(USearchdf))
#%%
USearchdf.shape
#%%
USearchdf.shape
#%%
USearchdf.info()
#%%
USearchdf[USearch_columns].head()
#%%
USearchdf['ProviderID'].unique()
#%%
USearchdf[USearch_columns].shape
#%%
# Ingesting the Data to MYSQL
# Import create_engine to create and manage database connection pool
from sqlalchemy import create_engine

#db_user = 'root'
#db_password = 'mysql'
#db_host = 'localhost'
#db_name = 'geopandas'

username = 'imperium_admin'
password = 'CoVayKfSgNgq6n8HJU4O'
host = 'localhost'
# port = '3379'
port = '3396'
# port = '3391'


database_name = 'Empirical_DataStage' # Change the database_name if required

# Connection String
connection_string = f'mysql+mysqlconnector://{username}:{password}@{host}:{port}/{database_name}'
#connection_string = f'mysql+mysqlconnector://{db_user}:{db_password}@{db_host}/{db_name}'
# Create the SQLAlchemy engine
database_engine = create_engine(connection_string)
#%%
USearchdf[USearch_columns].to_sql('LeaseComp_Stage_For_Grouping', database_engine, if_exists='append', index=False,chunksize=10000)
#%%
