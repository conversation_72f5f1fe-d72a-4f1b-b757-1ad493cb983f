#%%
import numpy as np
import pandas as pd
from datetime import date
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
def stats_gen(provider,tenant_df):
    # Create a dictionary to store summary statistics
    summary_stats = {
        'Column': [],
        f'Non-Null Count in {provider}_tenant_df': [],
        'Data Type': [],
        
    }
    
    # Fill the dictionary with summary statistics for each column
    for column in tenant_df.columns:
        summary_stats['Column'].append(column)
        summary_stats[f'Non-Null Count in {provider}_tenant_df'].append(tenant_df[column].count())
        summary_stats['Data Type'].append(tenant_df[column].dtype)
    
    # Create a new DataFrame from the summary statistics dictionary
    summary_df = pd.DataFrame(summary_stats)
    return summary_df

#%%
COLUMNS =[
 'ABN',
 'ACN',
 'VendorID',
 'TenantName',
 'Address1',
 'Address2',
 'City',
 'State',
 'StateAbbr',
 'CountryCode',
 'PostalCode',
 'NationalID',
 'OfficePhone',
 'Fax',
 'CEOName',
 'CEOTitle',
 'LineOfBusiness',
 'SICCode',
 'Revenue',
 'EmployeesAtLocation',
 'EmployeeCount',
 'LegalStatus',
 'StatusCode',
 'SubsidiaryCode',
 'IsProcessed',
 'ConfirmedTenantID',
 'PropertyID',
 'MatchingScore',
 'CreatedDate',
 'ModifiedDate',
 'Latitude',
 'Longitude',
 'ParentCompanyID',
 'BranchID',
 'BatchID',
 'ProviderID',
 'Provider',
 'IsDefault',
 'NAICSCode',
 'NACECode',
 'Email',
 'WebsiteURL',
 'ModifiedBy',
 'IsHidden',
 'IsDeleted',
 'HidedBy',
 'HidedDate',
 'HideReasonID',
 'HideReasonComments',
 'ASICEntityStatus',
 'ASICEntityType',
 'ASICEntityClass',
 'ABNStatus',
 'ABN_StatusFromDate',
 'GST_Status',
 'GST_StatusFromDate',
 'RegistrationOrIncorporationDate',
 'EntityAge',
 'EmployeeIndicator',
 'RevenueIndicator',
 'HQ_ID',
 'HQ_CompanyName',
 'NumberofMembersinHierarchy',
 'ImmediateParentDUNS',
 'ImmediateParentName',
 'ImmediateParentCountry',
 'DomesticParentDUNS',
 'DomesticParentName',
 'DomesticParentCountry',
 'GlobalUltimateParentDUNS',
 'GlobalUltimateParentName',
 'GlobalUltimateParentCountry',
 'PrimarySICDesc',
 'PrimarySIC3Digit',
 'PrimarySIC3DigitDesc',
 'PrimarySIC2Digit',
 'PrimarySIC2DigitDesc',
 'PrimarySICDivision',
 'PrimarySICDivisionDesc',
 'SubHideReasonID',
 'ANZSICCode',
 'TradingNames',
 'CleanedAddress',
 'FloorNumber',
 'MarketableFlag',
 'OriginalAddress',
 'StreetName',
 'StreetNo',
 'StreetNoMax',
 'StreetNoMin',
 'StreetNoSet',
 'Suffix',
 'SuiteNumber',
 'ShopNumber',
 'SuiteNumberDisplay',
 'UnitNumber',
 'transform_commit',
 'transform_created_date',
 'transform_username',
 'TSI_Bucket',
 'TSI_State',
 'TSI_Country',
 'DisplayName', 
 'CompanyID',
 'ZI_C_RELEASE_DATE',
 'ZI_C_LAST_UPDATED_DATE',
 'AddressID',
 'address_match_score',
 'address_match_type',
 'al_property_id',
 'verify_commit',
 'verify_created_date',
 'verify_username',
'GNAFAddressDetailPID',
'MasterPropertyID',
'SuffixAbbr',
'ZI_C_TIER_GRADE',
'GoogleMapsOperationalStatus',
'TSI_Ingested_Date'   
 ]

#%%
COLUMNS = sorted(COLUMNS)
#%%
len(COLUMNS)
#%%
state='WA'
#%%
il_df = pd.read_parquet(f'/home/<USER>/Downloads/{state}/orphan/beta_data_illion.parquet')
#%%
# il_df = il_df[il_df['PropertyID'].isna()]
#%%
il_df['City'].isna().sum()
#%%
il_df.shape
#%%
il_df.sort_index(axis=1).info()
#%%
il_df['ID'].nunique()
#%%
il_df['StateAbbr'].unique()
#%%
il_df[il_df.duplicated(['ID'], keep=False)].shape
#%%
il_df.rename(columns={'ID':'VendorID'},inplace=True)
#%%
il_df['CountryCode'] = 14
il_df['IsProcessed'] = 0
il_df['Provider'] = 'Illion'
il_df['ProviderID'] = 6
il_df['TSI_Bucket'] = 'Orphan'
il_df['TSI_State']= state
il_df['TSI_Country'] = 'Australia'
il_df['TSI_Ingested_Date'] = date.today()
#%%
list(set(COLUMNS) - set(il_df))
#%%
set(il_df) - set(COLUMNS)
#%%
ABN_ENTITY_TYPES = ['TRT','NPF','DES','STR','HYT','CUT','SMF','FXT','CMT','SAF','FUT','NRF','PTT','FPT','DIT','PST','DTT','POF','PQT','DST']
#%%
il_df['LegalStatus'].unique()
#%%
il_df['LegalStatus'] = il_df['LegalStatus'].str.strip().str.upper()
#%%
il_df[il_df['LegalStatus'].isin(ABN_ENTITY_TYPES)].shape
#%%
DROP_COLS = [
    "VendorID",
    "TenantName",
    "Address1",
    'Latitude',
    'Longitude'
]
#%%
 il_df = il_df.dropna(
        subset=DROP_COLS,
        how="any",
    )
#%%
il_df.shape
#%%
# il_df.loc[df['ABNStatus'].str.lower().eq('act') | il_df['ASICEntityStatus'].str.lower().eq('regd')]
#%%
il_df['ABNStatus'].unique()
#%%
il_df['ASICEntityStatus'].unique()
#%%
il_df = il_df.loc[il_df['ABNStatus'].str.lower().eq('act') | il_df['ASICEntityStatus'].str.lower().eq('regd')]

#%%
il_df.shape
#%%
il_df.head()
#%%
il_df[list(set(COLUMNS) - set(il_df))] = np.nan
#%%
il_stats = stats_gen('Illion',il_df.sort_index(axis=1))

#%%
il_stats.head()
#%%
il_stats.to_csv(f'./stats/{state}/il_stats.csv', index=False)
#%% md
## zoominfo
#%%
zoominfo_df_1 = pd.read_parquet(f'/home/<USER>/Downloads/{state}/orphan/beta_data_zoominfo.parquet')
#%%
zoominfo_df_1 .shape
#%%
zoominfo_df_1['ID'].nunique()
#%%
zoominfo_gl_df = pd.read_parquet(f'/home/<USER>/Downloads/{state}/orphan/beta_data_zoominfo_google_addresses.parquet')
#%%
zoominfo_gl_df.shape
#%%
zoominfo_gl_df[zoominfo_gl_df['ID'].isin(zoominfo_df_1['ID'])].shape
#%%
zoominfo_gl_df['ID'].nunique()
#%%
zoominfo_df_1 = zoominfo_df_1[~zoominfo_df_1['ID'].isin(zoominfo_gl_df['ID'])]
#%%
zoominfo_df_1.shape
#%%
zoominfo_df = pd.concat([zoominfo_df_1,zoominfo_gl_df])
#%%
zoominfo_df['City'].isna().sum()
#%%
zoominfo_df[zoominfo_df['ID'] == '**********'][['ID','City']]
#%%
zoominfo_df_1.shape[0] + zoominfo_gl_df.shape[0]
#%%
# zoominfo_df = zoominfo_df[zoominfo_df['PropertyID'].isna()]
#%%
zoominfo_df.info()
#%%
zoominfo_df['ID'].nunique()
#%%
zoominfo_df.head()
#%%
zoominfo_df['State'].unique()
#%%
zoominfo_df['IsProcessed'] = 0
zoominfo_df['Provider'] = 'zoominfo'
zoominfo_df['ProviderID'] = 15
zoominfo_df['CountryCode'] = 14
zoominfo_df['TSI_Bucket'] = 'Orphan'
zoominfo_df['TSI_State']= state
zoominfo_df['TSI_Country'] = 'Australia'
zoominfo_df['TSI_Ingested_Date'] = date.today()
#%%
zoominfo_df.rename(columns={'ID':'VendorID'},inplace=True)
#%%
zoominfo_df[zoominfo_df.duplicated('VendorID', )].shape
#%%
list(set(COLUMNS) - set(zoominfo_df))
#%%
zoominfo_df[list(set(COLUMNS) - set(zoominfo_df))] = np.nan
#%%
list(set(zoominfo_df) - set(COLUMNS))
#%%
zoominfo_df['Longitude'].notna().sum()
#%%
DROP_COLS = [
    "VendorID",
    "TenantName",
    "Address1",
    'Latitude',
    'Longitude'
]
#%%
(zoominfo_df['TenantName'].isna()).sum()
#%%
(zoominfo_df['Longitude'].notna()).sum()
#%%
(zoominfo_df['Address1'].notna()).sum()
#%%
(zoominfo_df['VendorID'].notna()).sum()
#%%
zoominfo_df[(zoominfo_df['TenantName'].isna()) | (zoominfo_df['Longitude'].notna()) | (zoominfo_df['Address1'].notna()) | (zoominfo_df['VendorID'].notna())].shape
#%%
 zoominfo_df = zoominfo_df.dropna(
        subset=DROP_COLS,
        how="any",
    )
#%%
zoominfo_df[COLUMNS].info()
#%%
zoominfo_stats = stats_gen('Zoominfo',zoominfo_df.sort_index(axis=1))
zoominfo_stats.to_csv(f'./stats/{state}/zoominfo_stats.csv', index=False)
#%% md
## GOOGLE
#%%
google_df = pd.read_parquet(f'/home/<USER>/Downloads/{state}/orphan/beta_data_google_maps_usearch.parquet')
#%%
# google_df = google_df[google_df['PropertyID'].isna()]
#%%
google_df.head()
#%%
google_df.shape
#%%
google_df.info()
#%%
google_df['ID'].nunique()
#%%
google_df['State'].unique()
#%%
google_df.rename(columns={'ID':'VendorID'},inplace=True)
#%%
google_df['CountryCode'] = 14
google_df['IsProcessed'] = 0
google_df['Provider'] = 'Google_Maps'
google_df['ProviderID'] = 18
google_df['TSI_Bucket'] = 'Orphan'
google_df['TSI_State']= state
google_df['TSI_Country'] = 'Australia'
google_df['TSI_Ingested_Date'] = date.today()
#%%
list(set(COLUMNS) - set(google_df))
#%%
import numpy as np
google_df[list(set(COLUMNS) - set(google_df))] = np.nan
#%%
google_df[google_df['VendorID'].isna()].shape
#%%
google_df[(google_df['Address1'].isna()) | (google_df['TenantName'].isna())].shape
#%%
set(google_df) - set(COLUMNS)
#%%
DROP_COLS = [
    "VendorID",
    "TenantName",
    "Address1"
]
#%%
 google_df = google_df.dropna(
        subset=DROP_COLS,
        how="any",
    )
#%%
google_df.shape
#%%
google_stats = stats_gen('Google',google_df.sort_index(axis=1))
google_stats.to_csv(f'./stats/{state}/google_stats.csv', index=False)
#%% md
## company Websites
#%%
company_df=pd.read_parquet(f'/home/<USER>/Downloads/{state}/orphan/beta_data_company_websites.parquet')
#%%
# company_df = company_df[company_df['PropertyID'].isna()]
#%%
company_df.shape
#%%
company_df.head()
#%%
company_df['ID'].nunique()
#%%
company_df['State'].unique()
#%%
company_df.rename(columns={'ID':'VendorID'},inplace=True)
#%%
company_df.info()
#%%
company_df['StateAbbr'].unique()
#%%
company_df['ProviderID']=19
company_df['Provider'] = 'company_websites'
company_df['TSI_Bucket'] = 'Orphan'
company_df['TSI_State']= state
company_df['TSI_Country'] = 'Australia'
company_df['CountryCode'] = 14
company_df['TSI_Ingested_Date'] = date.today()
#%%
(list(set(COLUMNS) - set(company_df)))
#%%
import numpy as np
company_df[list(set(COLUMNS) - set(company_df))] = np.nan
#%%
set(company_df) - set(COLUMNS)
#%%
company_df['VendorID'].isna().sum()
#%%
DROP_COLS = [
    "VendorID",
    "TenantName",
    "Address1"
]
#%%
 company_df = company_df.dropna(
        subset=DROP_COLS,
        how="any",
    )
#%%
company_df.info()
#%%
web_stats = stats_gen('Website',company_df.sort_index(axis=1))
web_stats.to_csv(f'./stats/{state}/website_stats.csv', index=False)
#%%
web_stats.tail()
#%%
il_df[COLUMNS].shape[0] + google_df[COLUMNS].shape[0] + zoominfo_df[COLUMNS].shape[0] + company_df[COLUMNS].shape[0]
#%%
concatenated_tenant_df = pd.concat([il_df[COLUMNS],
google_df[COLUMNS],
zoominfo_df[COLUMNS],
company_df[COLUMNS]])
#%%
concatenated_tenant_df['StateAbbr'] = concatenated_tenant_df['StateAbbr'].str.upper()
#%%
concatenated_tenant_df.shape
#%%
concatenated_tenant_df['TSI_Ingested_Date'].notna().sum()
#%%
stats = stats_gen('complete',concatenated_tenant_df.sort_index(axis=1))
#%%
stats.to_csv(f'./stats/{state}/all_providers_stats.csv', index=False)
#%%
concatenated_tenant_df.to_csv(f'beta_{state}_orphans_new.csv', index=False)
#%%
# Ingesting the Data to MYSQL
# Import create_engine to create and manage database connection pool
from sqlalchemy import create_engine


username = 'xxxxxxxxxx'
password = 'xxxxxxxxxx'
host = 'localhost'
port = '4339'
database_name = 'Empirical_DataStage' # Change the database_name if required

# Connection String
connection_string = f'mysql+mysqlconnector://{username}:{password}@{host}:{port}/{database_name}'
#connection_string = f'mysql+mysqlconnector://{db_user}:{db_password}@{db_host}/{db_name}'
# Create the SQLAlchemy engine
database_engine = create_engine(connection_string)
#%%
company_df[COLUMNS].to_sql('BT_Orphan_Tenants_2024_05_20', database_engine, if_exists='append', index=False, chunksize=10000)
#%%
2362824 - 