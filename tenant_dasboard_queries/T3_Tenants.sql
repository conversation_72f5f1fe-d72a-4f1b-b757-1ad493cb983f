SELECT 
    TS.BranchID,
    TRIM(TS.TenantName),
    NULL AS ConfirmedTenantID,
    TS.Tenant_Stage_Id,
    PA.level_number AS FloorNumber,
    -- Coalesce(CASE WHEN TRIM(TS.Address2) = '' THEN NULL ELSE TS.Address2 END, PA.flat_number) AS Address2,
    COALESCE(PA.flat_number, CASE WHEN TRIM(TS.Address2) = '' THEN NULL ELSE TS.Address2 END) AS Address2,
    -- PA.flat_number AS Address2,
    TRIM(TS.OfficePhone),
    TRIM(TS.CEOName),
    TRIM(ANZG.Group),
    CONCAT('$', TS.Revenue),
    TS.Revenue,
    (TS.ANZSICCode),
    TS.EmployeeCount,
    IFNULL(TS.IsHidden, 0) AS IsHidden,
    0 AS HasRegisteredLease,
    0 AS TenantVerified,
    CASE 
        -- If Tenant stage record is Illion source, it comes under Tenant tab
        WHEN EXISTS (SELECT 1 
                     FROM `ar-sandbox-data-lakehouse.ar_rep_Empirical_DataStage.Tenants_Stage` 
                     WHERE Tenant_Stage_ID = TS.Tenant_Stage_ID 
                       -- and PropertyID = P_PropertyID
                       AND ProviderID = 6) THEN 'Tenant'
        ELSE 'Possible Tenants'
    END AS TenantCategory
FROM 
    `ar-sandbox-data-lakehouse.ar_rep_Empirical_DataStage.Tenants_Stage` TS 
    INNER JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.Country` CTRY ON CAST(CTRY.CountryID AS STRING) = CAST(TS.CountryCode AS STRING)  
    INNER JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.State` S ON S.StateAbbr = TS.StateAbbr AND S.CountryID = CTRY.CountryID
    INNER JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.City` C ON C.CityName = TS.City AND C.StateID = S.StateID
    LEFT JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_DataStage.Tenants_Stage_ParsedAddress` PA ON PA.Tenant_Stage_ID = TS.Tenant_Stage_ID
    LEFT JOIN  `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.ANZSICCodes` ANZC ON ANZC.Code = TS.ANZSICCode
    LEFT JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.ANZSICGroupCodes` ANZG ON ANZG.GroupID = ANZC.GroupID
    LEFT JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.Company` cmpny ON cmpny.CompanyID = TS.BranchID AND cmpny.IsActive = 1
    LEFT JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Tenants.ConfirmedTenants` ct ON ct.CompanyID = TS.BranchID
WHERE 
     (
        -- Tenant Stage record without BranchID comes under T3
        TS.BranchID IS NULL 
        OR (
            TS.BranchID IS NOT NULL AND ct.ConfirmedTenantID IS NULL
            AND NOT EXISTS (
                -- If there is no active relationship with another company
                SELECT 1 
                FROM `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.CompanyRelationship` CR
                LEFT JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.CompanyRelationship` OldCRP ON OldCRP.CompanyRelationshipID = CR.CompanyRelationshipID
                                                     AND OldCRP.IsActive = 1
                LEFT JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.Company`  OldCompany ON OldCompany.CompanyID = OldCRP.ParentCompanyID
                                               AND OldCRP.IsActive = 1
                WHERE CR.ChildCompanyID = TS.BranchID
            )
        )
        OR (
            -- Inactive confirmed Tenant records
            TS.BranchID IS NOT NULL 
            AND ct.ConfirmedTenantID IS NOT NULL 
            AND ct.IsActive = 0
            AND NOT EXISTS (
                SELECT 1 
                FROM `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.CompanyRelationship` 
                WHERE ChildCompanyID = TS.BranchID AND ParentCompanyID <> TS.BranchID
            )
        )
    )
    -- To avoid duplicate Tenant Stage records to any other tenant stage records from the same property
    AND NOT EXISTS (
        SELECT 1 
        FROM `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.TSARelationship` 
        WHERE TargetTenantStageID = TS.Tenant_Stage_ID AND IsActive = '1'
    )
    -- Avoid coworking tenant stage records from being associated with other tenant stage records or t1 tenant records
    AND NOT EXISTS (
        SELECT 1 
        FROM `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.OtherRelationship` 
        WHERE (TargetTenantStageID = TS.Tenant_Stage_ID AND IsActive = 1)
           OR (TargetBranchID = TS.BranchID AND IsActive = 1 AND c.IsActive = 1 AND TS.BranchID IS NOT NULL)
    )
    AND IFNULL(TS.IsHidden, 0) = 0  -- Exclude hidden tenant stage records
    AND IFNULL(TS.IsDeleted, 0) = 0  -- Exclude deleted tenant stage records
    AND (
        TS.ProviderID IN (18, 19, 40)  -- Exclude records with specific providers
        OR (
            TS.ProviderID = 6 
            AND TS.NationalID IS NOT NULL 
            AND (TS.EmployeeCount > 0 OR TS.Revenue > 0 OR (TS.SICCode IS NOT NULL AND TRIM(TS.SICCode) != ''))
            AND LENGTH(TRIM(TS.LegalStatus)) > 0
            AND TS.LegalStatus NOT IN ('TRT', 'NPF', 'DES', 'STR', 'HYT', 'CUT', 'SMF', 'FXT', 'CMT', 'SAF', 'FUT', 'NRF', 'PTT', 'FPT', 'DIT', 'PST', 'DTT', 'POF', 'PQT', 'DST')
            AND TS.LineOfBusiness NOT IN ('ATM', 'Trust', 'Trustee', 'Superfund', 'Superannuation fund')
        )
    )
