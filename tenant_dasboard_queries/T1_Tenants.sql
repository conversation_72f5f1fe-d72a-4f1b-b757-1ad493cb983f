SELECT DISTINCT
    CTM.CompanyID AS BranchID,
    CTM.TenantName AS TenantName,
    CTM.ConfirmedTenantID,
    NULL AS Tenant_Stage_ID,
    CTM.FloorNumber AS FloorNumber,
    CTM.Address2 AS Address2,
    CTM.OfficePhone AS OfficePhone,
    CTM.CEOName AS CEOName,
    ANZG.Group AS ANZSICGroup,
    CTM.Revenue AS Revenue,
    CTM.ANZSICCode AS ANZSICCode,
    CTM.EmployeeCount AS EmployeeCount,
    TS.ProviderID AS ProviderID,
    TS.BranchID AS BranchID,
    TS.CreatedDate AS CreatedDate,
    TS.ModifiedDate AS ModifiedDate,
    TS.IsHidden AS Ishidden_ts,
    TS.IsDeleted AS IsDeleted,
    TS.Address1 AS Address,
    TS.PropertyID AS PropertyID,
    0 AS IsHidden, 
    
    CASE 
        WHEN EXISTS (
            SELECT 1 
            FROM `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.TenantVerification` 
            WHERE BranchID = CT.CompanyID 
            AND VerificationSourceID IN (14) 
            AND IsActive = 1
        ) THEN 'Arealytics Confirmed Tenant' 
        WHEN IFNULL(TV.VerificationSourceID, 0) = 0 
            OR (NOT EXISTS (
                SELECT 1 
                FROM `ar-sandbox-data-lakehouse.ar_rep_Empirical_DataStage.Tenants_Stage`
                WHERE BranchID = CT.CompanyID 
                AND ProviderID = 6
            )) THEN 
            CASE 
                WHEN IFNULL(TS.Tenant_Stage_Id, 0) = 0 
                THEN 'Arealytics Sourced'
                ELSE 'Multi-Sourced'
            END
        ELSE 'Arealytics Confirmed Tenant'
    END AS TenantVerified,

    CASE 

        WHEN EXISTS (
            SELECT 1 
            FROM `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.TenantVerification` 
            WHERE BranchID = CT.CompanyID 
            AND VerificationSourceID IN (5, 11, 13, 14, 10) 
            AND IsActive = 1
        ) THEN 'Tenant'

        WHEN EXISTS (
            SELECT 1 
            FROM `ar-sandbox-data-lakehouse.ar_rep_Empirical_DataStage.Tenants_Stage`
            WHERE BranchID = CT.CompanyID 
            AND ProviderID = 6
        ) THEN 'Tenant'

        WHEN EXISTS (
            SELECT 1 
            FROM `ar-sandbox-data-lakehouse.ar_rep_Empirical_DataStage.Tenants_Stage`
            WHERE BranchID = CT.CompanyID 
            AND ProviderID IN (6, 18, 19, 40)
        ) THEN 'Possible Tenants'

        WHEN EXISTS (
            SELECT 1 
            FROM `ar-sandbox-data-lakehouse.ar_rep_Empirical_DataStage.Tenants_Stage`
            WHERE BranchID = CT.CompanyID 
            AND ProviderID IN (15, 16)
        ) THEN 'ZIOT_Suppressed_rule'

        WHEN NOT EXISTS (
            SELECT 1 
            FROM `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.TenantVerification`  
            WHERE BranchID = CT.CompanyID 
            AND VerificationSourceID IN (5, 11, 13, 14, 10) 
            AND IsActive = 1
        ) THEN 'Verification_Suppressed_rule'

        ELSE 'Possible Tenants'
    END AS TenantCategory
FROM 
    `ar-sandbox-data-lakehouse.ar_rep_Empirical_Tenants.ConfirmedTenants` CT  
    JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.Company` B ON B.CompanyID = CT.CompanyID
    JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Tenants.ConfirmedTenants` CTM ON CTM.CompanyID = CT.CompanyID 
        AND CTM.ProviderID = 5 
    JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.SuiteTenant` ST ON ST.ConfirmedTenantID = CT.ConfirmedTenantID 
        AND ST.IsActive = 1
    JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.CompanyRelationship` CR ON CR.ChildCompanyID = CT.CompanyID 
        AND IFNULL(CR.IsActive, 1) = 1
    LEFT JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.ANZSICCodes` ANZC ON ANZC.Code = CTM.ANZSICCode
    LEFT JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.ANZSICGroupCodes` ANZG ON ANZG.GroupID = ANZC.GroupID
    LEFT JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_Prod.TenantVerification` AS TV ON TV.BranchID = CT.CompanyID 
        AND TV.VerificationSourceID IN (5, 11, 13, 14, 10) 
        AND TV.IsActive = 1 
    LEFT JOIN `ar-sandbox-data-lakehouse.ar_rep_Empirical_DataStage.Tenants_Stage` AS TS ON TS.BranchID = CT.CompanyID  
        AND TS.ProviderID IN (6, 15, 16, 17, 18, 19, 40)
WHERE
    CT.ProviderID = 1
    AND IFNULL(ST.TenantStatusID, 1) = 1
    AND IFNULL(B.IsHidden, 0) = 0 
    AND IFNULL(B.IsActive, 1) = 1
    AND IFNULL(CT.IsActive, 1) = 1;
