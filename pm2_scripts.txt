import csv

def convert_csv(input_file, output_file):
    with open(input_file, 'r', newline='') as infile:
        reader = csv.reader(infile)
        data = list(reader)

    # Replace commas with semicolons
    for row in data:
        for i in range(len(row)):
            row[i] = row[i].replace(',', ';')

    with open(output_file, 'w', newline='') as outfile:
        writer = csv.writer(outfile, delimiter=';')
        writer.writerows(data)

# Example usage:
input_filename = 'input.csv'
output_filename = 'output.csv'

convert_csv(input_filename, output_filename)

