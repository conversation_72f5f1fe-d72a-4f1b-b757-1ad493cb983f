#%%
import pandas as pd
import numpy as np
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
from pandarallel import pandarallel
# Avoid scientific notation
pd.set_option('display.float_format', '{:.10f}'.format)
pandarallel.initialize(progress_bar=True,nb_workers=12)
from sqlalchemy import create_engine
import yaml
#%%
state = 'QLD'
#%%
confirmed_COLUMNS =['ABN',
 'ACN',
 'ANZSICCode',
 'Address1',
 'Address2',
 'CEOName',
 'CEOTitle',
 'CityID',
 'CompanyID',
 'ConfirmedTenantID',
 'CountryCode',
 'CreatedBy',
 'CreatedDate',
 'Email',
 'EmployeeCount',
 'EmployeesAtLocation',
 'Fax',
 'FloorNumber',
 'LegalStatus',
 'LineOfBusiness',
 'NACECode',
 'NAICSCode',
 'NationalID',
 'OfficePhone',
 'PrimarySIC2DigitDesc',
 'PrimarySIC3DigitDesc',
 'PrimarySICDivisionDesc',
 'PropertyID',
 'ProviderID',
 'RegistrationOrIncorporationDate',
 'Revenue',
 'RevenueIndicator',
 'SICCode',
 'StateID',
 'StatusCode',
 'SubsidiaryCode',
 'TenantName',
 'VendorID',
 'WebsiteURL',
 'ZipCode']

#%%
# Load configuration from config.yaml
with open('./config.yaml', 'r') as f:
    config = yaml.safe_load(f)
#%%
# Extract database connection details
source_db_config = config['source_db']
source_db_config
#%%
# Connection String
source_connection_string = f"mysql+mysqlconnector://{source_db_config['username']}:{source_db_config['password']}@{source_db_config['host']}:{source_db_config['port']}/{source_db_config['database_name']}"
source_database_engine = create_engine(source_connection_string)
#%%
CT_query = '''SELECT * FROM Tenant_Roster_CT_match_2024_07_04'''
#%%
ct_tenants_df = pd.read_sql(CT_query,con=source_database_engine)
#%%
ct_tenants_df.rename(columns={'WebsiteUrl':'WebsiteURL','CountryID':'CountryCode','ExtVendorID':'VendorID'}, inplace=True)
#%%
# ct_tenants_df = ct_tenants_df[confirmed_COLUMNS]
#%%
ct_tenants_df.shape
#%%
ct_tenants_df['ConfirmedTenantID'].nunique()
#%%
ct_tenants_df[ct_tenants_df['CompanyID'].duplicated()].shape
#%%
ct_tenants_df.sort_index(axis=1).info()
#%%
ct_tenants_df.sort_index(axis=1).columns.to_list()
#%%
company_id_tuple = tuple(ct_tenants_df['CompanyID'].unique().tolist())
#%%
MT_query = f'''
select * from Empirical_DataStage.Tenants_Stage ts 
left join Empirical_Prod.NationalIdentifiers ni on ni.Tenant_Stage_Id =ts.Tenant_Stage_ID  
where ts.BranchID in {company_id_tuple}'''
#%%
tenant_stage_df = pd.read_sql(MT_query, con=source_database_engine)
#%%
tenant_stage_df.shape
#%%
ct_tenants_df.head()
#%%
tenant_stage_df.head()
#%%

#%%
tenant_stage_df[tenant_stage_df['ProviderID'] == 14].shape
#%%
tenant_stage_req_df = tenant_stage_df[tenant_stage_df['ProviderID'] != 14]
#%%
tenant_stage_req_df.info()
#%%
tenant_stage_req_df.sort_index(axis=1).head()
#%%
tenant_stage_req_df.rename(columns={'Tenant_Stage_Id':'Tenant_Stage_ID'},inplace=True)
#%%
tenant_stage_req_df.shape
#%%
list(set(ct_tenants_df.columns)-set(confirmed_COLUMNS))
#%%
priority_dict = {
    "ABNStatus": [40, 18, 6, 19, 15, 16],
    "ABN_StatusFromDate": [40, 18, 6, 19, 15, 16],
    "ANZSICCode": [40, 18, 6, 19, 15, 16],
    "ASICEntityClass": [40, 18, 6, 19, 15, 16],
    "ASICEntityStatus": [40, 18, 6, 19, 15, 16],
    "ASICEntityType": [40, 18, 6, 19, 15, 16],
    "Address1": [40, 18, 6, 19, 15, 16],
    "Address2": [40, 18, 6, 19, 15, 16],
    "CEOName": [40, 18, 6, 19, 15, 16],
    "CEOTitle": [40, 18, 6, 19, 15, 16],
    "DomesticParentCountry": [40, 18, 6, 19, 15, 16],
    "DomesticParentDUNS": [40, 18, 6, 19, 15, 16],
    "DomesticParentName": [40, 18, 6, 19, 15, 16],
    "Email": [40, 18, 6, 19, 15, 16],
    "EmployeeCount": [40, 18, 6, 19, 15, 16],
    "EmployeeIndicator": [40, 18, 6, 19, 15, 16],
    "EmployeesAtLocation": [40, 18, 6, 19, 15, 16],
    "EntityAge": [40, 18, 6, 19, 15, 16],
    "Fax": [40, 18, 6, 19, 15, 16],
    "FloorNumber": [40, 18, 6, 19, 15, 16],
    "GST_Status": [40, 18, 6, 19, 15, 16],
    "GST_StatusFromDate": [40, 18, 6, 19, 15, 16],
    "GlobalUltimateParentCountry": [40, 18, 6, 19, 15, 16],
    "GlobalUltimateParentDUNS": [40, 18, 6, 19, 15, 16],
    "GlobalUltimateParentName": [40, 18, 6, 19, 15, 16],
    "HQ_CompanyName": [40, 18, 6, 19, 15, 16],
    "HQ_ID": [40, 18, 6, 19, 15, 16],
    "ImmediateParentCountry": [40, 18, 6, 19, 15, 16],
    "ImmediateParentDUNS": [40, 18, 6, 19, 15, 16],
    "ImmediateParentName": [40, 18, 6, 19, 15, 16],
    "LegalStatus": [40, 18, 6, 19, 15, 16],
    "LineOfBusiness": [40, 18, 6, 19, 15, 16],
    "MarketableFlag": [40, 18, 6, 19, 15, 16],
    "NumberofMembersinHierarchy": [40, 18, 6, 19, 15, 16],
    "OfficePhone": [40, 18, 6, 19, 15, 16],
    "PostalCode": [40, 18, 6, 19, 15, 16],
    "PrimarySIC2Digit": [40, 18, 6, 19, 15, 16],
    "PrimarySIC2DigitDesc": [40, 18, 6, 19, 15, 16],
    "PrimarySIC3Digit": [40, 18, 6, 19, 15, 16],
    "PrimarySIC3DigitDesc": [40, 18, 6, 19, 15, 16],
    "PrimarySICDesc": [40, 18, 6, 19, 15, 16],
    "PrimarySICDivision": [40, 18, 6, 19, 15, 16],
    "PrimarySICDivisionDesc": [40, 18, 6, 19, 15, 16],
    "RegistrationOrIncorporationDate": [40, 18, 6, 19, 15, 16],
    "Revenue": [40, 18, 6, 19, 15, 16],
    "RevenueIndicator": [40, 18, 6, 19, 15, 16],
    "SICCode": [40, 18, 6, 19, 15, 16],
    "State": [40, 18, 6, 19, 15, 16],
    "SubsidiaryCode": [40, 18, 6, 19, 15, 16],
    "TenantName": [40, 18, 6, 19, 15, 16],
    "WebsiteURL": [40, 18, 6, 19, 15, 16],
    'NACECode': [40, 18, 6, 19, 15, 16],
    'NAICSCode':[40, 18, 6, 19, 15, 16],
    "VendorID": [6, 15, 18, 19,40,16]  
}

#%%
set(list(priority_dict.keys())) - set(confirmed_COLUMNS) 
#%%
set(confirmed_COLUMNS) - set(list(priority_dict.keys()))
#%%
tenant_stage_req_df[tenant_stage_req_df['BranchID'].isin(ct_tenants_df['CompanyID'])].shape
#%%
tenant_stage_req_df.shape
#%%
ct_tenants_df.shape
#%%
tenant_stage_req_df.head()
#%%
confirmed_COLUMNS_reference=[]
for col in confirmed_COLUMNS:
    confirmed_COLUMNS_reference.append(f"{col}_reference")
#%%
for column in confirmed_COLUMNS_reference:
    ct_tenants_df[column] = np.nan
#%%
tenant_stage_req_df.head()
#%%
# tenant_stage_req_df.set_index('BranchID',inplace=True)
#%%
tenant_stage_req_df.sort_index(axis=1).head()
#%%
non_priority_columns=['ConfirmedTenantID','PropertyID','CompanyID']
#%%
intersection_columns = list(set(ct_tenants_df.columns).intersection(set(tenant_stage_req_df.columns)))
sorted(intersection_columns)
#%%
set(intersection_columns) - set(list(priority_dict.keys()))
#%%
priority_columns = list(filter(lambda x:x not in non_priority_columns,intersection_columns))
priority_columns
#%%
%%time
import ast
def populate_values(row):
    try:
        tenant_stage_req_df_remain = tenant_stage_req_df[tenant_stage_req_df['BranchID']==row['CompanyID']][priority_columns]
        for column in priority_columns:
            updated=0
            for Provider in priority_dict.get(column, []):
                tenant = tenant_stage_req_df_remain[tenant_stage_req_df_remain['ProviderID'] == Provider]
                if not tenant.empty:
                    for idxs,tenant_row in tenant.iterrows():
                        if pd.notna(tenant_row[column]):
                            row[column] = tenant_row[column]
                            row[f"{column}_reference"] = Provider
                            updated=1
                            break  
                if updated:
                    break 
            if updated == 0:
                row[column] = np.nan
    except KeyError as e:
        print(f"KeyError processing row: {row}, Error: {e}")
    except Exception as e:
        print(f"Unexpected error processing row: {row}, Error: {e}")
    return row

ct_tenants_req_df = ct_tenants_df.parallel_apply(populate_values, axis=1)
#%%
%%time
import ast
def populate_values(row):
    tenant_stage_req_df_remain = tenant_stage_req_df[tenant_stage_req_df['BranchID']==row['CompanyID']][priority_columns]
    for Provider in [6]:
        tenant = tenant_stage_req_df_remain[tenant_stage_req_df_remain['ProviderID'] == Provider]
        if not tenant.empty:
            for idxs,tenant_row in tenant.iterrows():
                if pd.notna(tenant_row['ACN']) or pd.notna(tenant_row['ABN']):
                    row[['ACN_reference','ABN_reference']] = np.nan
                    row['ACN'] = tenant_row['ACN']
                    row['ABN'] = tenant_row['ABN']
                    if pd.notna(row['ACN']):
                        row[f"ACN_reference"] = Provider
                    if pd.notna(row['ABN']):
                        row[f"ABN_reference"] = Provider
                    updated=1
                    break 
    return row

ct_tenants_req_df = ct_tenants_req_df.parallel_apply(populate_values, axis=1)
#%%
ct_tenants_req_df.head()
#%%
column
#%%
confirmed_tenant_table = ct_tenants_req_df[confirmed_COLUMNS]
#%%
confirmed_tenant_table.head()
#%%
ct_tenants_req_df.to_csv('./merged_view_PROD.csv',index=False)
#%%
# matched_tenants_table[matched_COLUMNS]
# reference_table[refrence_COLUMNS]
# confirmed_tenant_table[confirmed_COLUMNS]
# Raw_Matched_Tenants_2024_06_21
# Raw_Confirmed_Tenants_Fields_Reference_2024_06_21
# Raw_Confirmed_Tenants_2024_06_21
#%%
# confirmed_tenant_table[confirmed_COLUMNS].to_sql('Raw_Confirmed_Tenants_2024_06_21', destination_database_engine, if_exists='append', index=False, chunksize=10000)
#%%

#%%

#%%

#%%

#%%
