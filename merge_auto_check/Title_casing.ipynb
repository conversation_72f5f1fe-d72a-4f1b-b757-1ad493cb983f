#%%
import pandas as pd
from sqlalchemy import create_engine
import yaml
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
#%%
COLUMNS_TITLE = [
'TenantName',
'CEOName',
'CEO<PERSON>it<PERSON>',
'ABNStatus',
'Address1',
'Address2',
'City',
'LineOfBusiness',
'LegalStatus',
'EmployeeIndicator',
'PrimarySIC2DigitDesc',
'PrimarySIC3DigitDesc',
'PrimarySICDesc',
'PrimarySICDivisionDesc',
'RevenueIndicator',
'ASICEntityClass',
'ASICEntityStatus',
'ASICEntityType',
'GlobalUltimateParentName',
'GlobalUltimateParentCountry',
'ImmediateParentName',
'ImmediateParentCountry',
'DomesticParentCountry',
'DomesticParentName']
#%%
# Load configuration from config.yaml
with open('./config.yaml', 'r') as f:
    config = yaml.safe_load(f)
#%%
# Extract database connection details
source_db_config = config['source_db']
source_db_config
#%%
# Connection String
source_connection_string = f"mysql+mysqlconnector://{source_db_config['username']}:{source_db_config['password']}@{source_db_config['host']}:{source_db_config['port']}/{source_db_config['database_name']}"
source_database_engine = create_engine(source_connection_string)
#%%
sql_query = f"SELECT * FROM {source_db_config['table_name']}"
#%%
df = pd.read_sql(sql_query, con=source_database_engine)
#%%
df.info()
#%%
df.head()
#%%
'City' in COLUMNS_TITLE
#%%
# Apply title case to the specified columns
for column in COLUMNS_TITLE:
    df[column] = df[column].str.title()
#%%
# Extract database connection details
destination_db_config = config['destination_db']
destination_db_config
#%%
# Connection String
destination_connection_string = f"mysql+mysqlconnector://{destination_db_config['username']}:{destination_db_config['password']}@{destination_db_config['host']}:{destination_db_config['port']}/{destination_db_config['database_name']}"
destination_database_engine = create_engine(destination_connection_string)
#%%
df.to_sql(destination_db_config['table_name'], destination_database_engine, if_exists='append', index=False, chunksize=10000)
#%%
